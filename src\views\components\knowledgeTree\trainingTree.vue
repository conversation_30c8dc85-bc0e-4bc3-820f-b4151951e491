<template>
  <div class="subject-container" :style="sty">
    <div class="tree-box">
      <el-tree
        :data="options"
        :props="iswen?defaultPropsWen:defaultProps"
        class="custom-tree"
        node-key="chapterId"
        ref="treeRef"
        @node-click="setChapterId"
        :default-expand-all = "true"
      >

        <template #default="{ node, data }">
          <div v-if="!node.isLeaf" class="custom-node" :class="node.expanded?'border-left':''">
              <span class="tree-h1" >{{ node.label }}</span>
            <el-icon class="expand-icon">
                <ArrowDown v-if="node.expanded"></ArrowDown>
                <ArrowUp v-else></ArrowUp>
            </el-icon>
          </div>
          
          <div v-else class="custom-node isLeaf" :class="{'is-current1':isCurrent(node)}">
             <!-- :class="{'is-current1':isCurrent(node)}" -->
              <span>{{ node.label }}</span>
              <div v-if="data.task" class="task-indicator">
                <img src="@/assets/img/synchronous/task-badge.png" class="task-badge" />
              </div>
              <div >
                <!-- 根据段位和状态显示图标 -->
                <div v-if="data.level && node.label !== '单元测试'" class="level-icon-container">
                  <img :src="getLevelIconByStatus(data.level, data.status3)" :alt="getLevelName(data.level)" style="width: 80px;height: 30px;" />
                </div>
                
                <!-- 单元测试显示百分比 -->
                <div v-else-if="node.label=='单元测试'"  class="btn" :class="data.status3 == 1 ? 'green-bg' : data.status3 == 2 ? 'yellow-bg' : data.status3 == 3 ? 'red-bg' : 'gray-bg'">
                  {{ data.correctRate ? data.correctRate + '%' : '未测' }}
                </div>

                <!-- 其他情况显示状态按钮 -->
                <div v-else class="btn" :class="data.status3 == 1 ? 'green-bg' : data.status3 == 2 ? 'yellow-bg' : data.status3 == 3 ? 'red-bg' : 'gray-bg'"
                > {{ data.status3 == 0 ? '未测' : data.status3 == 1 ? '已过关' : '未过关' }} </div>
              </div>
              <!-- {{ data.level }} -->
          </div>

          
        </template>
      </el-tree>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { nextTick, ref, watch, computed } from 'vue'
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue'

// 定义树节点数据接口
interface TreeNodeData {
  id?: string
  chapterId?: string
  name?: string
  chapterName?: string
  status?: number
  correctRate?: number
  children?: TreeNodeData[]
  [key: string]: any
}

const props = defineProps({
  options: {
    type: Array as () => TreeNodeData[],
    default: () => [] as TreeNodeData[]
  },
  selected: {
    type: String,
    default: () => ""
  },
  selectName: {
    type: String,
    default: () => ""
  },
  sty: {
    type: String,
    default: () => ""
  },
  iswen: {
    type: Boolean,
    default: () => false
  },
  isStatus: {
    type: Boolean,
    default: () => true
  },
  isHistoryTask: {
    type: Boolean,
    default: () => false
  },
  showCurrentTaskIndicator: {
    type: Boolean,
    default: () => false
  },
  currentTaskImageUrl: {
    type: String,
    default: () => "@/assets/img/percision/current-task.png"
  }
})

// 判断是否显示任务图标
const showTaskBadge = computed(() => {
  return props.isHistoryTask
})

const isCurrent = (data: any) => {
  if (props.selectName) {
    const nodeLabel = data.label || ''
    const nodeName = data.chapterName || data.name || ''
    return props.selected === data.key && (props.selectName === nodeLabel || props.selectName === nodeName)
  } else {
    return props.selected === data.key
  }
}

// 判断是否是当前选中的节点
const isCurrentNode = (data: TreeNodeData) => {
  const nodeKey = props.iswen ? data.id : data.chapterId
  return nodeKey === props.selected
}

const treeRef = ref()
watch(() => [props.selected, props.selectName] as const, ([newId, newName]) => {
  if(newId) {
    nextTick(() => {
      treeRef.value.setCurrentKey(newId)
      
      // 如果有selectName，尝试找到精确匹配的节点
      let targetNode: TreeNodeData | null = null
      if (newName) {
        targetNode = findNodeByIdAndName(props.options, newId, newName)
      }
      
      // 如果没有找到精确匹配，使用ID查找
      if (!targetNode) {
        const targetData: TreeNodeData = props.iswen ? {id: newId} : {chapterId: newId}
        targetNode = targetData
      }
      
      if (targetNode) {
        const fullName = getFullName(props.options, targetNode)
        emit('setChapterName', fullName)
      }
      // 延迟滚动到选中节点，确保树完全渲染
      setTimeout(() => {
        scrollToSelected()
      }, 200)
    })
  }
},{ immediate: true })

// 在树数据中查找节点
const findNodeByIdAndName = (nodes: TreeNodeData[], targetId: string, targetName: string): TreeNodeData | null => {
  const searchNode = (nodeList: TreeNodeData[]): TreeNodeData | null => {
    for (const node of nodeList) {
      const nodeId = props.iswen ? node.id : node.chapterId
      const nodeName = node.chapterName || node.name || ''
      
      // 精确匹配ID和名称
      if (nodeId === targetId && nodeName === targetName) {
        return node
      }
      
      if (node.children && node.children.length > 0) {
        const found = searchNode(node.children)
        if (found) return found
      }
    }
    return null
  }
  
  return searchNode(nodes)
}
const emit = defineEmits(['setChapterId','setChapterName'])

// 获取段位名称
const getLevelName = (level: number | null) => {
  if (level === null || level === undefined) {
    return '未评级'
  }
  
  switch (level) {
    case 1:
      return '青铜'
    case 2:
      return '白银'
    case 3:
      return '黄金'
    case 4:
      return '钻石'
    default:
      return '未评级'
  }
}

// 根据段位和状态获取图标
const getLevelIconByStatus = (level: number, status3: number) => {
  // 如果status3为0（未测）或2（未过关），显示灰色图标
  if (status3 === 0 || status3 === 2) {
    switch (level) {
      case 1:
        return new URL('@/assets/img/percision/training/medal_gray_1.png', import.meta.url).href
      case 2:
        return new URL('@/assets/img/percision/training/medal_gray_2.png', import.meta.url).href
      case 3:
        return new URL('@/assets/img/percision/training/medal_gray_3.png', import.meta.url).href
      case 4:
        return new URL('@/assets/img/percision/training/medal_gray_4.png', import.meta.url).href
      default:
        return new URL('@/assets/img/percision/training/medal_gray_1.png', import.meta.url).href
    }
  }
  
  // 如果status3为1（已过关），显示彩色图标
  if (status3 === 1) {
    switch (level) {
      case 1:
        return new URL('@/assets/img/percision/training/medal_1.png', import.meta.url).href
      case 2:
        return new URL('@/assets/img/percision/training/medal_2.png', import.meta.url).href
      case 3:
        return new URL('@/assets/img/percision/training/medal_3.png', import.meta.url).href
      case 4:
        return new URL('@/assets/img/percision/training/medal_5.png', import.meta.url).href
      default:
        return new URL('@/assets/img/percision/training/medal_1.png', import.meta.url).href
    }
  }
  
  // 默认情况（其他状态），显示灰色图标
  switch (level) {
    case 1:
      return new URL('@/assets/img/percision/training/medal_gray_1.png', import.meta.url).href
    case 2:
      return new URL('@/assets/img/percision/training/medal_gray_2.png', import.meta.url).href
    case 3:
      return new URL('@/assets/img/percision/training/medal_gray_3.png', import.meta.url).href
    case 4:
      return new URL('@/assets/img/percision/training/medal_gray_4.png', import.meta.url).href
    default:
      return new URL('@/assets/img/percision/training/medal_gray_1.png', import.meta.url).href
  }
}
const defaultProps = {
	value: 'chapterId',
  label: 'chapterName',
  children: 'children'
}
const defaultPropsWen = {
	value: 'id',
  label: 'chapterName', // Changed from 'name' to 'chapterName'
  children: 'children'
}

// 滚动到选中的节点
const scrollToSelected = () => {
  nextTick(() => {
    // 多种方式查找当前选中的节点元素
    let selectedNode = document.querySelector('.el-tree-node.is-current1')
    
    // 如果没有找到，尝试查找带有is-current1类名的节点（备用）
    if (!selectedNode) {
      selectedNode = document.querySelector('.el-tree-node.is-current1')
    }
    
    // 如果还没有找到，尝试通过data-key属性查找
    if (!selectedNode && props.selected) {
      selectedNode = document.querySelector(`[data-key="${props.selected}"]`)
    }
    if (selectedNode) {
      // 获取树容器
      const treeContainer = document.querySelector('.tree-box')
      if (treeContainer) {
        // 计算滚动位置，使选中节点居中显示
        const containerRect = treeContainer.getBoundingClientRect()
        const nodeRect = selectedNode.getBoundingClientRect()

        // 计算节点相对于容器的位置
        const relativeTop = nodeRect.top - containerRect.top

        // 计算滚动位置，使节点居中显示
        const scrollPosition = relativeTop + treeContainer.scrollTop - (containerRect.height / 2) + (nodeRect.height / 2)

        // 平滑滚动到计算出的位置
        treeContainer.scrollTo({
          top: Math.max(0, scrollPosition),
          behavior: 'smooth'
        })
        
      } else {
        console.warn('⚠️ scrollToSelected - 未找到.tree-box容器')
      }
    } else {
      console.warn('⚠️ scrollToSelected - 未找到选中的节点')
    }
  })
}

// 暴露方法给父组件使用
defineExpose({
  scrollToSelected
})

const setChapterId = (data: TreeNodeData) => {
  if (!data.children || data.children.length == 0) {
    const fullName = getFullName(props.options, data)
    const nodeKey = props.iswen ? data.id : data.chapterId
    treeRef.value.setCurrentKey(nodeKey)
    emit('setChapterId', data, fullName)

    // 延迟滚动到选中节点，确保DOM完全更新
    setTimeout(() => {
      scrollToSelected()
    }, 150)
  }else{
    return false
  }
}
const getFullName = (options: TreeNodeData[], data: TreeNodeData) => {
  let arr: TreeNodeData[] = []
  const getName = (node: TreeNodeData[], target: TreeNodeData, currentArr: TreeNodeData[]) => {
    node.map((item: TreeNodeData) => {
      const itemKey = props.iswen ? item.id : item.chapterId
      const targetKey = props.iswen ? target.id : target.chapterId

      if(itemKey == targetKey) {
        arr = currentArr.slice()
      } else if (item.children && item.children.length > 0) {
        getName(item.children, target, currentArr.concat(item))
      }
    })
  }

  getName(options, data, [])
  let fullName = ""
  arr.map((item: any) => {
    const itemName = item.chapterName || item.name // Prefer chapterName, fall back to name if not available
    fullName += (itemName + "/")
  })

  const currentName = data.chapterName || data.name // Prefer chapterName, fall back to name if not available
  return fullName + (currentName || '')
}
</script>
<style scoped lang="scss">
.is-current1{
  color: #009c7f !important;
  font-weight: 600 !important;
  background: rgba(229, 249, 246, 1) !important;
  border-radius: 6px !important;
  padding: 2px 6px !important;
  transition: all 0.3s ease !important;
}
.tree-box {
    padding: .875rem;
    height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height) - 6.25rem);
    overflow-y: auto;
}
.custom-tree {
  color: #666666;
  font-size: 1rem;
  .title-h1 {
    color: #2a2b2a;
    background: #f5f5f5;
  }
  .border-left {
    border-left: .1875rem solid #00C9A3;
  }
  /* 隐藏默认图标 */
  :deep(.el-tree-node__expand-icon) {
    display: none;
  }
  :deep(.el-tree-node) {
    width: 100%;
    margin: .5rem 0;  /* 增加节点间距 */
    position: relative;

    & > .el-tree-node__content {
      height: 2.5625rem;
      line-height: 2.5625rem;
    }
    .btn {
      width: 4.625rem;
      height: 1.4375rem;
      line-height: 1.4375rem;
      text-align: center;
      color: #ffffff;
      font-size: .75rem;
      border-radius: 1.125rem;
    }
  }
  /* 自定义节点布局 */
  .custom-node {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0 .4375rem!important;
    box-sizing: border-box;
    span {
      display: inline-block;
      width: calc(100% - 5.3125rem);
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    // height: 2.5625rem;
    // line-height: 2.5625rem;
    // margin-bottom: 1.125rem;
  }

  /* 右侧展开图标 */
  .expand-icon {
    margin-left: .5rem;
    font-size: .875rem;
    color: #666;
  }
}
// 选中节点的背景色样式 - 确保优先级最高
:deep(.el-tree-node.is-current1 > .el-tree-node__content) {
  background: rgba(229, 249, 246, 1) !important;
  color: #009c7f !important;
  border-radius: 8px;
  margin: 2px 0;
}

// 确保所有层级的选中节点都有背景色
:deep(.el-tree .el-tree-node.is-current1 > .el-tree-node__content) {
  background: rgba(229, 249, 246, 1) !important;
  color: #009c7f !important;
  border-radius: 8px;
  font-weight: 600;
}

// 子节点选中样式

:deep(.el-tree-node__children .el-tree-node.is-current1 > .el-tree-node__content) {
  background: rgba(229, 249, 246, 1) !important;
  color: #009c7f !important;
  border-radius: 8px;
  font-weight: 600;
}

// 叶子节点选中样式
:deep(.custom-node.isLeaf .is-current1) {
  background: rgba(229, 249, 246, 1) !important;
  color: #009c7f !important;
  border-radius: 8px;
  padding: 4px 8px;
  font-weight: 600;
}

// 选中节点内的文本颜色
:deep(.el-tree-node.is-current1 .custom-node span) {
  color: #009c7f !important;
  font-weight: 600;
}

// 选中节点的按钮样式保持不变
:deep(.el-tree-node.is-current1 .btn) {
  opacity: 1;
}

// 悬停效果
:deep(.el-tree-node__content:hover) {
  background: rgba(229, 249, 246, 0.5) !important;
  border-radius: 8px;
  transition: all 0.3s ease;
}

// 确保选中状态覆盖悬停效果
:deep(.el-tree-node.is-current1 > .el-tree-node__content:hover) {
  background: rgba(229, 249, 246, 1) !important;
  color: #009c7f !important;
}
.subject-container {
  width: 23rem;
  height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height));
  border-radius: 1.25rem 1.25rem 0 0;
  background: #ffffff;
  overflow-y: auto;
  padding-top: 3.75rem;
  position: relative;
  box-sizing: border-box;
}
.green-bg {
  background: linear-gradient(166.7deg, #08d8b8 0%, #00b392 100%);
}
.yellow-bg {
  background: linear-gradient(150.8deg, #f6d22b 0%, #f29500 100%);
}
.red-bg {
  background: linear-gradient(150.8deg, #f07f4c 0%, #c95656 100%);
}
.gray-bg {
  background: #bbbbbb;
}
.task-indicator {
  position: absolute;
  top: -11px;
  left: 0;
  z-index: 2;
}

.task-badge {
  width: 34px;
  height: 22px;
}

.level-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  
  img {
    transition: all 0.3s ease;
    
    &:hover {
      transform: scale(1.05);
    }
  }
}
</style>
