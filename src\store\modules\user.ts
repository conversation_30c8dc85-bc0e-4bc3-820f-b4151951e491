import store from "@/store"
import { defineStore } from "pinia"
import router from "@/router"
// import router, { resetRouter } from "@/router"
import { removeToken } from "@/utils/cache/cookies"
import { ElMessage } from "element-plus"
import { subjectList } from '@/utils/user/enum'

export const useUserStore = defineStore("user", {
  state: () => ({
    token: localStorage.token || "",
    sysUserId: localStorage.sysUserId || "", //用户id
    userInfo: localStorage.userInfo || "", //用户信息
    phone: localStorage.phone || "", //账号
    password: localStorage.password || "", //密码
    learnNow: JSON.parse(localStorage.learnNow || "{}"),//学习用户-当前
    learnUsers: JSON.parse(localStorage.learnUsers || "[]") || "",//学习用户-列表
    memberInfo: JSON.parse(localStorage.memberInfo || "[]") || "",//会员信息
    redirect: "", //重定向url
    subjectList: JSON.parse(localStorage.subjectList || "[]") || "",//学科数据
    subjectObj: JSON.parse(localStorage.subjectObj || "{}"), //选中学科
    chapterList: JSON.parse(localStorage.chapterList || "[]"), //章节数据
    chapterObj: JSON.parse(localStorage.chapterObj || "{}"), //选中章节
    improvementObj: JSON.parse(localStorage.improvementObj || "{}"), //提高模式选中的知识点
    selectedChapterInfo_synchronous:JSON.parse(localStorage.selectedChapterInfo_synchronous || "{}"), //选中同步章节
    // orgName:JSON.parse(localStorage.orgName || "{}"|| ""),
  }),
  actions: {
    //设置用户信息缓存
    setData(data : any) {
      const userInfo : string = data.userInfo
      if (userInfo) {
        this.userInfo = userInfo
      }
    },
    setlearnNow(data : any) {
      try {
        if (data) {
          this.learnNow = data
          localStorage.setItem("learnNow", JSON.stringify(data)) 
          
          // 触发自定义事件，通知所有页面learnNow已更新
          const event = new CustomEvent('learnNowUpdated', { detail: data })
          window.dispatchEvent(event)
          
          // 检查 versions 数组是否存在且不为空
          if (!data.versions || !Array.isArray(data.versions) || data.versions.length === 0) {
            console.warn('data.versions is undefined, not an array, or empty')
            return
          }
          
          if (this.subjectObj?.subject) {
            let isHave = false
            data.versions?.map(item => {
              if (item.bookId === this.subjectObj.bookId) {
                isHave = true
              }
            })
            if (!isHave) {
              this.subjectObj = data.versions[0] || {}
              const id = this.getId()
              if (id !== null) {
                this.subjectObj.id = id
              }
              // 同步存储到 localStorage
              localStorage.setItem("subjectObj", JSON.stringify(this.subjectObj))
            }
          } else {
            // 确保 versions[0] 存在
            if (data.versions[0]) {
              this.subjectObj = data.versions[0]
              const id = this.getId()
              if (id !== null) {
                this.subjectObj.id = id
              }
              // 同步存储到 localStorage
              localStorage.setItem("subjectObj", JSON.stringify(this.subjectObj))
            } else {
              console.warn('data.versions[0] is undefined')
            }
          }
        }
      } catch (error) {
        console.error('Error in setlearnNow:', error)
        // 即使出现错误，也要确保基本的数据设置
        if (data) {
          this.learnNow = data
          localStorage.setItem("learnNow", JSON.stringify(data))
        }
      }
    },
    setChapterList(data: any) {
      this.chapterList = data
      localStorage.setItem("chapterList", JSON.stringify(data))
    },
    setChapterId(chapterId?: any, chapterName?: any, type?: any) {
      this.chapterObj.chapterId = chapterId
      this.chapterObj.chapterName = chapterName
      this.chapterObj.type = type
      localStorage.setItem("chapterObj", JSON.stringify(this.chapterObj))
    },
    setImprovementId(chapterId?: any, chapterName?: any, type?: any) {
      this.improvementObj.chapterId = chapterId
      this.improvementObj.chapterName = chapterName
      this.improvementObj.type = type
      localStorage.setItem("improvementObj", JSON.stringify(this.improvementObj))
    },
    getId () {
      if (!this.subjectObj?.subject) {
        console.warn('subjectObj.subject is undefined or null')
        return null
      }
      
      if (!subjectList[this.subjectObj.subject]) {
        console.warn(`Subject ${this.subjectObj.subject} not found in subjectList`)
        return null
      }
      
      return subjectList[this.subjectObj.subject].key
    },
    // 设置学科数据
    setSubjectList(data: any[]) {
      this.subjectList = data
      localStorage.setItem("subjectList", JSON.stringify(data))
    },
    // 设置选中学科
    setSubjectObj(subject : string, id: number) {
      let data = {} as any
      if (subject) {
        this.learnNow.versions.map(item => {
          if (item.subject === subject) {
            data = item
          }
        })
        this.subjectObj = data
        data.id = id
        this.setChapterId()
        localStorage.setItem("subjectObj", JSON.stringify(data))
      }
    },
    //姓名脱敏带*
    formatName(name : string) {
      let newStr = ""
      if (name.length === 2) {
        newStr = name.substr(0, 1) + "*"
      } else if (name.length > 2) {
        let char = ""
        for (let i = 0, len = name.length - 2; i < len; i++) {
          char += "*"
        }
        newStr = name.substr(0, 1) + char + name.substr(-1, 1)
      } else {
        newStr = name
      }
      return newStr
    },
    //退出登录
    loginNo() {
      return new Promise((resolve, reject) => {
        Promise.resolve()
          .then(() => {
            this.resetToken()
            resolve(true)
          })
          .catch((error) => {
            reject(error)
          })
      })
    },
    // 登出
    logout(isUnToken?: boolean) {
      // 通知iframe父级窗口清除token
      this.notifyParentWindowLogout()
      
      this.resetToken(isUnToken)

      router.replace({ path: "/login" })
      // resetRouter()
    },
    
    // 通知父级窗口登出
    notifyParentWindowLogout() {
      try {
        // 检查是否在iframe中
        if (window.parent && window.parent !== window) {
          // 向父级窗口发送登出消息
          window.parent.postMessage({
            type: 'STUDENT_LOGOUT',
            action: 'clearToken',
            data: {
              timestamp: Date.now(),
              source: 'student-app'
            }
          }, '*')
          
          console.log('已通知父级窗口清除token')
        }
      } catch (error) {
        console.warn('通知父级窗口登出失败:', error)
      }
    },
    // 重置 Token  
    resetToken(isUnToken?: boolean) {
      removeToken()
      localStorage.removeItem("token")
      localStorage.removeItem("sysUserId")
      localStorage.removeItem("userInfo")
      localStorage.removeItem("learnNow")
      localStorage.removeItem("subjectObj")
      localStorage.removeItem("learnUsers")
      localStorage.removeItem("memberInfo")
      localStorage.removeItem("isDaySign")
      localStorage.removeItem("chapterList")
      localStorage.removeItem("chapterObj")
      localStorage.removeItem("improvementObj")
      
      // 清除同步学习相关的缓存
      localStorage.removeItem("syncSelectedBook")
      localStorage.removeItem("syncListScrollTop")
      localStorage.removeItem("syncListScrollTop2")
      localStorage.removeItem("syncListChapterId")
      localStorage.removeItem("currentCourse")
      if (!isUnToken) {
        localStorage.removeItem("isLogin")
      }
      sessionStorage.clear();
      // 清除所有科目相关缓存
      try {
        // 获取所有localStorage中的键
        for (let i = 0; i < localStorage.length; i++) {
          const key = localStorage.key(i);
          // 检查是否为同步学习科目缓存键
          if (key && key.includes('syncSelectedBook_')) {
            localStorage.removeItem(key);
          }
          if (key && key.includes('selectedChapterInfo_')) {
            localStorage.removeItem(key);
          }
        
        }
      } catch (err) {
        console.error('清除科目缓存失败:', err);
      }
      
      this.token = ""
      this.sysUserId = ""
      this.userInfo = ""
      this.learnNow = ""
      this.subjectObj = ""
      this.improvementObj = ""
      this.learnUsers = ""
      this.redirect = ""
      this.memberInfo = ''
      this.$reset()
    }
  }
})
/** 在 setup 外使用 */
export function useUserStoreHook() {
  return useUserStore(store)
}