<template>
            <!-- 顶部导航 -->
    <header class="page-header">
      <div class="breadcrumbs">
        <a href="#" class="back-link" @click.prevent="goBack">&lt; 返回</a>
        <span class="breadcrumb-separator">&nbsp;/&nbsp;</span>
        <span class="breadcrumb-item">AI精准学</span>
        <span class="breadcrumb-separator">&nbsp;/&nbsp;</span>
        <span class="breadcrumb-item">{{ getTrainingModeText(query.type) }}</span>
        <span class="breadcrumb-separator">&nbsp;/&nbsp;</span>
        <span class="breadcrumb-item active">知识点标熟</span>
      </div>
    </header>
  <div class="knowledge-hotspot-page">
    <!-- 规则说明 -->
    <div class="rule-notice">
      <div class="notice-icon"></div>
      <span class="notice-text">
        规则：知识点标熟后，每个知识点出一道题进行检测，过关后确认为"已掌握"，反之"未掌握"。每个知识点仅有一次标熟测试的机会。
      </span>
    </div>

    <!-- 知识点章节列表 -->
    <div class="knowledge-sections">
      <div
        v-for="(section,index) in knowledgeSections"
        :key="index"
        class="knowledge-section"
      >
        <!-- 一级标题 - 始终展开，不可折叠 -->
        <div class="section-header">
          <h2 class="section-title">{{ section.title }}</h2>
        </div>

        <!-- 一级标题下的内容 -->
        <div class="section-content">
          <div
            v-for="(subSection, index) in section.subSections"
            :key="`${section.id}-sub-${index}`"
            class="sub-section"
          >
            <!-- 二级标题 - 可以折叠 -->
            <div
              class="sub-section-header"
              @click="toggleSubSection(`${section.id}-sub-${index}`)"
            >
              <h3 class="sub-section-title">{{ subSection.title }}</h3>
              <div
                class="toggle-icon"
                :class="{ expanded: expandedSubSections[`${section.id}-sub-${index}`] }"
              >
                <!-- <div class="arrow"></div> -->
              </div>
            </div>

            <!-- 二级标题下的内容 -->
            <div
              v-show="expandedSubSections[`${section.id}-sub-${index}`]"
              class="sub-section-content"
            >
              <!-- 如果有三级标题，显示三级结构 -->
              <div v-if="subSection.thirdLevelSections && subSection.thirdLevelSections.length > 0" class="third-level-sections">
                <div
                  v-for="(thirdSection, thirdIndex) in subSection.thirdLevelSections"
                  :key="`${section.id}-sub-${index}-third-${thirdIndex}`"
                  class="third-level-section"
                >
                  <!-- 三级标题 - 可以折叠 -->
                  <div
                    class="third-level-header"
                    @click="toggleThirdLevel(`${section.id}-sub-${index}-third-${thirdIndex}`)"
                  >
                    <h4 class="third-level-title">{{ thirdSection.title }}</h4>
                    <div
                      class="toggle-icon"
                      :class="{ expanded: expandedThirdLevels[`${section.id}-sub-${index}-third-${thirdIndex}`] }"
                    >
                    </div>
                  </div>

                  <!-- 三级标题下的知识点列表 -->
                  <div
                    v-show="expandedThirdLevels[`${section.id}-sub-${index}-third-${thirdIndex}`]"
                    class="third-level-content"
                  >
                    <div v-if="thirdSection.points && thirdSection.points.length > 0" class="knowledge-points-grid">
                      <div
                        v-for="point in thirdSection.points"
                        :key="point.id"
                        class="knowledge-point-item"
                        :class="{
                          'is-hotspot': point.isHotspot,
                          'status-marked': point.status === 0,
                          'status-mastered': point.status === 1,
                          'status-unmastered': point.status === 2
                        }"
                      >
                        <div class="point-content">
                          <span class="point-name">{{ point.name }}</span>

                          <div class="point-actions">
                            <template v-if="point.status == 0">
                              <div class="hotspot-indicator">
                                <div class="star-icon active"></div>
                                <span class="status-text">已标熟</span>
                              </div>
                              <button
                                class="cancel-btn"
                                @click="cancelHotspot(point.id)"
                              >
                                取消
                              </button>
                            </template>
                            
                            <img v-else-if="point.status === 1" class="status-icon" src="@/assets/img/percision/training/understood.png" alt="已掌握" />
                            <img v-else-if="point.status === 2" class="status-icon" src="@/assets/img/percision/training/notMastered.png" alt="未掌握" />
                            <template v-else>
                              <button
                                class="mark-btn"
                                @click="markAsHotspot(point.id)"
                              >
                                <div class="star-icon"></div>
                                <span>标熟</span>
                              </button>
                            </template>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div v-else class="empty-points-message">
                      该小节下暂无可标熟的知识点
                    </div>
                  </div>
                </div>
              </div>

              <!-- 如果没有三级标题，直接显示知识点列表（原有逻辑） -->
              <div v-else>
                <div v-if="subSection.points && subSection.points.length > 0" class="knowledge-points-grid">
                  <div
                    v-for="point in subSection.points"
                    :key="point.id"
                    class="knowledge-point-item"
                    :class="{
                      'is-hotspot': point.isHotspot,
                      'status-marked': point.status === 0,
                      'status-mastered': point.status === 1,
                      'status-unmastered': point.status === 2
                    }"
                  >
                    <div class="point-content">
                      <span class="point-name">{{ point.name }}</span>

                      <div class="point-actions">
                        <template v-if="point.status == 0">
                          <div class="hotspot-indicator">
                            <div class="star-icon active"></div>
                            <span class="status-text">已标熟</span>
                          </div>
                          <button
                            class="cancel-btn"
                            @click="cancelHotspot(point.id)"
                          >
                            取消
                          </button>
                        </template>
                        
                        <img v-else-if="point.status === 1" class="status-icon" src="@/assets/img/percision/training/understood.png" alt="已掌握" />
                        <img v-else-if="point.status === 2" class="status-icon" src="@/assets/img/percision/training/notMastered.png" alt="未掌握" />
                        <template v-else>
                          <button
                            class="mark-btn"
                            @click="markAsHotspot(point.id)"
                          >
                            <div class="star-icon"></div>
                            <span>标熟</span>
                          </button>
                        </template>
                      </div>
                    </div>
                  </div>
                </div>
                <div v-else class="empty-points-message">
                  该章节下暂无可标熟的知识点
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作栏 -->
    <div class="bottom-action-bar">
      <div 
        class="action-button" 
        :class="{ 'disabled': hotspotCount === 0 }"
        @click="startEvaluation"
      >
        <span class="button-text">{{ hotspotCount }}个已标熟，去测评</span>
      </div>
    </div>
    
    <!-- 加载遮罩层 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <p>正在加载数据...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, watch } from 'vue'
import { getpointListApi, getChapterListApi } from "@/api/book"
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter ,useRoute} from 'vue-router'
import { dataEncrypt, dataDecrypt } from "@/utils/secret"
import { getImprovementPointList, getSonPointList, pointListList, getStudyPointApi } from "@/api/video"

import { getPointCategoryApi, categoryAddApi, addTraininsgApi,categoryDelApi} from "@/api/precise"

const router = useRouter()
const route = useRoute()
const query = reactive<any>(route.query)

// 获取训练模式文本
const getTrainingModeText = (type: string) => {
  switch (type) {
    case '1':
      return '基础训练'
    case '2':
      return '提升训练'
    case '3':
      return '备考模式'
    default:
      return '未知模式'
  }
}

// 临时定义，稍后会被更新
let getUniqueHotspotIds = (): string[] => {
  return []
}

// 知识点数据结构
interface KnowledgePoint {
  id: string
  name: string
  isHotspot: boolean
  status?: number // -1: 未标记, 0: 已标记, 1: 已掌握, 2: 未掌握
}

interface ThirdLevelSection {
  title: string
  points: KnowledgePoint[]
}

interface SubSection {
  title: string
  points?: KnowledgePoint[]
  thirdLevelSections?: ThirdLevelSection[]
}

interface KnowledgeSection {
  id: string
  title: string
  subSections: SubSection[]
}

// 二级标题的折叠展开状态管理（只有二级标题可以折叠）
const expandedSubSections = reactive<Record<string, boolean>>({})

// 三级标题的折叠展开状态管理
const expandedThirdLevels = reactive<Record<string, boolean>>({})

// 知识点章节数据
const knowledgeSections = ref<KnowledgeSection[]>([
  {
    id: 'section1',
    title: '一、一级标题一级标题一级标题一级标题',
    subSections: [
      {
        title: '二级标题二级标题 > 三级标题',
        points: [
          { id: 'point1-1', name: '知识点名称', isHotspot: false },
          { id: 'point1-2', name: '知识点名称', isHotspot: false },
          { id: 'point1-3', name: '知识点名称', isHotspot: false },
          { id: 'point1-4', name: '知识点名称', isHotspot: false }
        ]
      },
      {
        title: '二级标题二级标题 > 三级标题三级标题三级标题',
        points: [
          { id: 'point1-5', name: '知识点名称知识点名称知识点...', isHotspot: false },
          { id: 'point1-6', name: '知识点名称知识点名称', isHotspot: false },
          { id: 'point1-7', name: '知', isHotspot: false },
          { id: 'point1-8', name: '知识点名称知识点名称知识点名称知识点...', isHotspot: false },
          { id: 'point1-9', name: '知识点名称知', isHotspot: false },
          { id: 'point1-10', name: '知识点', isHotspot: false },
          { id: 'point1-11', name: '知识点', isHotspot: false },
          { id: 'point1-12', name: '知识点名称', isHotspot: false },
          { id: 'point1-13', name: '知识点名称', isHotspot: false },
          { id: 'point1-14', name: '知识点名称知识点名称', isHotspot: false },
          { id: 'point1-15', name: '知', isHotspot: false }
        ]
      }
    ]
  },
  {
    id: 'section2',
    title: '二、一级标题一级标题一级标题一级标题',
    subSections: [
      {
        title: '二级标题二级标题 > 三级标题',
        points: [
          { id: 'point2-1', name: '知识点名称知识点名称知识点名称', isHotspot: false },
          { id: 'point2-2', name: '知识点名称知识点名称', isHotspot: false },
          { id: 'point2-3', name: '知识点名称知识点名称', isHotspot: false }
        ]
      },
      {
        title: '二级标题二级标题 > 三级标题三级标题三级标题',
        points: [
          { id: 'point2-4', name: '知识点名称', isHotspot: false },
          { id: 'point2-5', name: '知识点名称', isHotspot: false },
          { id: 'point2-6', name: '知识', isHotspot: false },
          { id: 'point2-7', name: '知识点名称知识点名称', isHotspot: false }
        ]
      }
    ]
  }
])

// 更新getUniqueHotspotIds函数实现（支持三级结构）
getUniqueHotspotIds = (): string[] => {
  // 使用Set自动去重，即使数据中有重复ID，也只会计算一次
  const hotspotIdsSet = new Set<string>()
  
  knowledgeSections.value.forEach(section => {
    section.subSections.forEach(subSection => {
      // 如果有三级结构，遍历三级结构中的知识点
      if (subSection.thirdLevelSections && subSection.thirdLevelSections.length > 0) {
        subSection.thirdLevelSections.forEach(thirdSection => {
          if (thirdSection.points) {
            thirdSection.points.forEach(point => {
              // 只收集状态为0（已标记）的知识点
              if (point.status === 0 && point.id) {
                hotspotIdsSet.add(point.id)
              }
            })
          }
        })
      }
      // 如果没有三级结构，直接遍历二级结构中的知识点
      else if (subSection.points) {
        subSection.points.forEach(point => {
          // 只收集状态为0（已标记）的知识点
          if (point.status === 0 && point.id) {
            hotspotIdsSet.add(point.id)
          }
        })
      }
    })
  })
  
  // 返回去重后的ID数组
  return Array.from(hotspotIdsSet)
}

// 记录唯一ID和重复ID
const logUniqueAndDuplicateIds = () => {
  // 确保knowledgeSections已初始化
  if (!knowledgeSections.value || knowledgeSections.value.length === 0) {
    console.log('知识点数据尚未初始化')
    return { uniqueIds: [], duplicateIds: [] }
  }
  
  // 收集所有知识点ID
  const allIds: string[] = []
  const uniqueIds: string[] = []
  const duplicateIds: string[] = []
  
  knowledgeSections.value.forEach(section => {
    section.subSections.forEach(subSection => {
      // 如果有三级结构，遍历三级结构中的知识点
      if (subSection.thirdLevelSections && subSection.thirdLevelSections.length > 0) {
        subSection.thirdLevelSections.forEach(thirdSection => {
          if (thirdSection.points) {
            thirdSection.points.forEach(point => {
              if (point.id) {
                if (allIds.includes(point.id)) {
                  if (!duplicateIds.includes(point.id)) {
                    duplicateIds.push(point.id)
                  }
                } else {
                  allIds.push(point.id)
                  uniqueIds.push(point.id)
                }
              }
            })
          }
        })
      }
      // 如果没有三级结构，遍历二级结构中的知识点
      else if (subSection.points) {
        subSection.points.forEach(point => {
          if (point.id) {
            if (allIds.includes(point.id)) {
              if (!duplicateIds.includes(point.id)) {
                duplicateIds.push(point.id)
              }
            } else {
              allIds.push(point.id)
              uniqueIds.push(point.id)
            }
          }
        })
      }
    })
  })
  
  console.log(`唯一ID数量: ${uniqueIds.length}, 重复ID数量: ${duplicateIds.length}`)
  if (duplicateIds.length > 0) {
    console.log('重复ID列表:', duplicateIds)
  }
  
  return { uniqueIds, duplicateIds }
}

// 加载状态
const loading = ref(false)

// 计算已标熟的数量
const hotspotCount = computed(() => {
  // 确保knowledgeSections已初始化
  if (!knowledgeSections.value || knowledgeSections.value.length === 0) {
    return 0
  }
  
  // 使用辅助函数获取唯一的已标记知识点ID
  const uniqueIds = getUniqueHotspotIds()
  const count = uniqueIds.length
  
  return count
})

// 监听hotspotCount变化
watch(hotspotCount, (newCount, oldCount) => {
  console.log(`hotspotCount变化: ${oldCount} -> ${newCount}`)
})

// 切换二级标题的折叠展开状态
const toggleSubSection = (subSectionId: string) => {
  expandedSubSections[subSectionId] = !expandedSubSections[subSectionId]
}

// 切换三级标题的折叠展开状态
const toggleThirdLevel = (thirdLevelId: string) => {
  expandedThirdLevels[thirdLevelId] = !expandedThirdLevels[thirdLevelId]
}

// 🎯 新增：初始化所有章节的展开状态
const initializeAllSectionsExpanded = () => {
  // 清空现有的展开状态
  Object.keys(expandedSubSections).forEach(key => {
    delete expandedSubSections[key]
  })
  Object.keys(expandedThirdLevels).forEach(key => {
    delete expandedThirdLevels[key]
  })
  
  // 遍历所有章节，设置所有二级和三级标题为展开状态
  knowledgeSections.value.forEach((section, sectionIndex) => {
    section.subSections.forEach((subSection, subIndex) => {
      const subSectionId = `${section.id}-sub-${subIndex}`
      expandedSubSections[subSectionId] = true
      
      // 如果有三级标题，也设置为展开状态
      if (subSection.thirdLevelSections && subSection.thirdLevelSections.length > 0) {
        subSection.thirdLevelSections.forEach((thirdSection, thirdIndex) => {
          const thirdLevelId = `${section.id}-sub-${subIndex}-third-${thirdIndex}`
          expandedThirdLevels[thirdLevelId] = true
        })
      }
    })
  })
  
  console.log('🎯 所有章节已设置为展开状态:', {
    二级标题展开数量: Object.keys(expandedSubSections).length,
    三级标题展开数量: Object.keys(expandedThirdLevels).length,
    展开的二级标题: Object.keys(expandedSubSections),
    展开的三级标题: Object.keys(expandedThirdLevels)
  })
}

// 获取知识点的父级章节ID（支持三级结构）
const getParentChapterId = (pointId: string): string | null => {
  for (const section of knowledgeSections.value) {
    for (const subSection of section.subSections) {
      // 如果有三级结构，先在三级结构中查找
      if (subSection.thirdLevelSections && subSection.thirdLevelSections.length > 0) {
        for (const thirdSection of subSection.thirdLevelSections) {
          if (thirdSection.points) {
            const point = thirdSection.points.find(p => p.id === pointId)
            if (point) {
              return section.id
            }
          }
        }
      }
      // 如果没有三级结构，在二级结构中查找
      else if (subSection.points) {
        const point = subSection.points.find(p => p.id === pointId)
        if (point) {
          console.log(`知识点 ${pointId} 的父级章节ID: ${section.id}`)
          return section.id
        }
      }
    }
  }
  console.warn(`未找到知识点 ${pointId} 的父级章节`)
  return null
}

// 标熟操作
const markAsHotspot = async (pointId: string) => {
  try {
    // 获取知识点的父级章节ID
    const parentChapterId = getParentChapterId(pointId)
    
    if (!parentChapterId) {
      ElMessage.error('无法获取知识点的章节信息')
      return
    }
    
    await ElMessageBox.confirm(
      '确定要将此知识点标记为已熟练吗？',
      '确认标熟',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        center: true, // 让弹窗内容居中显示
        customStyle: {
          textAlign: 'center'
        }
        // type: 'info'
      }
    )
    
    // 添加标熟
    const pointIdArr = pointId.split(',')
    
    // 构建chapterPoints数组，包含章节ID和知识点ID
    const chapterPoints = pointIdArr.map(pId => ({
      chapterId: parentChapterId,
      pointId: pId
    }))
    
    console.log('标熟操作参数:', {
      bookId: query.bookId,
      chapterPoints: chapterPoints,
      type: 1,
      subject: query.subject
    })
    if(query.type == "3"){
        categoryAddApi({
        // bookId: query.bookId,
        // chapterPoints: chapterPoints, // 传递包含章节ID和知识点ID的数组
        pointIds:pointIdArr,
        type: query.type,
        subject: query.subject
      }).then((res) => {
        if (res.code === 200) {
          console.log(res,"新增成功！")
          console.log(`知识点 ${pointId} 已标熟，所属章节: ${parentChapterId}`)
          // getBookList()
        } 
      })
    }else{
      categoryAddApi({
        bookId: query.bookId,
        chapterPoints: chapterPoints, // 传递包含章节ID和知识点ID的数组
        type: query.type,
        subject: query.subject
      }).then((res) => {
        if (res.code === 200) {
          console.log(res,"新增成功！")
          console.log(`知识点 ${pointId} 已标熟，所属章节: ${parentChapterId}`)
          // getBookList()
        } 
      })
      }
    

   
    
    // 找到对应的知识点并更新状态（支持三级结构）
    knowledgeSections.value.forEach(section => {
      section.subSections.forEach(subSection => {
        // 如果有三级结构，在三级结构中查找并更新
        if (subSection.thirdLevelSections && subSection.thirdLevelSections.length > 0) {
          subSection.thirdLevelSections.forEach(thirdSection => {
            if (thirdSection.points) {
              const point = thirdSection.points.find(p => p.id === pointId)
              if (point) {
                point.isHotspot = true
                // 更新状态为已标记
                point.status = 0
                // console.log(`已更新知识点 ${pointId} 状态为已标熟`)
              }
            }
          })
        }
        // 如果没有三级结构，在二级结构中查找并更新
        else if (subSection.points) {
          const point = subSection.points.find(p => p.id === pointId)
          if (point) {
            point.isHotspot = true
            // 更新状态为已标记
            point.status = 0
            // console.log(`已更新知识点 ${pointId} 状态为已标熟`)
          }
        }
      })
    })

    ElMessage.success('标熟成功！')
  } catch {
    // 用户取消操作
  }
}

// 取消标熟操作
const cancelHotspot = async (pointId: string) => {
  try {
    // 获取知识点的父级章节ID
    const parentChapterId = getParentChapterId(pointId)
    
    if (!parentChapterId) {
      ElMessage.error('无法获取知识点的章节信息')
      return
    }
    
    await ElMessageBox.confirm(
      '确定要取消此知识点的标熟状态吗？',
      '确认取消',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        center: true, // 让弹窗内容居中显示
        customStyle: {
          textAlign: 'center'
        }
      }
    )
    
    // 取消标熟
    const pointIdArr = pointId.split(',')
    
    // 构建chapterPoints数组，包含章节ID和知识点ID
    const chapterPoints = pointIdArr.map(pId => ({
      chapterId: parentChapterId,
      pointId: pId
    }))
    
    categoryDelApi({
      bookId: query.bookId,
      chapterPoints: chapterPoints, // 传递包含章节ID和知识点ID的数组
      type: 1,
      subject: query.subject
    }).then((res) => {
      if (res.code === 200) {
        console.log(res,"删除成功！")
      } 
    })
    
    // 找到对应的知识点并更新状态（支持三级结构）
    knowledgeSections.value.forEach(section => {
      section.subSections.forEach(subSection => {
        // 如果有三级结构，在三级结构中查找并更新
        if (subSection.thirdLevelSections && subSection.thirdLevelSections.length > 0) {
          subSection.thirdLevelSections.forEach(thirdSection => {
            if (thirdSection.points) {
              const point = thirdSection.points.find(p => p.id === pointId)
              if (point) {
                point.isHotspot = false
                // 重置状态为未标记
                point.status = -1
              }
            }
          })
        }
        // 如果没有三级结构，在二级结构中查找并更新
        else if (subSection.points) {
          const point = subSection.points.find(p => p.id === pointId)
          if (point) {
            point.isHotspot = false
            // 重置状态为未标记
            point.status = -1
          }
        }
      })
    })

    ElMessage.success('取消标熟成功！')
  } catch {
    // 用户取消操作
  }
}

// 开始评估
const startEvaluation = () => {
  if (hotspotCount.value === 0) {
    ElMessage.warning('请至少标记一个知识点为已熟练')
    return
  }
 
  // 获取唯一的已标记知识点ID
  const uniqueArr = getUniqueHotspotIds()
  // 知识点标熟
  addTraininsgApi({
    bookId: query.bookId,
    pointIds: uniqueArr,
    type: query.type,
    subject:query.subject
  }).then((res:any) => {
    if (res.code === 200) {
      if(res.data){
        router.push({
        path: '/ai_percision/answer_training',
        query: {
          data: dataEncrypt({
            reportId: res.data ,
            pageSource: '1',
            bookId: query.bookId,
            chapterId:query.chapterId,
            source:'ripe',
            subject:query.subject,
            type:query.type
          }),
        }
      })
        //   router.push({
        //   path: '/ai_percision/answer_training',
        //   query: {
        //     reportId: res.data,
        //   }
        // })
      }
    } 
  })

}

// 返回上一页
const goBack = () => {
    // 检查是否从训练报告页面来的
    const isFromTrainingReport = checkIfFromTrainingReport()
    if (isFromTrainingReport) {
      router.push({
        path: '/ai_percision/knowledge_graph',
      })
    } else {
      router.go(-1)
    }
    // window.history.back()

}
  // 检查是否从训练报告页面来的
  const checkIfFromTrainingReport = () => {
    // 方法1: 检查路由查询参数中是否有特定标识
    if (query.from === 'training_report' || query.pageSource === 'training_report') {
      return true
    }
    
    // 方法2: 检查sessionStorage中的页面来源标记
    const pageSource = sessionStorage.getItem('basic_training_source')
    if (pageSource) {
      if (pageSource.includes('training_report') || 
          pageSource.includes('paper_analysis') ||
          pageSource.includes('regarding_learning') ||
          pageSource.includes('foundation_report')) {
        // 清除标记，避免影响后续导航
        sessionStorage.removeItem('basic_training_source')
        return true
      }
    }
    
    // 方法3: 检查referrer URL
    const referrer = document.referrer
    if (referrer) {
      try {
        const referrerUrl = new URL(referrer)
        const referrerPath = referrerUrl.pathname
        
        
        // 检查是否包含训练报告相关的路径
        const trainingReportPaths = [
          '/ai_percision/training_report',
          '/ai_percision/paper_analysis', 
          '/ai_percision/regarding_learning',
          '/ai_percision/foundation_report'
        ]
        
        for (const path of trainingReportPaths) {
          if (referrerPath.includes(path)) {
            return true
          }
        }
      } catch (error) {
      }
    }
    
    // 方法4: 检查Vue Router的历史记录
    try {
      const history = router.options.history
      if (history && history.state) {
        const historyState = history.state
        
        // 检查历史状态中是否有相关信息
        if (historyState.back && typeof historyState.back === 'string') {
          const backRoute = historyState.back
          
          if (backRoute.includes('training_report') || 
              backRoute.includes('paper_analysis') ||
              backRoute.includes('regarding_learning') ||
              backRoute.includes('foundation_report')) {
            return true
          }
        }
      }
    } catch (error) {
    }
    
    return false
  }

  // 自定义返回方法
  const customGoBack = () => {
      router.go(-1)
  }
  onUnmounted(() => {
    // 清除自定义返回方法
    if (window.customGoBack) {
      delete window.customGoBack
    }
  })
onMounted(() => {
  window.customGoBack = customGoBack
  
  // 获取数据
  getBookList()
  // 初始化后检查知识点状态
  setTimeout(() => {
    logAllPointsStatus()
  }, 1000)
})

//获取列表
const getBookList = async() => {

  try {
    loading.value = true // 显示加载状态
    let res: any
     if(query.type == "3"){
       res = await pointListList({
        subject:query.subject,
        hierarchy: 3,
      })
    }else{
         res = await getPointCategoryApi({
        bookId:query.bookId,
        type: query.type,
      })
    }
    
    // 处理接口返回的数据结构
    let processedData:any = []
    
    if (query.type == "3") {
      // type=3 时使用 pointListList 接口，数据结构可能不同
      // console.log('=== 处理 type=3 的数据 ===')
      // console.log('原始响应数据:', res.data)
      // console.log('数据类型:', typeof res.data)
      // console.log('是否为数组:', Array.isArray(res.data))
      
      // 尝试多种可能的数据结构
      let dataToProcess:any = null
      
      if (res.data && Array.isArray(res.data)) {
        // 如果返回的直接是数组
        dataToProcess = res.data
      } else if (res.data && res.data.chapters && Array.isArray(res.data.chapters)) {
        // 如果返回的是包含chapters的对象
        dataToProcess = res.data.chapters
      } else if (res.data && res.data.list && Array.isArray(res.data.list)) {
        // 如果返回的是包含list的对象
        dataToProcess = res.data.list
      } else if (res.data && res.data.data && Array.isArray(res.data.data)) {
        // 如果返回的是包含data的对象
        dataToProcess = res.data.data
      } else {
        dataToProcess = []
      }
      
      if (dataToProcess && dataToProcess.length > 0) {
        processedData = processPointListData(dataToProcess)
      } else {
        console.warn('没有找到可处理的数据')
        processedData = []
      }
    } else {
      // 非 type=3 时使用原有逻辑
      if (res.data && res.data.chapters && Array.isArray(res.data.chapters)) {
        processedData = processChaptersData(res.data.chapters)
      } else {
        console.warn('API返回的数据结构不符合预期:', res.data)
        processedData = []
      }
    }
    
    knowledgeSections.value = processedData
    
    if (processedData.length > 0) {
      // 检查是否有重复的知识点ID
      checkDuplicatePointIds()
      
      // 记录唯一ID和重复ID
      logUniqueAndDuplicateIds()
      
      // 同步所有知识点的isHotspot和status
      syncAllPointsHotspotStatus()
      
      // 🎯 新增：初始化所有章节为展开状态
      initializeAllSectionsExpanded()
      
      // 记录所有知识点状态
      setTimeout(() => {
        logAllPointsStatus()
      }, 500)
    } else {
      console.warn('处理后的数据为空')
    }
    
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取章节数据失败')
    knowledgeSections.value = []
  } finally {
    // 延迟关闭loading，确保DOM渲染完成
    setTimeout(() => {
      loading.value = false
    }, 300)
  }
}

// 处理章节数据
const processChaptersData = (chapters) => {
  if (!chapters || !Array.isArray(chapters)) return []
  
  return chapters.map((chapter, index) => {
    return {
      id: chapter.id || `section${index + 1}`,
      title: `${index + 1}、${chapter.name || '未命名章节'}`,
      subSections: processSubSections(chapter.children || [])
    }
  })
}

// 处理 pointListList 接口返回的数据（type=3 专用，支持多级嵌套）
const processPointListData = (pointListData) => {
  if (!pointListData || !Array.isArray(pointListData)) {
    console.warn('pointListList 返回的数据结构不是数组:', pointListData)
    return []
  }
  
  // console.log('=== 开始处理 pointListList 数据 ===')
  // console.log('数据长度:', pointListData.length)
  // console.log('原始数据结构预览:', JSON.stringify(pointListData.slice(0, 1), null, 2))
  
  return pointListData.map((chapter, index) => {
    
    // 统计当前章节的结构信息
    const hasDirectPoints = chapter.points && Array.isArray(chapter.points) && chapter.points.length > 0
    const hasChildren = chapter.children && Array.isArray(chapter.children) && chapter.children.length > 0
    const hasSubChapters = chapter.subChapters && Array.isArray(chapter.subChapters) && chapter.subChapters.length > 0
        
    // 如果章节直接包含 points，则创建一个默认的子章节
    if (hasDirectPoints) {
      console.log(`章节直接包含 ${chapter.points.length} 个知识点，创建默认子章节`)
      
      return {
        id: chapter.id || `section${index + 1}`,
        title: `${index + 1}、${chapter.name || chapter.chapterName || '未命名章节'}`,
        subSections: [{
          title: chapter.name || chapter.chapterName || '知识点列表',
          points: processPointListKnowledgePoints(chapter.points)
        }]
      }
    }
    
    // 处理有子级结构的情况
    let subSections:any = []
    
    if (hasChildren) {
      subSections = processPointListSubSections(chapter.children, 2)
    } else if (hasSubChapters) {
      subSections = processPointListSubSections(chapter.subChapters, 2)
    }
    
    // 如果没有找到任何子章节，创建一个空的提示
    if (subSections.length === 0) {
      subSections = [{
        title: '暂无子章节',
        points: []
      }]
    }
     
    return {
      id: chapter.id || `section${index + 1}`,
      title: `${index + 1}、${chapter.name || chapter.chapterName || '未命名章节'}`,
      subSections: subSections
    }
  })
}

// 处理 pointListList 的子章节数据（支持多级嵌套）
const processPointListSubSections = (subChapters, level = 1) => {
  if (!subChapters || !Array.isArray(subChapters)) {
    return []
  }
   
  return subChapters.map((subChapter, index) => {
    
    // 首先检查是否有直接的 points 数据
    if (subChapter.points && Array.isArray(subChapter.points) && subChapter.points.length > 0) {
      
      return {
        title: subChapter.name || subChapter.chapterName || `未命名小节${index + 1}`,
        points: processPointListKnowledgePoints(subChapter.points)
      }
    }
    
    // 检查是否有子级结构需要进一步处理
    if (subChapter.children && Array.isArray(subChapter.children) && subChapter.children.length > 0) {
      
      // 检查 children 中是否包含知识点数据
      const hasPointsInChildren = subChapter.children.some(child => 
        (child.points && Array.isArray(child.points) && child.points.length > 0) ||
        (child.children && Array.isArray(child.children))
      )
      
      if (hasPointsInChildren) {
        // children 中有知识点或更深层级，根据type=3判断是否创建三级结构
        
        // 检查是否是type=3，如果是则创建三级结构
        if (query.type === '3' && level === 2) {
          
          // 为每个child创建三级标题
          const thirdLevelSections = subChapter.children
            .filter(child => (child.points && Array.isArray(child.points) && child.points.length > 0) || 
                           (child.children && Array.isArray(child.children)))
            .map((child, childIndex) => {
              
              let points = []
              if (child.points && Array.isArray(child.points)) {
                points = child.points
              } else if (child.children && Array.isArray(child.children)) {
                points = extractPointsFromDeepStructure(child.children)
              }
              
              return {
                title: child.name || child.chapterName || `未命名三级标题${childIndex + 1}`,
                points: processPointListKnowledgePoints(points)
              }
            })
                  
          return {
            title: subChapter.name || subChapter.chapterName || `未命名小节${index + 1}`,
            thirdLevelSections: thirdLevelSections
          }
        } else {
          // 非type=3或非二级章节，收集所有子级的知识点
          let allPoints = []
          subChapter.children.forEach((child, childIndex) => {
            if (child.points && Array.isArray(child.points)) {
              allPoints = allPoints.concat(child.points)
            } else if (child.children && Array.isArray(child.children)) {
              // 对于更深层级，递归收集知识点
              const deepPoints = extractPointsFromDeepStructure(child.children)
              allPoints = allPoints.concat(deepPoints)
            }
          })
                    
          return {
            title: subChapter.name || subChapter.chapterName || `未命名小节${index + 1}`,
            points: processPointListKnowledgePoints(allPoints)
          }
        }
      } else {
        // children 中没有知识点，可能是纯结构性的，直接使用 children 作为知识点
        return {
          title: subChapter.name || subChapter.chapterName || `未命名小节${index + 1}`,
          points: processPointListKnowledgePoints(subChapter.children)
        }
      }
    }
    
    // 既没有 points 也没有有效的 children
    return {
      title: subChapter.name || subChapter.chapterName || `未命名小节${index + 1}`,
      points: []
    }
  })
}

// 从深层结构中递归提取知识点
const extractPointsFromDeepStructure = (structure, maxDepth = 5, currentDepth = 0) => {
  if (!structure || !Array.isArray(structure) || currentDepth >= maxDepth) {
    return []
  }
  
  let allPoints = []
  
  structure.forEach((item, index) => {
    
    if (item.points && Array.isArray(item.points) && item.points.length > 0) {
      allPoints = allPoints.concat(item.points)
    }
    
    if (item.children && Array.isArray(item.children) && item.children.length > 0) {
      const deeperPoints = extractPointsFromDeepStructure(item.children, maxDepth, currentDepth + 1)
      allPoints = allPoints.concat(deeperPoints)
    }
  })
  
  return allPoints
}

// 处理 pointListList 的知识点数据
const processPointListKnowledgePoints = (points) => {
  if (!points || !Array.isArray(points)) {
    return []
  }
  
  
  // 对于 type=3，可能不需要过滤 type=1，直接处理所有知识点
  return points
    .filter(point => {
      // 确保知识点有有效的ID和名称
      const hasValidId = point.id || point.pointId
      const hasValidName = point.name || point.pointName
      
      if (!hasValidId || !hasValidName) {
        console.warn('跳过无效知识点:', point)
        return false
      }
      
      return true
    })
    .map((point, index) => {
      // 确保状态和isHotspot同步
      const status = point.status !== undefined ? point.status : -1 // 默认为-1（未标记）
      const isHotspot = status === 0 // 只有status为0时，isHotspot才为true
      
      const pointData = {
        id: point.id || point.pointId || `point-${index}`,
        name: point.name || point.pointName || `未命名知识点${index + 1}`,
        isHotspot: isHotspot,
        status: status
      }
      
      console.log(`知识点处理结果:`, pointData)
      
      return pointData
    })
}

// 处理子章节数据
const processSubSections = (subChapters) => {
  if (!subChapters || !Array.isArray(subChapters)) return []
  
  return subChapters.map((subChapter) => {
    return {
      title: subChapter.name || '未命名小节',
      points: processKnowledgePoints(subChapter.points || [])
    }
  })
}

// 处理知识点数据，只保留type=1的知识点
const processKnowledgePoints = (points) => {
  if (!points || !Array.isArray(points)) return []
  
  // 过滤type=1的知识点
  return points
    .filter(point => point.type === 1)
    .map(point => {
      // 确保状态和isHotspot同步
      const status = point.status !== undefined ? point.status : -1 // 默认为-1（未标记）
      const isHotspot = status === 0 // 只有status为0时，isHotspot才为true
      
      return {
        id: point.id || '', // 保留原始ID，不生成新ID
        name: point.name || '未命名知识点',
        isHotspot: isHotspot,
        status: status
      }
    })
}

// 获取状态文本
const getStatusText = (status: number) => {
  switch (status) {
    case 0:
      return '已标记'
    case 1:
      return '已掌握'
    case 2:
      return '未掌握'
    default:
      return '未标记'
  }
}

// 记录所有知识点状态
const logAllPointsStatus = () => {
  // 确保knowledgeSections已初始化
  if (!knowledgeSections.value || knowledgeSections.value.length === 0) {
    return
  }

  knowledgeSections.value.forEach(section => {
    section.subSections.forEach(subSection => {
      // 如果有三级结构，遍历三级结构中的知识点
      if (subSection.thirdLevelSections && subSection.thirdLevelSections.length > 0) {
        subSection.thirdLevelSections.forEach(thirdSection => {
          if (thirdSection.points) {
            thirdSection.points.forEach(point => {
              // console.log(`知识点ID: ${point.id}, 名称: ${point.name}, 状态: ${getStatusText(point.status)}(${point.status}), isHotspot: ${point.isHotspot}`)
            })
          }
        })
      }
      // 如果没有三级结构，遍历二级结构中的知识点
      else if (subSection.points) {
        subSection.points.forEach(point => {
          // console.log(`知识点ID: ${point.id}, 名称: ${point.name}, 状态: ${getStatusText(point.status)}(${point.status}), isHotspot: ${point.isHotspot}`)
        })
      }
    })
  })
}

// 更新知识点状态（支持三级结构）
const updatePointStatus = (pointId: string, newStatus: number) => {
  knowledgeSections.value.forEach(section => {
    section.subSections.forEach(subSection => {
      // 如果有三级结构，在三级结构中查找并更新
      if (subSection.thirdLevelSections && subSection.thirdLevelSections.length > 0) {
        subSection.thirdLevelSections.forEach(thirdSection => {
          if (thirdSection.points) {
            const point = thirdSection.points.find(p => p.id === pointId)
            if (point) {
              point.status = newStatus
              
              // 根据状态更新isHotspot
              if (newStatus === 0) {
                point.isHotspot = true
              } else if (newStatus === -1) {
                point.isHotspot = false
              }
            }
          }
        })
      }
      // 如果没有三级结构，在二级结构中查找并更新
      else if (subSection.points) {
        const point = subSection.points.find(p => p.id === pointId)
        if (point) {
          point.status = newStatus
          
          // 根据状态更新isHotspot
          if (newStatus === 0) {
            point.isHotspot = true
          } else if (newStatus === -1) {
            point.isHotspot = false
          }
        }
      }
    })
  })
  
  // 记录更新后的状态
  logAllPointsStatus()
}

// 更新所有知识点状态（支持三级结构）
const updateAllPointsStatus = (newStatus: number) => {
  knowledgeSections.value.forEach(section => {
    section.subSections.forEach(subSection => {
      // 如果有三级结构，更新三级结构中的知识点
      if (subSection.thirdLevelSections && subSection.thirdLevelSections.length > 0) {
        subSection.thirdLevelSections.forEach(thirdSection => {
          if (thirdSection.points) {
            thirdSection.points.forEach(point => {
              point.status = newStatus
              
              // 根据状态更新isHotspot
              if (newStatus === 0) {
                point.isHotspot = true
              } else if (newStatus === -1) {
                point.isHotspot = false
              }
            })
          }
        })
      }
      // 如果没有三级结构，更新二级结构中的知识点
      else if (subSection.points) {
        subSection.points.forEach(point => {
          point.status = newStatus
          
          // 根据状态更新isHotspot
          if (newStatus === 0) {
            point.isHotspot = true
          } else if (newStatus === -1) {
            point.isHotspot = false
          }
        })
      }
    })
  })
  
  // 记录更新后的状态
  logAllPointsStatus()
}

// 同步所有知识点的isHotspot和status
const syncAllPointsHotspotStatus = () => {
  // 确保knowledgeSections已初始化
  if (!knowledgeSections.value || knowledgeSections.value.length === 0) {
    return
  }
  
  knowledgeSections.value.forEach(section => {
    section.subSections.forEach(subSection => {
      // 如果有三级结构，同步三级结构中的知识点
      if (subSection.thirdLevelSections && subSection.thirdLevelSections.length > 0) {
        subSection.thirdLevelSections.forEach(thirdSection => {
          if (thirdSection.points) {
            thirdSection.points.forEach(point => {
              // 只有status为0时，isHotspot才为true
              point.isHotspot = point.status === 0
            })
          }
        })
      }
      // 如果没有三级结构，同步二级结构中的知识点
      else if (subSection.points) {
        subSection.points.forEach(point => {
          // 只有status为0时，isHotspot才为true
          point.isHotspot = point.status === 0
        })
      }
    })
  })

}

// 检查是否有重复的知识点ID
const checkDuplicatePointIds = () => {
  // 确保knowledgeSections已初始化
  if (!knowledgeSections.value || knowledgeSections.value.length === 0) {
    return false
  }
  
  // 收集所有知识点ID
  const allIds: string[] = []
  const duplicateIds: string[] = []
  
  knowledgeSections.value.forEach(section => {
    section.subSections.forEach(subSection => {
      // 如果有三级结构，检查三级结构中的知识点
      if (subSection.thirdLevelSections && subSection.thirdLevelSections.length > 0) {
        subSection.thirdLevelSections.forEach(thirdSection => {
          if (thirdSection.points) {
            thirdSection.points.forEach(point => {
              if (point.id) {
                if (allIds.includes(point.id)) {
                  duplicateIds.push(point.id)
                } else {
                  allIds.push(point.id)
                }
              }
            })
          }
        })
      }
      // 如果没有三级结构，检查二级结构中的知识点
      else if (subSection.points) {
        subSection.points.forEach(point => {
          if (point.id) {
            if (allIds.includes(point.id)) {
              duplicateIds.push(point.id)
            } else {
              allIds.push(point.id)
            }
          }
        })
      }
    })
  })
  
  if (duplicateIds.length > 0) {
    // console.warn('发现重复的知识点ID:', duplicateIds)
    return true
  } else {
    // console.log('没有发现重复的知识点ID')
    return false
  }
}

// 确保知识点ID唯一性
const ensureUniquePointIds = () => {
  // 确保knowledgeSections已初始化
  if (!knowledgeSections.value || knowledgeSections.value.length === 0) {
    return 0
  }
  
  const idMap = new Map<string, boolean>()
  let duplicateCount = 0
  
  knowledgeSections.value.forEach(section => {
    section.subSections.forEach(subSection => {
      // 如果有三级结构，处理三级结构中的知识点
      if (subSection.thirdLevelSections && subSection.thirdLevelSections.length > 0) {
        subSection.thirdLevelSections.forEach(thirdSection => {
          if (thirdSection.points) {
            thirdSection.points.forEach((point, index) => {
              if (!point.id) {
                // 为没有ID的知识点生成一个唯一ID
                point.id = `generated-${section.id}-${index}-${Date.now()}`
              } else if (idMap.has(point.id)) {
                // 发现重复ID，但不修改，只记录
                duplicateCount++
              }
              
              // 记录此ID已被使用
              idMap.set(point.id, true)
            })
          }
        })
      }
      // 如果没有三级结构，处理二级结构中的知识点
      else if (subSection.points) {
        subSection.points.forEach((point, index) => {
          if (!point.id) {
            // 为没有ID的知识点生成一个唯一ID
            point.id = `generated-${section.id}-${index}-${Date.now()}`
          } else if (idMap.has(point.id)) {
            // 发现重复ID，但不修改，只记录
            duplicateCount++
          }
          
          // 记录此ID已被使用
          idMap.set(point.id, true)
        })
      }
    })
  })
  
  if (duplicateCount > 0) {
    // console.log(`发现${duplicateCount}个重复ID，保留原ID不修改`)
  } else {
    // console.log('所有知识点ID已唯一')
  }
  return duplicateCount
}
</script>

<style scoped>
.knowledge-hotspot-page {
  width: 100%;
  min-height: 100vh;
  background-color: #fff;
  padding: 20px;
}

.rule-notice {
  display: flex;
  align-items: center;
  margin-bottom: 18px;
  padding: 12px 0 12px 0;
  border-radius: 8px;
}

.notice-icon {
  width: 14px;
  height: 14px;
  background-image: url('@/assets/img/percision/standard-help.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  margin-right: 8px;
  flex-shrink: 0;
}

.notice-text {
  font-size: 12px;
  color: rgba(90, 133, 236, 1);
  line-height: 1.4;
}

.knowledge-sections {
  margin-bottom: 80px;
}

.knowledge-section {
  /* background-color: #fff; */
  border-radius: 8px;
  margin-bottom: 16px;
  /* box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); */
  overflow: hidden;
}

/* 一级标题样式 - 不可折叠 */
.section-header {
  padding: 10px 0;
  /* background-color: #f5f7f9;
  border-bottom: 1px solid #eaeaea; */
}

.section-title {
  font-size: 16px;
  font-weight: 700;
  color: #2a2b2a;
  margin: 0;
}

.section-content {
  padding: 0;
}

/* 二级标题样式 */
.sub-section {
  border-bottom: 1px solid #eaeaea;
}

.sub-section:last-child {
  border-bottom: none;
}

.sub-section-header {
  box-sizing: border-box;
  border-bottom: 1px solid rgba(234, 234, 234, 1);
  background: rgba(245, 247, 249, 1);
  padding: 20px 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: background-color 0.3s ease;
}

.sub-section-header:hover {
  background-color: #f8f9fa;
}

.sub-section-title {
  font-size: 16px;
  font-weight: 400;
  color: #2a2b2a;
  margin: 0;
}

.toggle-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  background: url('@/assets/img/percision/standard-down.png');
  background-size: cover;
}

.toggle-icon.expanded {
  background: url('@/assets/img/percision/standard-up.png');
  background-size: cover;
}

.arrow {
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid #fff;
  transition: transform 0.3s ease;
}

.toggle-icon:not(.expanded) .arrow {
  background: url('@/assets/img/percision/standard-up.png');
  background-size: cover;
}

/* 知识点内容区域 */
.sub-section-content {
  /* padding: 20px; */
  /* background-color: #fafbfc; */
  /* margin-bottom: 20px; */
  border-radius: 0;
  overflow: hidden;
}

/* 三级标题区域 */
.third-level-sections {
  width: 100%;
}

.third-level-section {
  border-bottom: 1px solid #f0f0f0;
}

.third-level-section:last-child {
  border-bottom: none;
}

/* 三级标题样式 */
.third-level-header {
  box-sizing: border-box;
  border-bottom: 1px solid rgba(240, 240, 240, 1);
  background: rgba(250, 251, 252, 1);
  padding: 16px 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  transition: background-color 0.3s ease;
}

.third-level-header:hover {
  background-color: #f5f5f5;
}

.third-level-title {
  font-size: 14px;
  font-weight: 400;
  color: #333;
  margin: 0;
}

/* 三级标题内容区域 */
.third-level-content {
  border-radius: 0;
  overflow: hidden;
}

.knowledge-points-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1px;
  background-color: rgba(234, 234, 234, 1);
  padding: 1px;
}

.knowledge-point-item {
  background-color: #fff;
  padding: 16px 20px;
  transition: all 0.3s ease;
  cursor: pointer;
  display: flex;
  align-items: center;
  height: 72px;
  box-sizing: border-box;
  margin: 0;
}

/* .knowledge-point-item:hover { */
  /* border-color: #5a85ec; */
  /* box-shadow: 0 2px 8px rgba(90, 133, 236, 0.1); */
/* } */

.knowledge-point-item.is-hotspot {
  background: rgba(255, 248, 240, 1);
}

.knowledge-point-item.status-marked {
  background: rgba(255, 248, 240, 1);
}

.knowledge-point-item.status-mastered {
  background: rgba(240, 255, 244, 1);
}

.knowledge-point-item.status-unmastered {
  background: rgba(255, 240, 240, 1);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-icon {
  width: 70px;
  height: 34px;
  object-fit: contain;
}

/* 已标记状态 */
.status-marked .status-text {
  color: rgba(254, 129, 62, 1);
}

/* 已掌握状态 */
.status-mastered .status-text {
  color: rgba(82, 196, 26, 1);
}

/* 未掌握状态 */
.status-unmastered .status-text {
  color: rgba(245, 34, 45, 1);
}

.point-content {
  font-size: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.point-name {
  color: #2a2b2a;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 260px;
  font-size: 14px;
}
.understood{
  width: 70px;
  height: 34px;
}
.point-actions {
  display: flex;
  align-items: center;
  gap: 6px;
  flex-shrink: 0;
  min-width: 80px;
  justify-content: flex-end;
}

.mark-btn {
  display: flex;
  align-items: center;
  /* gap: 4px; */
  /* padding: 6px 12px; */
  background: none;
  border: none;
  color: #5A85EC;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.3s ease;
  font-size: 14px;
  white-space: nowrap;
}

.mark-btn:hover {
  background-color: rgba(90, 133, 236, 0.1);
}

.hotspot-indicator {
  display: flex;
  align-items: center;
  gap: 2px;
  white-space: nowrap;
}

.star-icon {
  width: 14px;
  height: 14px;
  background-image: url('@/assets/img/percision/standard-star.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  margin-right: 4px;
}

.star-icon.active {
  width: 20px;
  height: 16px;
  background-image: url('@/assets/img/percision/standard-stared.png');
  background-size: 100% 100%;
}

.status-text {
  font-size: 12px;
  color: rgba(254, 129, 62, 1);
  font-weight: 500;
}

.cancel-btn {
  background: none;
  border: 0;
  color: rgba(153, 153, 153, 1);
  cursor: pointer;
  font-size: 12px;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.cancel-btn:hover {
  background-color: #ff4d4f;
  color: white;
}

.bottom-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 80px;
  /* background-color: #fff; */
  display: flex;
  align-items: center;
  justify-content: center;
  /* box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1); */
}

.action-button {
  width: 253px;
  height: 75px;
  line-height: 60px;
  display: flex;
  /* align-items: center; */
  justify-content: center;
  color: white;
  cursor: pointer;
  transition: all 0.3s ease;
  background: url('@/assets/img/percision/standard-btn.png')center center no-repeat;
  background-size: cover;
}

.action-button.disabled {
  cursor: not-allowed;
  opacity: 0.7;
}

.button-text {
  font-size: 16px;
  font-weight: 500;
}

.empty-points-message {
  padding: 20px;
  text-align: center;
  color: #999;
  font-size: 14px;
  background-color: #f9f9f9;
  border-radius: 0;
  grid-column: 1 / -1;
}

/* 加载遮罩层样式 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #5a85ec;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 15px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式布局 */
@media screen and (max-width: 1200px) {
  .knowledge-points-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1px;
  }
}

@media screen and (max-width: 768px) {
  .knowledge-points-grid {
    grid-template-columns: 1fr;
    gap: 1px;
  }
  
  .point-name {
    max-width: none;
  }
}
.page-header {
  margin-bottom: 18px;
  margin-top: 15px;
}

.breadcrumbs {
  font-size: 14px;
  color: #606266;
  display: flex;
  align-items: center;
}

.breadcrumbs .back-link {
  color: #00bfa5;
  font-size: 16px;
  text-decoration: none;
}
.breadcrumbs .back-link:hover {
  color: #00bfa5;
}

.breadcrumb-separator {
  color: #c0c4cc;
  margin: 0 5px;
}

.breadcrumb-item {
  color: #606266;
}
.breadcrumb-item.active {
  color: #303133;
  font-weight: 500;
}
</style>

