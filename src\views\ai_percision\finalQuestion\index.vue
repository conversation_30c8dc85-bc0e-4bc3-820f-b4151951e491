<!-- 我的积分 -->
<template>
    <div class="content" v-loading="loading">
        <div class="header">
            <div class="header-img">
                <img src="@/assets/img/percision/back.png" alt="back" @click="goBack"></img>
                <img src="@/assets/img/percision/final_text.png" alt="text"></img>
            </div>
            <div class="header-text">
                <template v-if="learnNow.gradeId < 10">{{ subjectObj.subjectName }}{{ subjectObj.editionName }}{{ learnNow.gradeName }}{{ subjectObj.termName }}</template>
                <template v-else>{{ subjectObj.subjectName }}{{ subjectObj.editionName }}{{ subjectObj.typeName }}</template>
            </div>
        </div>
        <div class="main">
            <img class="record-sty" @click="goRecord" src="@/assets/img/percision/record.png" alt="record"></img>
            <div class="main-box">
                <div class="main-box-flex" v-if="options.length > 0">
                    <img class="way-start" src="@/assets/img/percision/final_start.png" alt="start"></img>
                    <div v-for="(item, index) in options">
                        <div v-if="index == 0" class="way-box">
                            <img class="way-sty" src="@/assets/img/percision/way1.png" alt=""></img>
                            <div class="island-box position-right">
                                <img class="way-sty" :src="getIsland(index, item.status)" alt=""></img>
                                <img v-if="item.status == 1" class="way-status" src="@/assets/img/percision/final_success.png" alt=""></img>
                                <img v-if="item.status == 2" class="way-status" src="@/assets/img/percision/final_fail.png" alt=""></img>
                                <div v-if="item.status !== 0" class="way-rate-box">
                                    <img src="@/assets/img/percision/report.png" alt=""></img>
                                    正确率：<span class="way-rate">{{ item.correctRate?.endsWith('.00')?parseInt(item.correctRate):item.correctRate }}</span>%
                                </div>
                                <div class="way-point-box">
                                    <div class="way-point-box-cont">
                                        {{ index + 1 }}. {{ item.pointName }}
                                    </div>
                                    <div v-if="item.status == 0" class="start-btn-box">
                                        <img @click="reChallenge(item)" src="@/assets/img/percision/start-btn.png" alt=""></img>
                                    </div>
                                    <div v-if="item.status == 2" class="way-point-box-btn">
                                        <div @click="reChallenge(item)">再次闯关</div>
                                        <div @click="goLearning(item)"><img src="@/assets/img/percision/play.png" alt=""></img>去学习</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-else-if="index % 2 !== 0" class="way-box">
                            <img class="way-sty" src="@/assets/img/percision/way2.png" alt=""></img>
                            <div class="island-box position-left top50">
                                <img class="way-sty" :src="getIsland(index, item.status)" alt=""></img>
                                <img v-if="item.status == 1" class="way-status" src="@/assets/img/percision/final_success.png" alt=""></img>
                                <img v-if="item.status == 2" class="way-status" src="@/assets/img/percision/final_fail.png" alt=""></img>
                                <img v-if="showChain(item.status, index)" class="way-status-chain" src="@/assets/img/percision/chain.png" alt=""></img>
                                <div v-if="item.status !== 0" class="way-rate-box text-right">
                                    <img src="@/assets/img/percision/report.png" alt=""></img>
                                    正确率：<span class="way-rate">{{ item.correctRate?.endsWith('.00')?parseInt(item.correctRate):item.correctRate }}</span>%
                                </div>
                                <div class="way-point-box">
                                    <div class="way-point-box-cont">
                                        {{ index + 1 }}. {{ item.pointName }}
                                    </div>
                                    <div v-if="showStart(item.status, index)" class="start-btn-box">
                                        <img @click="reChallenge(item)" src="@/assets/img/percision/start-btn.png" alt=""></img>
                                    </div>
                                    <div v-if="item.status == 2" class="way-point-box-btn">
                                        <div @click="reChallenge(item)">再次闯关</div>
                                        <div @click="goLearning(item)"><img src="@/assets/img/percision/play.png" alt=""></img>去学习</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-else-if="index % 2 === 0" class="way-box">
                            <img class="way-sty" src="@/assets/img/percision/way3.png" alt=""></img>
                            <div class="island-box position-right top50">
                                <img class="way-sty" :src="getIsland(index, item.status)" alt=""></img>
                                <img v-if="item.status == 1" class="way-status" src="@/assets/img/percision/final_success.png" alt=""></img>
                                <img v-if="item.status == 2" class="way-status" src="@/assets/img/percision/final_fail.png" alt=""></img>
                                <img v-if="showChain(item.status, index)" class="way-status-chain" src="@/assets/img/percision/chain.png" alt=""></img>
                                <div v-if="item.status !== 0" class="way-rate-box">
                                    <img src="@/assets/img/percision/report.png" alt=""></img>
                                    正确率：<span class="way-rate">{{ item.correctRate?.endsWith('.00')?parseInt(item.correctRate):item.correctRate }}</span>%
                                </div>
                                <div class="way-point-box">
                                    <div class="way-point-box-cont">
                                        {{ index + 1 }}. {{ item.pointName }}
                                    </div>
                                    <div v-if="showStart(item.status, index)" class="start-btn-box">
                                        <img @click="reChallenge(item)" src="@/assets/img/percision/start-btn.png" alt=""></img>
                                    </div>
                                    <div v-if="item.status == 2" class="way-point-box-btn">
                                        <div @click="reChallenge(item)">再次闯关</div>
                                        <div @click="goLearning(item)"><img src="@/assets/img/percision/play.png" alt=""></img>去学习</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="nodata" v-else>
                    <img src="@/assets/img/user/nodata.png" />暂无数据
                </div>
            </div>
        </div>
    </div>
    <PassGifDialog ref="passGifDialog" />
</template>
  
<script lang="ts" setup>
import router from '@/router'
import { dataEncrypt } from '@/utils/secrets'
import PassGifDialog from '@/views/ai_percision/finalQuestion/components/pass_gif_dialog.vue'
import { onMounted, ref } from 'vue'
import { getFinalePointApi } from "@/api/point"
import { useUserStore } from "@/store/modules/user"
import { storeToRefs } from 'pinia'
const passGifDialog = ref()
const loading = ref(false)
const userStore = useUserStore()

let { subjectObj, learnNow,chapterObj } = storeToRefs(userStore)
const options = ref([{id:0}] as any[])
onMounted(() => {
    getPointlist()
})
const getPointlist = async() => { 
    loading.value = true
    const res: any = await  getFinalePointApi({
        bookId: subjectObj.value.bookId
    })
    if(res.code == 200) {
      options.value = res.data || []
      if (res.data.length > 0 && res.data[res.data.length - 1].status == 1) {
        passGifDialog.value.init()
      }
    }
    loading.value = false
}
const showChain = (status: number, index: number) => {
    if (status == 0) {
        if (options.value[index - 1].status == 1) {
            return false
        } else {
            return true
        }
    } else{
        return false
    }
}
const showStart = (status: number, index: number) => {
    if (status == 0) {
        if (options.value[index - 1].status == 1) {
            return true
        } else {
            return false
        }
    } else{
        return false
    }
}
const goBack = () => {
    router.push({
        path: '/ai_percision/knowledge_graph'
    })
}
const getIsland = (index: number, status: number) => {
    const sign = index % 4
    const lastStaus = options.value[index - 1]?options.value[index - 1].status: null
    let img = ""
    if (status == 1 || (lastStaus == 1 && status == 0) || (lastStaus == null && status == 0)) {
        switch (sign) {
            case 0:
                img = "island1.png"
                break
            case 1:
                img = "island2.png"
                break
            case 2:
                img = "island3.png"
                break
            case 3:
                img = "island4.png"
                break
        }
    } else {
        switch (sign) {
            case 0:
                img = "island1_grey.png"
                break
            case 1:
                img = "island2_grey.png"
                break
            case 2:
                img = "island3_grey.png"
                break
            case 3:
                img = "island4_grey.png"
                break
        }
    }
    return new URL(`../../../assets/img/percision/${img}`, import.meta.url).href //静态资源引入为url，相当于require()

}
const reChallenge = (data: any) => { 
    router.push({
        path: '/ai_percision/final_question/final_question_write',
        query: {
            data: dataEncrypt({
                pointId: data.pointId,
                pageSource: '11'
            })
        }
    })
}
const goRecord = () => { 
    router.push({
        path: '/ai_percision/final_question/final__record',
        query: {
            data: dataEncrypt({
                pageSource: '11'
            })
        }
    })
}

const goLearning = (data: any) => { 
    
    router.push({
        path: '/ai_percision/final_question/final__learning',
        // query: {
        //     data: dataEncrypt({
        //         id: data.pointId,
        //         pointName: data.pointName,
        //         source: 'analysis',
        //         subject: subjectObj.value.id
        //     })
        // }
        query: {
            id: data.pointId,
            pointName: data.pointName,
            pageSource: '11',
            source: 'analysis',
            subject: subjectObj.value.id
        }
    })
}
</script>
  
<style lang="scss" scoped>
.content{
    width: 100%;
    height: calc(100vh - 4.375rem);
    background: url(@/assets/img/percision/finalbg.png) no-repeat;
    background-size: 100% calc(100vh - 4.375rem);
    box-sizing: border-box;
    display: flex;
    align-items: center;
    flex-direction: column;
    .header {
        width: 81.25rem;
        height: 5.8125rem;
        display: flex;
        padding-top: 1.875rem;
        box-sizing: border-box;
        justify-content: space-between;
        &-img {
            display: flex;
            align-items: center;
            img:first-child {
                width: 2.75rem;
                height: 2rem;
                margin-right: 1rem;
                cursor: pointer;
            }
            img:last-child {
                width: 12.8125rem;
                height: 3.3125rem;
            }
        }
        &-text {
            height: fit-content;
            border-radius: 1.375rem;
            background: #0000004d;
            color: #ffffff;
            font-size: 1rem;
            font-weight: 400;
            padding: .6875rem 1.25rem;
        }
    }
    .main {
        width: 81.25rem;
        height: calc(100vh - 10.1875rem);
        background: #ffffff;
        padding: 1.25rem 1.25rem 0 1.25rem;
        box-sizing: border-box;
        position: relative;
        .record-sty {
            position: absolute;
            bottom: 3.125rem;
            right: 4.375rem;
            width: 5.4375rem;
            height: 5rem;
            cursor: pointer;
        }
        &-box {
            height: calc(100vh - 11.4375rem);
            width: 100%;
            background: url(@/assets/img/percision/finalbf2.png) no-repeat;
            background-size: 100% 100%;
            display: flex;
            overflow-y: auto;
            justify-content: center;
            .main-box-flex {
                width: 42.75rem;
                margin-top: .625rem;
                padding: 0 4.75rem;
                box-sizing: border-box;
            }
            .way-start {
                width: 6.0625rem;
                height: 4.0625rem;
                margin-left: 1.625rem;
                margin-bottom: -0.3125rem;
            }
            .way-box {
                position: relative;
                .way-sty {
                    width: 33.375rem;
                }
                .island-box {
                    position: absolute;
                    top: 0;
                    .way-sty {
                        width: 12.5rem;
                        height: 12.5rem;
                        position: relative;
                        z-index: 10;
                    }
                    .way-status {
                        position: absolute;
                        top: -3.125rem;
                        left: 0;
                        width: 12.5rem;
                        height: 12.5rem;
                        z-index: 11;
                    }
                    .way-status-chain {
                        position: absolute;
                        width: 11.25rem;
                        left: .5rem;
                        top: 3.75rem;
                        z-index: 49;
                    }
                    .way-rate-box {
                        position: absolute;
                        z-index: 1;
                        width: 11.25rem;
                        top: 5.625rem;
                        right: 9.375rem;
                        background-color: #ffffff;
                        padding: .375rem .75rem;
                        font-size: .875rem;
                        border-radius: 1rem;
                        display: flex;
                        align-items: center;
                        span {
                            font-weight: 700;
                        }
                        img {
                            width: 1rem;
                            height: 1rem;
                            margin-right: .3125rem;
                        }
                    }
                    .text-right {
                        left: 9.375rem;
                        width: 8.125rem;
                        padding-left: 3.75rem;
                    }
                    .way-point-box {
                        position: absolute;
                        top: 8.9375rem;
                        z-index: 20;
                        &-cont {
                            padding: .125rem .75rem;
                            width: 12.5rem;
                            text-align: center;
                            box-sizing: border-box;
                            min-height: 1.875rem;
                            border-radius: .625rem;
                            border: .125rem solid #5a85ec;
                            background: #ffffffcc;
                            color: #323a57;
                        }
                        &-btn {
                            display: flex;
                            justify-content: space-between;
                            margin-top: .375rem;
                            div {
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                width: 5.625rem;
                                cursor: pointer;
                                height: 1.8125rem;
                                border-radius: .9063rem;
                                border: .125rem solid #f25500;
                                background: #e98b00;
                                color: #ffffff;
                                font-size: .875rem;
                                img {
                                    width: 1rem;
                                    height: 1rem;
                                    margin-right: .3125rem;
                                }
                            }
                        }
                    }
                }
                .position-right {
                    right: -6.25rem;
                }
                .position-left {
                    left: -6.25rem;
                }
                .top50 {
                    top: 3.625rem;
                }
            }
        }
    }
}
.start-btn-box {
    width: 12.5rem;
    display: flex;
    justify-content: center;
    margin-top: .375rem;
    img {
        width: 7.125rem;
        height: 2.625rem;
    }
}
.nodata {
    flex: 1;
    color: #999999;
    font-size: .875rem;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-flow: column;
}
</style>
  