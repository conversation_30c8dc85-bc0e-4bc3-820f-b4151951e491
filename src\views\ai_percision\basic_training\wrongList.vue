<!-- 错题本列表 -->
<template>
            <!-- 顶部导航 -->
    <header class="page-header">
      <div class="breadcrumbs">
        <a href="#" class="back-link" @click.prevent="goBack">&lt; 返回</a>
        <span class="breadcrumb-separator">&nbsp;/&nbsp;</span>
        <span class="breadcrumb-item">AI精准学</span>
        <span class="breadcrumb-separator">&nbsp;/&nbsp;</span>
        <span class="breadcrumb-item active" > 错题消化</span>
      </div>
    </header>
  <div class="content">
    <!-- 菜单占位 -->
    <div class="header_seat"></div>
    <div class="inner">
      <div class="wrap">
        <div class="menu_rt ">
          <div class="rt_opt" style="display: flex;text-align: center;">
            
            <div style="align-items: center;justify-content: center;"><span>共 {{ route.query.num }} 个知识点， {{state.data.length}} 道题需要订正</span></div>
            <div class="rt_btns" v-show="state.isBatch" style="margin-left: auto;">           
              <div class="downword" @click="exportNote" data-i="1">
                <img src="@/assets/img/note/down.svg" />下载Word
              </div>
            </div>
            <div class="rt_btns" v-show="!state.isBatch">
              <div class="rt_sel" :class="state.subAll" @click="selAll">
                <img src="@/assets/img/note/check.svg" />
                <img src="@/assets/img/note/checksel.svg" />
                <div>全选</div>
              </div>
              <div class="rt_del" @click="delNoteConfirm">确认删除</div>
              <div class="rt_btn rt_back" @click="multiSel">返回</div>
            </div>
          </div>
          <div class="ques_box" id="quesBox" ref="quesBoxRef" @scroll="handleScroll"
            v-show="state.learnId && state.isShow && state.data.length">
            <!-- 显示复选框  -->
            <div class="ques_ul" :class="!state.isBatch?'show_sel':''">
              <div class="ques_li" v-for="(item,i) in state.data" :key="i">
                <div class="ques_labs">
                  <div class="ques_sel" :class="item.active" :data-i="i" @click="selQues">
                    <img src="@/assets/img/note/check.svg" />
                    <img src="@/assets/img/note/checksel.svg" />
                  </div>
                  <div class="ques_lab hui" v-if="state.type==2">已订正</div>
                  <div class="ques_lab" v-else>未订正</div>
                  <div class="ques_lab">错题来源：{{sourceType[item.source]}}</div>
                  <div class="ques_lab">做错次数：{{item.count}}</div>
                  <div class="ques_lab">错题时间：{{item.updateTime}}</div>
                  <div class="ques_pic" v-if="item.url">
                    <div class="ques_org" @click="lookImg(item.url)">
                      <img src="@/assets/img/note/pic.svg" />查看错题原图
                    </div>
                  </div>
                </div>
                <div class="res_li">
                  <div class="res_circle"></div>
                  <div class="content-row">
                    <span class="index-number">{{ i + 1 }}.</span>
                    <div class="res_stem" v-html="ReplaceMathString(item.ques.content)" :data-i="i" @click="selQues">
                    </div>
                  </div>
                  <div class="opt_ul" v-if="item.ques.cate === 1 || item.ques.cate===3" :data-i="i" @click="selQues">
                    <div class="opt_li" v-for="(item2,index2) in item.ques.options">
                      <div class="opt_num">{{state.subArr[index2]}}.</div>
                      <div class="opt_txt" v-html="ReplaceMathString(item2)"></div>
                    </div>
                  </div>
                  <!-- on显示答案 -->
                  <div class="answer" :class="item.answer?'on':''">
                    <div class="switch_box" @click="togAnswer(i,item)">
                      <div class="switch">
                        <div class="circle"></div>
                      </div>
                      <div class="ans_txt">显示答案与解析</div>
                    </div>
                    <div class="ans_box">
                      <div class="res_item res_item2">
                        <div class="res_h1">【知识点】</div>
                        <div class="res_p">
                          <div class="res_pt" v-html="item.ques.pointName"></div>
                        </div>
                      </div>
                      <div class="res_item" v-if="item.analyseData?.displayAnswer">
                        <div class="res_h1">【答案】</div>
                        <div class="res_p"  v-html="item.analyseData?.displayAnswer"></div>
                      </div>
                      <div class="res_item" v-if="item.analyseData?.analyse">
                        <div class="res_h1">【分析】</div>
                        <div class="res_p" v-html="item.analyseData?.analyse"></div>
                      </div>
                      <div class="res_item" v-if="item.analyseData?.method">
                        <div class="res_h1">【解答】</div>
                        <div class="res_p"  v-html="item.analyseData?.method"></div>
                      </div>
                      <div class="res_item" v-if=" item.analyseData?.discuss">
                        <div class="res_h1">【点评】</div>
                        <div class="res_p"  v-html="item.analyseData?.discuss"></div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="ques_bom">
                  <div class="wkpts">
                    <div class="wkpt" v-for="(item2,index2) in item.ques.pointVos" :data-id="item2.pointId"
                      :data-name="item2.name" :data-i="index2" :data-subject="item.ques.subject"
                      :data-noteid="item.noteId" @click="goWklist">
                      <img src="@/assets/img/note/wkplay.svg" />{{ item2.name }}
                    </div>
                  </div>
                  <!-- <div class="wkline" v-if="item.ques.pointVos"></div> -->
                  <!-- <div class="ques_btn" @click="goTrack(item)">举一反三</div> -->
                </div>
              </div>

            </div>
            <!-- 底部加载 -->
            <div class="pg_loading">
              <div class="pg_load" v-if="state.pgIndex < state.pgPage">
                <img src="@/assets/img/note/loading.svg" class="pg_gif" />
                <div class="pg_tip">正在加载...</div>
              </div>
              <div class="pg_load" v-else>
                <div class="pg_tip">暂无更多</div>
              </div>
            </div>
          </div>
          <!-- <div class="gomake" @click="goOrderTrue" v-if="state.isOrder || state.history >0"> -->
          <div class="gomake" @click="goOrderTrue" v-if="state.data.length >0">
            <img src="@/assets/img/note/gomake.png" />
            <div>去订正</div>
          </div>
          <!-- 无数据 -->
          <div class="nodata" :class="state.learnId && state.isShow && !state.data.length ? '' : 'none'">
            <img src="@/assets/img/note/nodata.png" />错题本空空如也
          </div>
        </div>
      </div>
    </div>
    <!-- 图片预览 -->
    <el-image-viewer @close="state.preview = false" :url-list="state.imgList" v-if="state.preview" />
  </div>
  <!-- 删除弹窗 -->
  <div v-if="state.isQuit">
    <div class="alert_bg"></div>
    <div class="alert_box">
      <div class="alert_inner">
        <div class="alert_top">
          <div class="alert_h1">提示</div>
          <img src="@/assets/img/user/close.svg" class="alert_x" @click="state.isQuit=false" />
        </div>
        <div class="alert_wrap">
          <div class="alert_tit">确定要要删除这些题目吗？</div>
          <div class="alert_btns">
            <div class="alert_quit" @click="state.isQuit=false">取消</div>
            <div class="alert_ok" @click="noteDelete">确定</div>
          </div>
        </div>
      </div>
    </div>
  </div>
    <el-dialog
    :close-on-click-modal="false"
    v-model="dialogVisible"
    title="下载错题"
    width="550"
    :center="true"
    :destroy-on-close="true"
    @close="handlerReset()"
  >
    <div class="warp">
      <el-form :model="formData" label-width="6.25rem" ref="baseForm">
        <!-- <el-form-item label="试卷：" class="paper-label">
          <span class="paper-txt">{{ paperDetail.title }}</span>
        </el-form-item> -->
        <el-form-item label="错题类型：" class="paper-type-label" prop="" style="margin-top: 30px;">
          <el-radio-group v-model="formData.type" class="syu">
            <el-radio :value="0">无答案错题 </el-radio>
            <el-radio :value="2">错题+答案</el-radio>
          </el-radio-group>
        </el-form-item>
        <div class="mt-2" style="text-align: center">
          <el-button @click="handlerReset" style="width: 5.5rem">取消</el-button>

          <el-button type="primary" @click="onSubmit" v-loading="loading" style="width: 120px;">立即下载</el-button>
        </div>
      </el-form>
    </div>
  </el-dialog>
  <!-- 购买会员弹窗 -->
  <buyVip :show="state.showVip" @close="state.showVip = false"></buyVip>
</template>

<script lang="ts" setup>
  import { ref, reactive, onMounted, nextTick, watch, computed } from 'vue';
  import router from '@/router/index'
  import { useRoute } from "vue-router"
  import { useUserStore } from "@/store/modules/user"
  import { ElMessage } from "element-plus"
  import { cn2enList } from '@/utils/user/commonData'
  import { sourceType, subjectList, wenke } from '@/utils/user/enum'
  import { getObjectURL } from '@/utils/download'
  import { getDay, getImageSize, formatDate2, ReplaceMathString } from '@/utils/user/util'
  import { NameOfSubject, KeyValueOfSubject } from '@/utils/user/commonData'
  import { noteTotalApi, noteListApi, exportNoteApi, noteDeleteApi, noteNumsApi, picSearchApi } from "@/api/note"
  import { uploadApi } from "@/api/user"
  import buyVip from "@/views/components/buyVip/index.vue"
  import { setLearnKey } from '@/utils/user/learntime'
  import { dataEncrypt } from '@/utils/secret';
  import { storeToRefs } from 'pinia';
  import { createNoteRedoTrainApi,saveNoteRedoTrainApi } from '@/api/note'
  import { quesGetApi} from "@/api/video"
  import subjectSelect from "@/views/components/subjectSelect/index.vue"
import { fa } from 'element-plus/es/locale';

  defineOptions({
    name: "NoteList"
  })
  const analysis :any = reactive({
    displayAnswer:'b',
    analyse:'',
    method:'',
    discuss:''
  })

  const userStore = useUserStore()
  let { subjectObj } = storeToRefs(userStore)

  const route = useRoute()
  const loading = ref(false)
  const dialogVisible = ref(false)
  const formData : any = reactive({
    type:0
  })

  const learnUsers = JSON.parse(localStorage.learnUsers || "[]") || []
  const state : any = reactive({
    showVip: false,
    drawer: false,
    isBatch: true,
    scrollHeight: 0,
    subjectName: '数学',
    subjectcn: '数学',
    subject: 20,
    deleteIdArr: [],
    pageMainIndex: 0,
    mastery: 0, // 掌握程度
    importance: 0, // 重要程度
    reason: 0, // 错误原因
    source: 0, // 错误来源
    status: 0, // 攻克状态
    type: 1,  //1待清理-未订正，2历史错题-已订正
    beginTime: '', // 错题开始时间
    endTime: '', // 错题结束时间
    wrongCount: 0,
    section: 0,//学段
    timeIndex: 0,
    printIndex: 0,
    userId: '',
    learnId: '',
    isCheck: 0,
    //订正数
    clear: 0,
    history: 0,
    noteTotal: [],
    isOrder: 0,
    //分页
    isShow: 0,
    isFirst: 0,
    isload: 0,
    pgIndex: 1,
    pgSize: 10,
    pgTotal: 0,
    pgPage: 1,
    loading: 0,
    data: [],
    showTop: 0,
    //弹窗
    subAll: '',
    subAlert: false,
    subArr: ['A', 'B', 'C', 'D', 'E', 'F', 'G'],
    subList: [], //用初中
    subI: 0,
    isFilter: false,
    isAnswer: false,
    isExport: false,
    filePath: '',
    title: '',
    reportId: '',
    param: {},
    subNum: 1,
    isQuit: false,
    //搜题
    isHelp: false,
    formdata: {},
    formPic: '',
    //预览
    preview: false,
    imgList: [],
    sourceArr: [],
    //理科错题来源
    sourceArr1: [
      {
        name: '全部',
        active: 'active',
        number: 0
      },
      //需要后端确定
      {
        name: 'AI精准学',
        active: '',
        number: 101
      },
      {
        name: '入门测',
        active: '',
        number: 116
      },
      {
        name: '单元测试',
        active: '',
        number: 104
      },
      {
        name: '在线作业',
        active: '',
        number: 105
      },
      {
        name: '真题试卷',
        active: '',
        number: 106
      },
      {
        name: '难题求助',
        active: '',
        number: 107
      },
      {
        name: '相似题',
        active: '',
        number: 108
      },
      {
        name: '闯关练习',
        active: '',
        number: 109
      },
      {
        name: '其他',
        active: '',
        number: 1024
      }
    ],
    //文科错题来源
    sourceArr2: [
      {
        name: '全部',
        active: 'active',
        number: 0
      },
      {
        name: '基础巩固',
        active: '',
        number: 101
      },
      {
        name: '综合进阶',
        active: '',
        number: 102
      },
      {
        name: '难点突破',
        active: '',
        number: 103
      },
      {
        name: '单元测试',
        active: '',
        number: 104
      },
      {
        name: '在线作业',
        active: '',
        number: 105
      },
      {
        name: '真题试卷',
        active: '',
        number: 106
      },
      {
        name: '难题求助',
        active: '',
        number: 107
      },
      {
        name: '相似题',
        active: '',
        number: 108
      },
      {
        name: '闯关练习',
        active: '',
        number: 109
      },
      {
        name: '其他',
        active: '',
        number: 1024
      }
    ],
    sectionArr: [
      {
        name: '全部',
        active: 'active',
        number: 1
      },
      {
        name: '小学',
        active: '',
        number: 10
      },
      {
        name: '初中',
        active: '',
        number: 20
      },
      {
        name: '高中',
        active: '',
        number: 30
      }
    ],
    wrongArr: [
      {
        name: '全部',
        active: 'active',
        number: 0
      },
      {
        name: '≥5次',
        active: '',
        number: 5
      },
      {
        name: '错4次',
        active: '',
        number: 4
      },
      {
        name: '错3次',
        active: '',
        number: 3
      },
      {
        name: '错2次',
        active: '',
        number: 2
      },
      {
        name: '错1次',
        active: '',
        number: 1
      }
    ],
    timeArr: [
      {
        name: '全部',
        active: 'active',
        number: 0
      },
      {
        name: '今天',
        active: '',
        number: 1
      },
      {
        name: '本周',
        active: '',
        number: 2
      },
      {
        name: '1个月',
        active: '',
        number: 3
      },
      {
        name: '3个月',
        active: '',
        number: 4
      },
      {
        name: '6个月',
        active: '',
        number: 5
      }
    ],
    scrollTop: 0
  })
  //读取缓存
  let subject3 = computed(() => {
    return subjectObj.value.subject
  })
  onMounted(() => {
    console.log(route.query,"queryqueryqueryquery",learnUsers[0].gradeId)
    init()
  })

  //监听路由参数
  watch(
    () => route.query,
    () => {
      setTop(state.scrollTop)
      if (localStorage.isLoad == '1' && route.name == "NoteList") {
        localStorage.removeItem('isLoad')
        init()
      }
    }
  )

  // 属性赋值
  const setData = (obj : any) => {
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        state[key] = obj[key]
      }
    }
  }
  // 自定义返回方法
const goBack = () => {
    router.go(-1)
}

  const init = () => {
    //重置数据
    setData({
      userId: '',
      learnId: '',
      data: [],
      isShow: false,
      subList: NameOfSubject[1].subject
    })

    const userId = localStorage.sysUserId
    if (userId) {
      const learnNow = JSON.parse(localStorage.learnNow || '{}')
      const learnId = learnNow?.learnId || ''
      setData({
        userId,
        learnId
      })
      //无用户返回首页
      if (!learnNow) {
        //新增用户
        router.replace({ path: "../user/user_add", query: { pageType: 'add' } })
        setData({
          isShow: true
        })
        return
      }
      // 判断高中小学科目
      let subI = 0
      const gradeId = Number(learnNow.gradeId)
      if (gradeId < 7) {
        //小学语数英科学
        let subList = state.subList
        const science = {
          img: 'kexue.svg',
          img2: 'kexue2.svg',
          subjectName: '科学',
          subjectValue: 'science3',
          fullName: '小学科学',
          key: '14',
          quantity: 0
        }
        let hasKexue = 0
        for (let i of state.subList) {
          if (i.subjectName == '科学') {
            hasKexue = 1
          }
        }
        if (!hasKexue) {
          subList.splice(6, 0, science)
        }
        subList = subList.filter(function (item : any) {
          const subName = item.subjectName
          let filter2 = false
          if (gradeId == 6) {
            //6年级五四制判断
            filter2 = subName == '生物' || subName == '历史' || subName == '地理' || subName == '政治'
          }
          if (subName == '数学' || subName == '语文' || subName == '英语' || subName == '科学' || filter2) {
            return item
          }
        })
        setData({
          subList
        })
        subI = 0

      } else if (gradeId < 10) {
        subI = 1
      } else {
        subI = 2
      }
      setData({
        learnNow,
        gradeId,
        subI
      })
      setFirstSection()
      getSubAllNum()
      // 改学科组件
      setSub()
    } else {
      setData({
        isShow: true
      })
    }
  }

  // 判断会员状态
  const getIsMember = () => {
    const isVip = useUserStore().memberInfo
    if (isVip) {
      return true
    } else {
      state.showVip = true
      return false
    }
  }

  //获取学科错题总数
  const getSubAllNum = async () => {
    await noteTotalApi().then((res : any) => {
      const data = res.data
      let { subList } = state
      //添加学科中文
      if (data?.items) {
        for (let i of data.items) {
          i.subjectName = subjectList[i.name].text
        }
        for (let x of subList) {
          for (let y of data.items) {
            if (x.subjectName == y.subjectName) {
              x.cleanedNum = y.cleanedNum
              x.redressNum = y.redressNum
            }
          }
        }
      }
      setData({
        noteTotal: data,
        subList
      })
    })
  }
  //判断去订正显示
  const checkOrder = () => {
    let { subList, subject } = state
    let txt = subjectList[KeyValueOfSubject[subject]].text
    let isOrder = 0
    for (let i of subList) {
      if (i.subjectName == txt) {
        isOrder = (i?.cleanedNum || 0) + (i?.redressNum || 0)
      }
    }
    // console.log(subList)
    // console.log(txt)
    // console.log(isOrder)
    setData({
      isOrder
    })
  }

// 提交操作
const onSubmit = async () => {
  loading.value = true
      if (getIsMember()) {
      const { data, subjectcn, subNum, deleteIdArr } = state
      //去掉选中
      for (const n of data) {
        n.active = ''
      }
      setData({
        data,
        // title,
        subAll: '',
        isFilter: false,
        isExport: true,
        isBatch: true
      })
      //获取错题数组
      let noteIds : any = []
      const submitArr = data//deleteIdArr
      for (let i = 0; i < submitArr.length; i++) {
        if (submitArr[i] === submitArr[submitArr.length - 1]) {
          noteIds = noteIds + submitArr[i].noteId
        } else {
          noteIds = noteIds + submitArr[i].noteId + ','
        }
      }
      let param = {
                  // {{ formData.type }}
        type: formData.type,//0不带答案，2：解答
        noteIds: noteIds
      }
      exportNoteApi(param)
        .then((res : any) => {
          if (res.data) {
            ElMessage.success("导出成功")
            const link : any = document.createElement("a");
            link.href = res.data;
            link.setAttribute("download", data.name);
            document.body.appendChild(link);
            link.click();
            link.remove();
            loading.value = false
          } else {
            ElMessage.error("导出失败")
            loading.value = false
          }
        })
    }
  // state.loading = true
  // try {
  //   await baseForm.value.formRef?.validate()
  //   const res = (await downloadReportApi({ id: prop.paperDetail.id, method: formData.type, subject: subjectObj.value.subject })) as any
  //   state.loading = false
  //   if (res.data) {
  //     const link = document.createElement("a")
  //     const body: any = document.querySelector("body")
  //     link.href = res.data
  //     link.style.display = "none"
  //     body.appendChild(link)
  //     link.click()
  //     body.removeChild(link)
  //     state.dialogVisible = false
  //   }
  // } catch (err) {
  //   console.log(err)
  //   state.dialogVisible = false
  //   state.loading = false
  // }
}
  const handlerReset = () => {
    dialogVisible.value = false
  }
  // 切换订正
  const setType = (num : any) => {
    state.type = num
    reLoad()
  }
  //切换学科
  const setSub = () => {
    //匹配学科
    let val = subject3.value
    for (let i of state.subList) {
      let str = i.subjectValue
      if (val.indexOf(str) > -1 || str.indexOf(val) > -1) {
        setData({
          subAlert: false,
          subjectcn: i.subjectName == '政治' && state.subI != 2 ? '道法' : i.subjectName,
          subject: Number(i.key)
        })
        reLoad()
        break
      }
    }
    //错题来源判断文理科
    state.sourceArr = wenke.includes(state.subject) ? state.sourceArr2 : state.sourceArr1
  }

  //加载数据
  const dataLoad = () => {
    const data = state
    const { subject, mastery, importance, reason, source, status, pgIndex, pgSize, type, wrongCount, section, beginTime, endTime, subjectName } = data
    // if (!data.isFirst) {
    //   loading.value = true
    // }
    //记录学习学科
    let subject2 = 0
    if (section == 10) {
      subject2 = cn2enList['小学' + subjectName]
    } else if (section == 20) {
      subject2 = cn2enList['初中' + subjectName]
    } else if (section == 30) {
      subject2 = cn2enList['高中' + subjectName]
    }

    setLearnKey(subject2)
    
    // 根据年级ID判断学段
    let sectionValue = section;
    const gradeId = learnUsers[0]?.gradeId;
    if (gradeId) {
      if (gradeId <= 6) {
        sectionValue = 10; // 小学
      } else if (gradeId > 6 && gradeId <= 9) {
        sectionValue = 20; // 初中
      } else if (gradeId > 9) {
        sectionValue = 30; // 高中
      }
    }
    console.log(route.query.allPointIds,"allPointIdsallPointIdsallPointIdsallPointIds")
    
    const param = {
      subject:route.query.subjectEn?route.query.subjectEn: KeyValueOfSubject[subject],
      source:route.query.source?route.query.source:source,
      type:1,
      pointIds:route.query.allPointIds?route.query.allPointIds:'',
      chapterId:route.query.curChapterId
      // section:1//写死1，返回所有学段
    }

    noteListApi(param, pgIndex, pgSize)
      .then((res : any) => {
        // loading.value = false
        const res2 = res.data
        console.log(res2,"res2res2res2res2")
        const list : any = []

        for (const i of res2.records) {
          i.isOmit = false
          if (
            i.masteryMap?.code === 0 &&
            i.importanceMap?.code === 0 &&
            i.reasonMap?.code === 0 &&
            i.sourceMap?.code === 0
          ) {
            i.isShowNull = true
          } else {
            i.isShowNull = false
          }
          i.active = ''
          //时间戳转日期格式
          if (i.updateTime) {
            i.updateTime = formatDate2(i.updateTime).slice(0, 16)
          }
          list.push(i)
        }
        //分页判断
        if (data.isFirst) {
          setData({
            isShow: 1,
            data: data.data.concat(list),
            isload: 1
          })
        } else {
          setData({
            isFirst: 1,
            isShow: 1,
            data: list,
            pgTotal: Number(res2.total),
            pgPage: Number(res2.pages),
            isload: 1
          })
          //获取订正数
          noteNumsApi(param)
            .then((res3 : any) => {
              const info = res3.data
              setData({
                clear: info.clear,
                history: info.history
              })
            })
        }
        //设置富文本图片宽度等样式
        const listArr = data.data
        for (const i of listArr) {
          getImageSize(i.ques.content).then((richtxt : any) => {
            i.ques.content = richtxt
            setData({
              data: listArr
            })
          })
        }
        checkOrder()
      })
      .catch(() => {
        loading.value = false
        // 确保在API请求失败时也重置isload状态，避免卡在加载中
        setData({
          isload: 1
        })
      })
  }
  const quesBoxRef = ref(null);
  // 滚动节流定时器
  let scrollTimer: any = null;
  // 滚动到底部
  const handleScroll = () => {
    const element : any = quesBoxRef.value;
    if (element) {
      // 记录滚动位置
      setData({
        scrollTop: element.scrollTop
      })
      
      // 添加节流处理，防止频繁触发加载
      if (scrollTimer) return;
      
      scrollTimer = setTimeout(() => {
        // 判断是否滚动到底部
        if (element.scrollHeight - element.scrollTop - element.clientHeight < 50) {
          // 距离底部50px时触发加载
          if (state.isload === 1) { // 确保当前没有加载中的请求
            pageLoad()
          }
        }
        scrollTimer = null;
      }, 200);
    }
  }
  //分页加载
  const pageLoad = () => {
    const { pgIndex, pgPage, isload } = state
    if (pgIndex < pgPage && isload) {
      setData({
        isFirst: 1,
        pgIndex: pgIndex + 1,
        pageMainIndex: 0, // 修改为0，让编号从1开始计算，不受页码影响
        isload: 0,
        loading: 1
      })
      
      // 添加超时处理，确保即使API请求没有响应，也能在5秒后重置加载状态
      const loadTimeout = setTimeout(() => {
        if (state.isload === 0) {
          setData({
            isload: 1,
            loading: 0
          })
        }
        clearTimeout(loadTimeout)
      }, 5000)
      
      dataLoad()
    } else {
      setData({
        loading: 0
      })
    }
  }
  //重新加载
  const reLoad = () => {
    setTop(0)
    setData({
      isFirst: 0,
      pgIndex: 1,
      pageMainIndex: 0, // Reset the page index for numbering
      showTop: 0,
      scrollTop: 0
    })
    dataLoad()
  }

  //返回置顶
  const setTop = (num : any) => {
    const div : any = document.getElementById('quesBox');
    if (div) {
      div.scrollTop = num;
    }
  }

  //显示答案
  const togAnswer = async (i : any ,item:any) => { 
    // 如果已经有数据，直接切换显示状态，不再调用接口
    if (item.analyseData) {
      if (getIsMember()) {
        const list = state.data
        let answer = list[i].answer ? false : true
        state.data[i].answer = answer
      }
      return;
    }
    
    // 没有数据时才调用接口
    loading.value = true
    try {
      await quesGetApi({id:item.ques.quesId}).then((res : any) => {
        if(res.code == 200){
          item.analyseData = res.data
          loading.value = false
        }
      })
    } catch (error) {
      loading.value = false
    }

    if (getIsMember()) {
      const list = state.data
      let answer = list[i].answer ? false : true
      state.data[i].answer = answer
    }
  }

  //预览图片
  const lookImg = (url : any) => {
    const src = url
    state.imgList = [src]
    state.preview = true
    handleClickStop()
  }
  //点击蒙版关闭预览
  const handleClickStop = () => {
    nextTick(() => {
      let domImageView = document.querySelector(".el-image-viewer__mask"); // 获取遮罩层dom
      if (!domImageView) {
        return;
      }
      domImageView.addEventListener("click", () => {
        let close : any = document.querySelector(".el-image-viewer__close")
        close.click();
      });
    });
  }

  // 点击知识点微课
  const goWklist = (e : any) => {
    if (getIsMember()) {
      const { id, subject, noteid } = e.currentTarget.dataset
      if (wenke.includes(Number(subject)) || Number(subject) >= 30) {
        //文科、高中原知识点
        router.push({ name: "NoteWkvideo", query: { pointId: id, type: 'note', subject, noteId: noteid } })
      } else {
        //初中小学优学派
        router.push({ name: "NoteWkvideo2", query: { pointId: id, type: 'note', subject, noteId: noteid } })
      }
    }
  }
  //获取学段对应的学科
  const getSectionKey = () => {
    let { section, subjectName } = state
    let subject = 0
    if (section == 10) {
      subject = cn2enList['小学' + subjectName]
    } else if (section == 20) {
      subject = cn2enList['初中' + subjectName]
    } else if (section == 30) {
      subject = cn2enList['高中' + subjectName]
    }
    return subject
  }

  // 去订正
  const goOrderTrue = () => {
    let noteIds : any = []
    
    // 从 state.data (records) 中收集所有的 noteId
    const list = state.data
    for (let i of list) {
      if (i.noteId) {
        noteIds.push(i.noteId)
      }
    }
    
    // createNoteRedoTrainApi({
    //   noteIds: noteIds
    // }).then((res: any) => {
    //   if (res.data) {
    //     console.log(res.data,"createNoteRedoTrainApicreateNoteRedoTrainApicreateNoteRedoTrainApi")
    //       if (getIsMember()) {
    //         router.push({
    //           name: 'worningLearningAC',
    //           query: {
    //             data: dataEncrypt({
    //               trainingId: res.data,
    //               source: 'wrong'
    //             })
    //           }
    //         })
    //     }
    //   }
    // }).catch((error) => {
    // })

    console.log('🎯 去订正事件 - 收集到的 noteIds:', noteIds)
    console.log('📊 错题详细信息:', list.map(item => ({
      noteId: item.noteId,
      quesTitle: item.ques?.content?.substring(0, 50) + '...' || '未知题目',
      updateTime: item.updateTime,
      count: item.count,
      source: item.source
    })))
    console.log('📈 错题总数:', noteIds.length)


    
    if (getIsMember()) {
      router.push({
        name: 'worningLearningAC',
        query: {
          data: dataEncrypt({
            chapterId: '',// 不用传章节chapterObj.value.chapterId,
            noteId: noteIds,
            pageSource: '10'
          })
        }
      })
    }
  }
  // 举一反三
  const goTrack = (item : any) => {
    if (getIsMember()) {
      router.push({
        name: 'worningLearningAJ',
        query: {
          data: dataEncrypt({
            noteId: item.noteId,
            pageSource: '9'
          })
        }
      })
    }
  }

  //筛选标签
  const tagHide = () => {
    state.drawer = false
  }

  const tagShow = () => {
    state.drawer = true
  }

  // 错题来源
  const setSource = (i : any) => {
    const items = state.sourceArr
    const number = items[i].number
    for (let x of items) {
      x.active = ''
    }
    items[i].active = 'active'
    setData({
      sourceArr: items,
      source: number
    })
  }
  //设置当前学段
  const setFirstSection = () => {
    let { sectionArr, gradeId } = state
    let number = gradeId < 7 ? 10 : gradeId <= 9 ? 20 : 30

    sectionArr[0].number = 1  // 修改为固定值1，表示全部学段

    setData({
      section: 1,
      sectionArr
    })

  }
  //选择学段
  const setSection = (i : any) => {

    const items = state.sectionArr
    const number = items[i].number
    for (let x of items) {
      x.active = ''
    }
    items[i].active = 'active'

    setData({
      sectionArr: items,
      section: number
    })

  }
  // 错题次数
  const setWrong = (i : any) => {
    const items = state.wrongArr
    const number = items[i].number
    for (let x of items) {
      x.active = ''
    }
    items[i].active = 'active'
    setData({
      wrongArr: items,
      wrongCount: number
    })
  }

  // 错题时间
  const setDate = (i : any) => {
    const items = state.timeArr
    const number = items[i].number
    for (let x of items) {
      x.active = ''
    }
    items[i].active = 'active'
    let beginTime = '',
      endTime = ''
    if (number == 1) {
      beginTime = getDay(0)
      endTime = getDay(0)
    } else if (number == 2) {
      beginTime = getDay(-7)
      endTime = getDay(0)
    } else if (number == 3) {
      beginTime = getDay(-30)
      endTime = getDay(0)
    } else if (number == 4) {
      beginTime = getDay(-90)
      endTime = getDay(0)
    } else if (number == 5) {
      beginTime = getDay(-180)
      endTime = getDay(0)
    }
    setData({
      timeArr: items,
      beginTime,
      endTime,
    })
  }

  // 筛选-清空
  const tagQuit = () => {
    const { sourceArr, sectionArr, wrongArr, timeArr } = state
    for (let x of sourceArr) {
      x.active = ''
    }
    for (let x of sectionArr) {
      x.active = ''
    }
    for (let x of wrongArr) {
      x.active = ''
    }
    for (let x of timeArr) {
      x.active = ''
    }
    sourceArr[0].active = 'active'
    sectionArr[0].active = 'active'
    sectionArr[0].active = 'active'
    wrongArr[0].active = 'active'
    timeArr[0].active = 'active'
    setData({
      sourceArr,
      sectionArr,
      wrongArr,
      timeArr,
      source: 0,
      section: 1,  // 修改为1，表示全部学段
      wrongCount: 0,
      beginTime: '',
      endTime: ''
    })
    setFirstSection()
    reLoad()
  }
  // 筛选-确定
  const tagOk = () => {
    tagHide()

    reLoad()
  }

  // 全选
  const selAll = () => {
    const data = state.data,
      deleteIdArr : any = []
    let subAll = state.subAll
    if (subAll) {
      //反选
      subAll = ''
      for (const i of data) {
        i.active = ''
      }
    } else {
      //全选
      subAll = 'active'
      for (const i of data) {
        i.active = 'active'
        deleteIdArr.push(i.noteId)
      }
    }
    setData({
      data,
      subAll,
      deleteIdArr
    })
  }

  // 批量管理
  const multiSel = () => {
    const data = state.data
    for (const i of data) {
      i.isOmit = false
      i.active = ''
    }
    setData({
      subAlert: false,
      isBatch: !state.isBatch,
      subAll: '',
      deleteIdArr: [],
      data: data
    })
  }

  //点击题干
  const selQues = (e : any) => {
    if (state.isBatch) {

    } else {
      chooseNote(e)
    }
  }

  //批量管理-点击选题
  const chooseNote = (e : any) => {
    const i = e.currentTarget.dataset.i,
      data = state.data
    let active = data[i].active
    if (data[i].active) {
      active = ''
    } else {
      active = 'active'
    }
    data[i].active = active
    //获取选中数组
    const ids : any = []
    for (const y of data) {
      if (y.active) {
        ids.push(y.noteId)
      }
    }
    setData({
      data,
      deleteIdArr: ids
    })
  }

  // 批量删除按钮
  const delNoteConfirm = (e : any) => {
    const data = state
    if (!data.deleteIdArr.length) {
      ElMessage.error('请选择您想要删除的错题')
      return
    }
    state.isQuit = true
  }

  // 删除选中题目
  const noteDelete = (e : any) => {
    let noteIds : any = ''
    const submitArr = state.deleteIdArr
    for (let i = 0; i < submitArr.length; i++) {
      if (submitArr[i] === submitArr[submitArr.length - 1]) {
        noteIds = noteIds + submitArr[i]
      } else {
        noteIds = noteIds + submitArr[i] + ','
      }
    }
    //保留未删除
    const data = state,
      list : any = []
    for (const i of data.data) {
      let isDel = 0
      for (const n of submitArr) {
        if (i.noteId == n) {
          isDel = 1
        }
      }
      if (!isDel) {
        list.push(i)
      }
    }
    //本地删除
    const pgTotal = data.pgTotal - submitArr.length
    setData({
      subAll: '',
      deleteIdArr: [],
      data: list,
      pgTotal,
      isBatch: true,
      isQuit: false
    })
    noteIds = noteIds.split(',')
    let param = {
      noteIds
    }
    noteDeleteApi(param).then(() => {
      reLoad()
      //如果全删了，重新加载(有可能存在删除，分页加载问题)
      // if (list.length == 0) {
      //   // setTimeout(() => {
      //   reLoad()
      //   // }, 1000)
      // }
    })
  }

  // 导出错题
  const exportNote = () => {
    dialogVisible.value=true
    return;
    if (getIsMember()) {
      const { data, subjectcn, subNum, deleteIdArr } = state
      //去掉选中
      for (const n of data) {
        n.active = ''
      }
      setData({
        data,
        // title,
        subAll: '',
        isFilter: false,
        isExport: true,
        isBatch: true
      })
      //获取错题数组
      let noteIds : any = []
      const submitArr = data//deleteIdArr
      for (let i = 0; i < submitArr.length; i++) {
        if (submitArr[i] === submitArr[submitArr.length - 1]) {
          noteIds = noteIds + submitArr[i].noteId
        } else {
          noteIds = noteIds + submitArr[i].noteId + ','
        }
      }
      let param = {
        type: 0,//0不带答案，2：解答
        noteIds: noteIds
      }
      exportNoteApi(param)
        .then((res : any) => {
          if (res.data) {
            ElMessage.success("导出成功")
            const link : any = document.createElement("a");
            link.href = res.data;
            link.setAttribute("download", data.name);
            document.body.appendChild(link);
            link.click();
            link.remove();
          } else {
            ElMessage.error("导出失败")
          }
        })
    }
  }

  // 显示搜题弹窗
  const helpShow = () => {
    state.isHelp = true
  }
  //关闭搜题弹窗
  const helpHide = () => {
    state.isHelp = false
    let fileInput : any = document.getElementById('file');
    fileInput.value = null;
    state.formdata = {}
    state.formPic = ''
  }
  // 搜题
  const picSearch = () => {
    if (getIsMember()) {
      loading.value = true
      picSearchApi(state.formdata)
        .then((res : any) => {
          loading.value = false
          //缓存搜题结果和图片
          const data = res.data || []
          if (data.length) {
            //上传图片
            uploadApi(state.formdata)
              .then((res2 : any) => {
                const data2 = res2.data
                const url = data2.url + data2.key
                localStorage.searchPicData = JSON.stringify(data)
                localStorage.searchPic = url
                helpHide()
                router.push({ name: "NoteHelp" })
                localStorage.isLoad = 1
                //解决重复图片不能上传
                let fileInput : any = document.getElementById('file');
                fileInput.value = null;
              })
          }
        }).catch(() => {
          loading.value = false
        })
    }
  }
  //上传图片
  async function uploadFiles(e : any) {
    let file = e.target.files[0]
    if (changeImg(file)) {
      //修改文件名
      const type = file.type.split('/')
      const file2 = new File([file], 'web_user_' + new Date().getTime() + Math.floor(Math.random() * 10001) + '.' + type[1], { type: file.type });
      const formdata : any = new FormData()
      formdata.append("file", file2)
      state.formdata = formdata
      state.formPic = getObjectURL(file)
    }
  }
  //限制图片大小和格式
  const changeImg = (file : any) => {
    const size = 1024 * 10
    const limit = (file?.size || 0) / 1024
    const isLtM = limit < size
    let msg = "上传图片大小不能超过" + size + "KB"
    const type = file.type
    const isType = type == "image/png" || type == "image/jpg" || type == "image/jpeg"
    if (!isType) {
      msg = "上传图片只支持png,jpg,jpeg格式"
    }
    if (!isLtM || !isType) {
      //解决重复图片不能上传
      let fileInput : any = document.getElementById('file');
      fileInput.value = null;
      ElMessage.error(msg)
      return false
    } else {
      return true
    }
  }
</script>

<style lang="scss" scoped>
.page-header {
  margin-bottom: 18px;
  margin-top: 10px;
}

.breadcrumbs {
  font-size: 14px;
  color: #606266;
  display: flex;
  align-items: center;
}

.breadcrumbs .back-link {
  color: #00bfa5;
  font-size: 16px;
  text-decoration: none;
}
.breadcrumbs .back-link:hover {
  color: #00bfa5;
}

.breadcrumb-separator {
  color: #c0c4cc;
  margin: 0 5px;
}

.breadcrumb-item {
  color: #606266;
}
.breadcrumb-item.active {
  color: #303133;
  font-weight: 500;
}
  @import url('@/assets/styles/reset.css');
  .none {
    display: none !important
  }

  .nowrap {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .content {
    width: 100%;
    background: #F5F5F5;
    flex: 1;
  }

  .inner {
    margin: 0 auto;
    width: 81.25rem;
  }

  .wrap {
    float: left;
    width: 100%;
    /* height: calc(100vh - 7.4375rem);
    overflow-y: auto; */
  }



  /* 求助 */
  .help {
    /* 原 */
    position: relative;
    top: 13.875rem;
    left: 8.125rem;
    z-index: 10;
    margin: -13.25rem 0 0;
    /* position: absolute;
    top: 13.875rem;
    right: 8.125rem;
    z-index: 10; */
  }

  .help:hover {
    cursor: pointer;
  }

  .help,
  .help img {
    float: right;
    width: 7.5rem;
    height: 13.25rem;
    border-radius: .625rem;
  }

  .help div {
    float: left;
    width: 100%;
    line-height: 1rem;
    text-align: center;
    color: #009c7f;
    font-size: .75rem;
    margin: -2.25rem 0 0;
    position: relative;
    z-index: 2;
  }

  /* 学科 */
  .wrap>div {
    box-sizing: border-box;
    height: calc(100vh - 10.75rem);
    margin: -0.1875rem 0 0;
  }

  .menu_lt {
    float: left;
    width: 8.875rem;
    padding: 2.5rem .875rem 0;
    border-radius: 1.25rem 1.25rem 0 0;
    height: calc(100vh - 11rem);
    overflow-y: auto;
    background: #fff;
    border: .0625rem solid #eaeaea;
  }

  .menu_lt div {
    float: left;
  }

  .sub_ul {
    width: 100%;
  }

  .sub_li {
    width: 100%;
    height: 3.375rem;
    font-size: 1rem;
    border-radius: .25rem;
    margin: 0 0 1.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .sub_li img {
    width: 1.375rem;
    height: 1.375rem;
    margin: 0 .5rem 0 0;
  }

  .sub_li.active {
    color: #009C7F;
    background: #f1f7f6;
  }

  .sub_li:hover {
    cursor: pointer;
    background: #f5f5f5;
  }

  .sub_li img:nth-child(1),
  .sub_li.active img:nth-child(2) {
    display: inline-block;
  }

  .sub_li img:nth-child(2),
  .sub_li.active img:nth-child(1) {
    display: none;
  }

  /* tab */
  .menu_rt {
    display: flex;
    flex-flow: column;
  }

  .rt_opt {
    float: left;
    width: 100%;
    height: 4.5rem;
    border: .0625rem solid #eaeaea;
    box-sizing: border-box;
    background: #fff;
    border-radius: 1.25rem 1.25rem 0 0;
    padding: 1.125rem 1.875rem 0 2.125rem;
    margin: 0 0 .625rem;
  }

  .rt_tabs {
    float: left;
  }

  .rt_tab {
    float: left;
    display: flex;
    align-items: center;
    flex-flow: column;
    margin: 0 2.5rem 0 0;
  }

  .rt_tab:hover {
    cursor: pointer;
  }

  .rt_txt {
    line-height: 1.3125rem;
    color: #2a2b2a;
    font-size: 1rem;
    padding: .4375rem 0;
  }

  .rt_line {
    width: 1.875rem;
    height: .25rem;
    border-radius: .125rem;
    background: #00c9a3;
    opacity: 0;
  }

  .rt_tab.active .rt_line {
    opacity: 1;
  }

  .rt_btns {
    float: right;
  }

  .rt_btn {
    float: left;
    width: 6.875rem;
    height: 2.25rem;
    line-height: 2.125rem;
    color: #666666;
    font-size: 1rem;
    text-align: center;
    border-radius: .25rem;
    border: .0625rem solid #eaeaea;
    background: #ffffff;
    box-sizing: border-box;
    margin: 0 1.25rem 0 0;
  }

  .rt_btn:hover {
    cursor: pointer;
    background: #EAEAEA;
  }

  .downword {
    float: right;
    line-height: 2.25rem;
    color: #009c7f;
    font-size: .875rem;
    margin: 0 0 0 1.875rem;
  }

  .downword:hover {
    cursor: pointer;
  }

  .downword img {
    width: .875rem;
    height: .875rem;
  }

  /* 全选 */
  .rt_sel {
    float: left;
    display: flex;
    align-items: center;
    margin: 0 1.25rem 0 0;
  }

  .rt_sel:hover {
    cursor: pointer;
  }

  .rt_sel div {
    float: left;
    color: #666666;
    font-size: .875rem;
    line-height: 2.25rem;
  }

  .rt_sel img {
    float: left;
    width: 1.25rem;
    height: 1.25rem;
    margin: 0 .625rem 0 0;
  }

  .rt_sel img:nth-child(1),
  .rt_sel.active img:nth-child(2) {
    display: inline-block;
  }

  .rt_sel img:nth-child(2),
  .rt_sel.active img:nth-child(1) {
    display: none;
  }

  .rt_del {
    float: left;
    width: 6.875rem;
    height: 2.25rem;
    line-height: 2.25rem;
    color: #DD2A2A;
    font-size: 1rem;
    text-align: center;
    border-radius: .25rem;
    background: rgba(221, 42, 42, 0.1);
    box-sizing: border-box;
    margin: 0 1.25rem 0 0;
  }

  .rt_del:hover {
    cursor: pointer;
  }

  .rt_back {
    margin-right: 8.4375rem;
  }

  /* 列表 */
  .ques_box {
    float: left;
    width: 100%;
    flex: 1;
    overflow-y: auto;
  }

  .ques_ul {
    float: left;
    width: 100%;
  }

  .ques_li {
    float: left;
    width: 100%;
    background: #fff;
    box-sizing: border-box;
    border: .0625rem solid #EAEAEA;
    margin: 0 0 .625rem;
  }

  .ques_labs {
    float: left;
    width: 100%;
    margin: 1.25rem 0 .625rem;
    box-sizing: border-box;
    padding: 0 0 0 1.875rem;
  }

  .ques_lab {
    float: left;
    height: 1.75rem;
    line-height: 1.625rem;
    color: #ef9d19;
    font-size: .75rem;
    border-radius: .875rem;
    background: #fef8e9;
    padding: 0 .75rem;
    margin: 0 .75rem 0 0;
    border: .0625rem solid #fef8e9;
    box-sizing: border-box;
  }

  .ques_lab.hui {
    color: #666;
    border: .0625rem solid #eaeaea;
    background: #f5f5f5;
  }

  .ques_sel {
    float: left;
    margin: 0 1.25rem 0 0;
    display: none;
  }

  .show_sel .ques_sel {
    display: inline-block;
  }

  .ques_sel img {
    float: left;
    width: 1.25rem;
    height: 1.25rem;
    margin: .25rem 0 0;
  }

  .ques_sel img:nth-child(1),
  .ques_sel.active img:nth-child(2) {
    display: inline-block;
  }

  .ques_sel img:nth-child(2),
  .ques_sel.active img:nth-child(1) {
    display: none;
  }



  .ques_sel:hover {
    cursor: pointer;
  }

  .ques_pic {
    float: left;
    cursor: pointer;
  }

  .ques_org {
    float: left;
    width: 7.25rem;
    line-height: 1.75rem;
    border-radius: 1.7813rem;
    background: linear-gradient(147.5deg, #36e2c2 0%, #00b7d0 100%);
    color: #ffffff;
    font-size: .75rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .ques_org img {
    width: .875rem;
    height: .75rem;
    margin: 0 .375rem 0 0;
  }

  .ques_img {
    float: left;
    width: 7.25rem;
    height: 1.75rem;
    background: red;
    position: relative;
    z-index: 2;
    margin: 0 0 0 -7.25rem;
    // opacity: 0;
  }

  /* 题干 */
  .res_li {
    float: left;
    width: 100%;
    box-sizing: border-box;
  }

  .res_circle {
    float: left;
    width: .875rem;
    height: 1rem;
    border-radius: 0 .375rem .375rem 0;
    background: #5a85ec;
    position: relative;
    top: .25rem;
  }

  .content-row {
    display: flex;
    width: calc(100% - 1.875rem);
    margin-left: 1rem;
    align-items: flex-start;
  }

  .index-number {
    // min-width: 2.5rem;
    // color: #009c7f;
    // font-size: 1rem;
    // font-weight: 600;
    line-height: 1.2;
    margin-right: 0.5rem;
    // padding-top: 0.125rem;
    height: 20px;
    flex-shrink: 0;
  }

  .res_stem {
    flex: 1;
    color: #2a2b2a;
    font-size: .875rem;
    line-height: 1.5;
    box-sizing: border-box;
    padding-right: 1.875rem;
  }

  .opt_ul {
    float: left;
    width: 100%;
    box-sizing: border-box;
    padding: 0 1.875rem;
    display: flex;
    align-items: center;
    margin: 1.25rem 0 -0.625rem;
  }

  .opt_li {
    float: left;
    margin: 0 3.125rem 1.875rem 0;
    display: flex;
    align-items: center;
  }

  .opt_num {
    float: left;
    color: #2a2b2a;
    font-size: .875rem;
    // font-weight: 700;
    line-height: 1.5;
    margin: 0 .3125rem 0 0;
  }

  .opt_txt {
    float: left;
    color: #2a2b2a;
    font-size: .875rem;
    line-height: 1.5;
  }

  .answer {
    float: left;
    width: 100%;
    background: #fef8e9;
    box-sizing: border-box;
    padding: 0 1.8125rem;
    border-top: .0625rem dashed #F5F5F5;
    margin: .625rem 0 0;
  }

  /* 开关 */
  .switch_box {
    float: left;
    margin: .625rem 0;
  }

  .switch_box:hover {
    cursor: pointer;
  }

  .switch {
    float: left;
    margin: 0 0 0 auto;
    width: 1.875rem;
    height: .9375rem;
    border-radius: .8438rem;
    background: #999;
    transition: background 0.1s, border 0.1s;
  }

  .circle {
    width: .6875rem;
    height: .6875rem;
    background: #fff;
    box-shadow: 0 .0625rem .1875rem rgba(0, 0, 0, 0.4);
    border-radius: 50%;
    transform: translateX(0);
    transition: transform 0.15s linear;
    position: relative;
    top: .125rem;
    left: .125rem;
  }

  .on .switch {
    border-color: #5a85ec;
    background: #5a85ec;
  }

  .on .switch .circle {
    transform: translateX(.9375rem);
  }

  .ans_txt {
    float: left;
    line-height: .9375rem;
    color: #666666;
    font-size: .75rem;
    margin: 0 0 0 .5rem;
  }

  /* 答案 */
  .ans_box {
    float: left;
    width: 100%;
    mix-blend-mode: multiply;
  }

  .res_item {
    display: none;
    float: left;
    width: 100%;
    box-sizing: border-box;
    margin: 0 0 .625rem;
  }

  .on .res_item {
    display: block;
  }

  .res_item:first-child {
    margin-top: .4375rem;
  }

  .res_item>div {
    float: left;
    color: #2a2b2a;
    font-size: .875rem;
    line-height: 1.625rem;
  }

  .res_h1 {
    width: 4.125rem;
    font-weight: 700;
  }

  .res_p {
    width: calc(100% - 4.125rem);
  }

  /* 知识点 */
  .res_item2 .res_h1 {
    width: 5rem;
  }

  .res_item2 .res_p {
    width: calc(100% - 5rem);
  }

  .res_item2 div {
    float: left;
  }

  .res_pt {
    margin: 0 .625rem 0 0;
  }

  /* 举一反三 */
  .ques_bom {
    float: left;
    width: 100%;
    box-sizing: border-box;
    padding: 1.25rem 1.875rem .625rem;
  }

  .wkpts {
    float: left;
    width: calc(100% - 8.0625rem);
  }

  .wkpt {
    float: left;
    line-height: 1.9375rem;
    border-radius: .9688rem;
    background: #f5f5f5;
    padding: 0 .75rem;
    color: #009c7f;
    font-size: .875rem;
    display: flex;
    align-items: center;
    margin: 0 .625rem .625rem 0;
    cursor: pointer;
  }

  .wkpt img {
    width: 1rem;
    height: 1rem;
    border-radius: .5rem;
    background: #00c9a3;
    margin: 0 .375rem 0 0;
  }

  .wkline {
    float: left;
    width: .0625rem;
    height: 1.9375rem;
    background: #a4a4a4;
  }

  .ques_btn {
    float: right;
    width: 6.125rem;
    line-height: 1.9375rem;
    border-radius: .25rem;
    background: #00c9a3;
    color: #ffffff;
    font-size: .875rem;
    text-align: center;
    cursor: pointer;
  }

  .pg_loading {
    float: left;
    width: 100%;
    margin: 0 0 4.0625rem;
  }

  /* 去订正 */
  .gomake {
    float: left;
    width: 15.8125rem;
    border-radius: 1.4688rem;
    text-align: center;
    position: absolute;
    left: calc(50% - 7.9375rem);
    bottom: 0rem;
  }

  .gomake img {
    float: left;
    width: 15.8125rem;
  }

  .gomake div {
    float: left;
    line-height: 4.6875rem;
    color: #ffffff;
    font-size: 1.125rem;
    position: relative;
    z-index: 2;
    margin: -5.3125rem 0 0;
    width: 100%;
  }

  .gomake:hover {
    cursor: pointer;
  }

  /* 暂无数据 */
  .nodata {
    flex: 1;
    color: #999999;
    font-size: .875rem;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-flow: column;
    background: #fff;
  }

  .nodata img {
    width: 7.4375rem;
    height: 8rem;
    margin: 0 0 .625rem;
  }

  /* 标签弹窗 */
  .tag_box {
    width: 35rem;
    height: 100vh;
    display: flex;
    flex-flow: column;
    position: fixed;
    top: 0;
    right: 0;
    z-index: 8888;
    background: #fff;
  }

  .tag_box div {
    float: left;
  }

  .tag_wrap {
    flex: 1;
    overflow-y: auto;
  }

  .tag_h1 {
    width: 100%;
    line-height: 1.3125rem;
    color: #2a2b2a;
    font-size: 1rem;
    font-weight: 700;
    margin: 3.125rem 0 1.25rem;
  }

  .tag_h1 div {
    width: .875rem;
    height: 1rem;
    border-radius: 0 .375rem .375rem 0;
    background: #5a85ec;
    margin: .25rem 2.25rem 0 0;
  }

  .tag_ul {
    width: 100%;
    box-sizing: border-box;
    padding: 0 0 0 3.125rem;
    margin: 0 0 -1.25rem;
  }

  .tag_li {
    width: 6.25rem;
    height: 3.125rem;
    line-height: 3.125rem;
    text-align: center;
    border-radius: .25rem;
    color: #2a2b2a;
    font-size: .875rem;
    border: .0625rem solid #dddddd;
    background: #ffffff;
    margin: 0 1.25rem 1.25rem 0;
    box-sizing: border-box;
  }

  .tag_li:hover {
    cursor: pointer;
  }

  .tag_li:nth-child(4n+4) {
    margin: 0 0 1.25rem 0;
  }

  .tag_li.active {
    color: #009c7f;
    border-color: #00c9a3;
    background: #e5f9f6;
  }

  .tag_btns {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 1.25rem 0 3.75rem;
  }

  .tag_btns div {
    cursor: pointer;
  }

  .tag_quit {
    width: 7.625rem;
    line-height: 2.375rem;
    border-radius: 1.1875rem;
    background: #f5f5f5;
    color: #666666;
    font-size: 1rem;
    text-align: center;
    margin: 0 2.125rem 0 0;
  }

  .tag_ok {
    width: 7.625rem;
    line-height: 2.375rem;
    border-radius: 1.1875rem;
    background: #00c9a3;
    color: #ffffff;
    font-size: 1rem;
    text-align: center;
  }

  /* 搜题弹窗 */
  .st_bg {
    z-index: 999;
    background: rgba(0, 0, 0, .7);
    position: fixed;
    width: 100%;
    height: 100vh;
    top: 0;
    left: 0;
  }

  .st_box {
    z-index: 1000;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
  }

  .st_inner {
    width: 63.25rem;
    border-radius: 1.25rem;
    background: #ffffff;
    box-shadow: 0 .125rem 1.25rem 0 #00000040;
    box-sizing: border-box;
    padding: 1.25rem 1.25rem 1.875rem;
  }

  .st_pic {
    width: 100%;
    height: 12.75rem;
    border-radius: .625rem;
    border: .0625rem dashed #00c9a3;
    background: #f3fffd;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1.25rem;
    color: #999999;
    font-size: .875rem;
  }

  .st_pic:hover {
    cursor: pointer;
  }

  .st_pic img {
    max-width: 100%;
    max-height: 100%;
  }

  .st_upload {
    float: left;
    width: 100%;
    margin: .625rem 0 3.125rem;
  }

  .st_img {
    float: left;
    color: #009c7f;
    font-size: .875rem;
    line-height: 1rem;
    display: flex;
    align-items: center;
  }

  .st_img:hover {
    cursor: pointer;
  }

  .st_img img {
    width: 1rem;
    height: 1rem;
    margin: 0 .375rem 0 0;
  }

  .st_tip {
    float: left;
    line-height: 1rem;
    color: #999999;
    font-size: .875rem;
  }

  .file {
    float: left;
    width: 100%;
    height: 12.75rem;
    margin: -12.75rem 0 0;
    position: relative;
    z-index: 2;
    opacity: 0;
    cursor: pointer;
  }

  .st_btns {
    width: 100%;
    display: flex;
    justify-content: center;
  }

  .st_btns div {
    width: 7.625rem;
    line-height: 2.375rem;
    font-size: 1rem;
    border-radius: 1.1875rem;
    text-align: center;
  }

  .st_btns div:hover {
    cursor: pointer;
  }

  .st_quit {
    color: #666666;
    background: #f5f5f5;
  }

  .st_ok {
    margin: 0 0 0 2.125rem;
    color: #fff;
    background: linear-gradient(150.8deg, #36e2c2 0%, #00b7d0 100%);
  }

  /* 删除弹窗 */
  .alert_bg {
    z-index: 50;
    background: rgba(0, 0, 0, .7);
    position: fixed;
    width: 100%;
    height: 100vh;
    top: 0;
    left: 0;
  }

  .alert_box {
    z-index: 99;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .alert_inner {
    width: 31.25rem;
    border-radius: 1.25rem;
    background: #ffffff;
    box-sizing: border-box;
    padding: 0 1.25rem;
  }

  .alert_top {
    float: left;
    width: 100%;
    height: 3.375rem;
    border-bottom: .0625rem solid #eee;
  }

  .alert_h1 {
    float: left;
    width: 100%;
    line-height: 3.375rem;
    text-align: center;
    color: #2a2b2a;
    font-size: 1rem;
    font-weight: bold;
  }

  .alert_x {
    float: right;
    width: .9375rem;
    height: .9375rem;
    padding: 1.1875rem;
    margin: -3.125rem -1.125rem 0 0;
  }

  .alert_x:hover {
    cursor: pointer;
  }

  .alert_wrap {
    width: 100%;
    float: left;
    display: flex;
    align-items: center;
    flex-flow: column;
  }

  .alert_tit {
    line-height: 1.3125rem;
    color: #2a2b2a;
    font-size: 1rem;
    margin: 2.4375rem 0 1rem;
  }

  .alert_btns {
    display: flex;
    margin: 1.25rem 0 2.1875rem;
  }

  .alert_btns div {
    width: 7.625rem;
    line-height: 2.375rem;
    text-align: center;
    border-radius: 1.1875rem;
    font-size: 1rem;
  }

  .alert_btns div:hover {
    cursor: pointer;
  }

  .alert_quit {
    color: #666666;
    background: #f5f5f5;
  }

  .alert_ok {
    color: #DD2A2A;
    background: #fee9e9;
    margin: 0 0 0 2.125rem;
  }

  :deep(.subject-container) {
    width: 100%;
    overflow-x: hidden;
    height: auto;
    flex: 1;
  }
  .paper-txt {
  display: inline;
  color: rgb(0, 0, 0);
  font-size: .875rem;
  font-weight: 600;
}
:deep(.el-radio.is-checked .el-radio__label) {
  color: rgb(0, 0, 0);
  font-size: .875rem;
  font-weight: 600;
}
:deep(.el-radio__label) {
  font-size: .875rem;
  font-weight: 400;
  color: rgb(77, 77, 77);
}
:deep(.el-form-item__label) {
  color: rgb(77, 77, 77);
  font-size: .875rem;
}
.paper-type-label {
  display: flex;
}
.syu {
  display: grid;
}

</style>
