<!-- 知识点-知识点视频-章节进入 -->
<template>
  <div class="content" v-loading.fullscreen="state.loading">
    <div class="inner">
      <div class="wrap">
        <div class="menu_lt">
          <div class="wk_box">
            <div class="wk_tit">
              <div class="wk_p" v-html="ReplaceMathString(state.vidInfo.videoName3||state.vidInfo.videoName)"></div>
              <template v-if="state.vidType==1">
                <div class="wk_status status2" v-if="state.vidInfo.studyStatus==2">已学完</div>
                <div class="wk_status status1" v-else-if="state.vidInfo.studyStatus==1">未学完</div>
                <div class="wk_status status0" v-else>未学习</div>
              </template>
            </div>
            <!-- 播放器 -->
            <div id="dplayer1" class="wk_video" v-show="state.videoUrl|| state.videoList.length"></div>
            <div class="wk_nodata" v-show="!(state.videoUrl|| state.videoList.length)&&state.isShow">
              <img src="@/assets/img/teachroom/nopoint.png" />暂无知识点视频
            </div>
            <div class="wk_opt" v-show="state.videoUrl">
              <div class="wk_collect" :class="state.vidInfo.userCollect?'active':''" @click="setCollect">
                <img src="@/assets/img/teachroom/collect.svg" />
                <img src="@/assets/img/teachroom/collectsel.svg" />
              </div>
              <div class="wk_thumbs" :class="state.vidInfo.userLike?'active':''" @click="setThumbs">
                <img src="@/assets/img/teachroom/thumbs.svg" />
                <img src="@/assets/img/teachroom/thumbs2sel.svg" />
                {{state.vidInfo.likeNum||0}}
              </div>
            </div>
          </div>
          <template v-if="state.vidInfo.summary">
            <!-- 优学派h5 -->
            <iframe :src="state.vidInfo.summary" class="iframe" v-if="state.vidInfo.isHttp"></iframe>
            <!-- 学大html -->
            <div class="wkinfo" v-html="ReplaceMathString(state.vidInfo.summary)" v-else></div>
          </template>
        </div>
        <div class="menu_rt" id="menu_rt">
          <div class="now_box" v-show="state.data.length">
            <div class="now_air">
              <img src="@/assets/img/teachroom/playing2.gif" />正在播放
            </div>
            <div class="now_img">
              <img :src="state.vidInfo.cover" style="transform: scale(1.1);" v-if="state.vidInfo.cover" />
              <img src="@/assets/img/teachroom/novid.png" v-else />
              <template v-if="state.vidType==1">
                <div class="now_status status2" v-if="state.vidInfo.studyStatus==2">已学完</div>
                <div class="now_status status1" v-else-if="state.vidInfo.studyStatus==1">未学完</div>
                <div class="now_status status0" v-else>未学习</div>
              </template>
            </div>
            <div class="now_name" v-html="ReplaceMathString(state.vidInfo.videoName3||state.vidInfo.videoName)"></div>
          </div>
          <!-- 显示知识点名称作为标题 -->
          <div class="vid_h2 nowrap" v-if="state.data.length">知识点视频列表</div>
          <div class="vid_ul">
            <!-- 遍历知识点数据 -->
            <template v-for="(knowledgeItem, knowledgeIndex) in state.data" :key="`knowledge-${knowledgeIndex}`">
              <!-- 知识点分组标题 -->
              <div class="knowledge_group_title" v-if="knowledgeItem.name">
                {{ knowledgeItem.name }}
              </div>
              
              <!-- 如果该知识点有视频列表 -->
              <template v-if="knowledgeItem.videoInfos && knowledgeItem.videoInfos.length > 0">
                <div 
                  class="vid_li" 
                  v-for="(video, videoIndex) in knowledgeItem.videoInfos" 
                  :key="`video-${knowledgeIndex}-${videoIndex}`"
                  @click="wekePlay" 
                  :data-i="knowledgeIndex"
                  :data-i2="0" 
                  :data-i3="videoIndex"
                  :class="{ 'active': video.videoId === state.videoId }"
                >
                  <div class="vid_img">
                    <img :src="video.cover" class="wk_img" v-if="video.cover" />
                    <img src="@/assets/img/teachroom/novid.png" class="wk_img" v-else />
                    <!-- 播放状态图标 -->
                    <!-- <div class="play_icon" v-if="video.videoId === state.videoId">
                      <img src="@/assets/img/teachroom/playing2.gif" />
                    </div> -->
                  </div>
                  <div class="vid_rt">
                    <div class="vid_name nowrap2" v-html="ReplaceMathString(video.videoName)"></div>
                    <div class="vid_info">
                      <div class="knowledge_name">{{ video.knowledgeName }}</div>
                    </div>
                    <div class="vid_state">
                      <!-- 学习状态 -->
                      <template v-if="video.type == 1">
                        <div class="vid_status status2" v-if="video.studyStatus == 2">已学完</div>
                        <div class="vid_status status1" v-else-if="video.studyStatus == 1">未学完</div>
                        <div class="vid_status status0" v-else>未学习</div>
                      </template>
                      <div class="vid_h1"></div>
                      <div class="vid_thumbs">
                        <img src="@/assets/img/teachroom/thumbs.svg" />
                        <img src="@/assets/img/teachroom/thumbs2sel.svg" />
                        {{ video.likeNum || 0 }}
                      </div>
                    </div>
                  </div>
                </div>
              </template>
              
              <!-- 如果该知识点没有视频但有概述 -->
              <div 
                class="vid_li no_video" 
                v-else-if="knowledgeItem.summary"
                @click="showSummaryInLeft" 
                :data-i="knowledgeIndex"
                :class="{ 'active': !state.videoId && state.vidInfo.knowledgeId === knowledgeItem.knowledgeId }"
              >
                <div class="vid_img">
                  <img src="@/assets/img/teachroom/novid2.png" class="wk_img" />
                </div>
                <div class="vid_rt">
                  <div class="vid_name nowrap2" v-html="ReplaceMathString(knowledgeItem.name)"></div>
                  <div class="vid_info">
                    <div class="summary_hint">左侧查看概述</div>
                  </div>
                  <div class="vid_state">
                    <div class="vid_status status0">仅概述</div>
                    <div class="vid_h1"></div>
                  </div>
                </div>
              </div>
              
              <!-- 如果该知识点既没有视频也没有概述 -->
              <div 
                class="vid_li empty" 
                v-else
              >
                <div class="vid_img">
                  <img src="@/assets/img/teachroom/novid2.png" class="wk_img" />
                </div>
                <div class="vid_rt">
                  <div class="vid_name nowrap2" v-html="ReplaceMathString(knowledgeItem.name)"></div>
                  <div class="vid_info">
                    <div class="empty_hint">暂无内容</div>
                  </div>
                  <div class="vid_state">
                    <div class="vid_status">暂无</div>
                    <div class="vid_h1"></div>
                  </div>
                </div>
              </div>
            </template>
            
            <!-- 如果没有任何数据 -->
            <div class="empty_list" v-if="!state.data.length && state.isShow">
              <img src="@/assets/img/teachroom/nopoint.png" />
              <div>暂无知识点视频</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 购买会员弹窗 -->
  <buyVip :show="state.showVip" @close="quitHide"></buyVip>
  <!-- 积分弹窗 -->
  <coinAlert :show="state.jfShow" :hide="state.jfHide" :num="state.jfNum" :source="state.jfSource" @close="jfHide">
  </coinAlert>
</template>

<script lang="ts" setup>
  import { reactive, onMounted, watch } from 'vue';
  import router from '@/router/index'
  import { ElMessage } from "element-plus"
  import { useUserStore } from "@/store/modules/user"
  import { subjectList } from '@/utils/user/enum'
  import { ReplaceMathString } from '@/utils/user/util'
  import { setStudyTimeApi, analyseVideoNumApi } from "@/api/user"
  import { getVideoListApi, getVideoUrlApi, videoModifyApi, getVideoReviewNumApi, setUserVideoViewNumberApi,getKnowledgeVideosApi } from "@/api/video"
  import { useRoute } from "vue-router"
  import buyVip from "@/views/components/buyVip/index.vue"
  import coinAlert from "@/views/components/coinAlert/index.vue"
  import { setLearnKey } from '@/utils/user/learntime'

  defineOptions({
    name: "PointRoomTeachVideo"
  })

  const route = useRoute()
  const state : any = reactive({
    showVip: false,
    dp: null,
    chapterId: '',
    subActive: '',
    vidInfo: {},
    isFirst: 1,
    isShow: false,
    data: [],
    poster: '',
    videoId: '',
    videoUrl: '',
    videoList: [], //视频
    videoIndex: 0,
    autoplay: false,
    subject: '',
    dataType: 0, //0带概述 1例题
    dataI: 0,
    dataI2: 0,
    dataI3: 0,
    isOne: '', //是否第一个视频
    vidType: '',
    //视频组件
    video: '',
    controls: false,
    videoBtn: true,
    playstate: 0, //0暂停，1播放
    speed: '1.0', //速度
    speedArr: ['0.5', '0.75', '1.0', '1.25', '1.5', '2.0'],
    rate: 0, //显示倍速
    playbtn: true,
    title: '',
    isVidMenu: 0,
    //进度条
    showSlider: true,
    showComp: 0,
    updateState: false,
    slider: 0,
    curtime: 0,
    nowtime: '00:00', // 当前时间
    endtime: '00:00', // 总时长
    duration: '', // 视频长度秒
    isFull: false,
    //积分
    jfShow: false,
    jfHide: true,
    jfNum: '0',
    jfSource: '0',
    // 用户交互状态
    userHasInteracted: false
  })

  onMounted(() => {
    setData({
      loading:true
    })
    
    // 添加用户交互监听器
    const handleUserInteraction = () => {
      state.userHasInteracted = true
      console.log('👆 用户已与页面交互，可以尝试自动播放')
      // 移除监听器，只需要监听一次
      document.removeEventListener('click', handleUserInteraction)
      document.removeEventListener('touchstart', handleUserInteraction)
      document.removeEventListener('keydown', handleUserInteraction)
    }
    
    document.addEventListener('click', handleUserInteraction)
    document.addEventListener('touchstart', handleUserInteraction)
    document.addEventListener('keydown', handleUserInteraction)
    
    // console.log(route.query,"route.query")
    init(route.query)
  })

  //监听路由参数
  watch(
    () => route.query,
    (newQ) => {
      if (newQ && route.name == "PointRoomTeachVideo") {
        setData({
          loading:true
        })
        init(newQ)
      }
    }
  )

  // 知识点讲解监听
  watch(() => state.vidInfo.summary, (newVal, oldVal) => {
    if (newVal) {
      setJfShow()
    }
  }, { immediate: true })

  // 显示积分-看知识点讲解
  const setJfShow = () => {
    state.jfShow = true
    state.jfHide = false
    state.jfNum = '3'
    state.jfSource = '1'
  }

  // 显示积分-看视频
  const setJfShow2 = () => {
    state.jfShow = true
    state.jfHide = true
    state.jfNum = '5'
    state.jfSource = '2'
  }

  // 隐藏积分
  const jfHide = () => {
    state.jfShow = false
  }

  // 属性赋值
  const setData = (obj : any) => {
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        state[key] = obj[key]
      }
    }
  }

  const quitHide = () => {
    state.showVip = false
    if (useUserStore().memberInfo) {
      init(route.query)
    }
  }

  const init = (query : any) => {
    if (query.id) {
      let { id, type, vid, subKey } = query
      setData({
        chapterId: id,
        subActive: subKey,
        videoId: vid,
        vidType: type,
        subject: subKey,
        //重置
        vidInfo: {},
        isFirst: 1,
        isShow: false,
        data: [],
        poster: '',
        videoUrl: '',
        videoList: [], //视频
        videoIndex: 0,
        autoplay: false,
        dataType: 0, //0带概述 1例题
        dataI: 0,
        dataI2: 0,
        dataI3: 0,
        isOne: '', //是否第一个视频
      })
      getVideoList()
      // console.log(state.subject,"state.subjectstate.subjectstate.subject")
      //记录学习学科
      setLearnKey(state.subject)
    }
  }

  //获取知识点视频列表
  const getVideoList = () => {
    const { chapterId, videoId, subject } = state
    const param = {
      subject,
      id: chapterId
    }       
    getKnowledgeVideosApi(param)
      .then((res : any) => {
        const res2 = res.data
        
        if (res2?.length) {
          // 处理视频数据结构
          const processedData = res2.map((knowledgeItem: any, index: number) => {

     
            // 如果有视频列表，处理每个视频
            if (knowledgeItem.videoInfos && knowledgeItem.videoInfos.length > 0) {
              knowledgeItem.videoInfos = knowledgeItem.videoInfos.map((video: any, videoIndex: number) => {
                // 继承知识点的属性
                const processedVideo = {
                  ...video,
                  type: knowledgeItem.type || 1, // 视频类型，默认为1（优学派）
                  summary: knowledgeItem.summary || '', // 知识点概述
                  knowledgeId: knowledgeItem.knowledgeId, // 知识点ID
                  knowledgeName: knowledgeItem.name, // 知识点名称
                  isHttp: 0
                }
                
                // 判断概述是否为链接
                if (knowledgeItem.summary && knowledgeItem.summary.indexOf('https://') === 0) {
                  processedVideo.isHttp = 1
                }              
                
                return processedVideo
              })
            }
            
            return knowledgeItem
          })
          
          // 设置默认显示内容（优先视频，其次概述）
          let defaultContent:any = null
          let hasVideo = false
          
          // 首先尝试找到有视频的知识点
          for (const knowledgeItem of processedData) {
            if (knowledgeItem.videoInfos && knowledgeItem.videoInfos.length > 0) {
              defaultContent = knowledgeItem.videoInfos[0]
              hasVideo = true
              break
            }
          }
          
          // 如果没有视频，找到有概述的知识点
          if (!defaultContent) {
            for (const knowledgeItem of processedData) {
              if (knowledgeItem.summary) {
                defaultContent = {
                  summary: knowledgeItem.summary,
                  isHttp: knowledgeItem.summary.indexOf('https://') === 0 ? 1 : 0,
                  videoName: knowledgeItem.name,
                  videoName3: knowledgeItem.name,
                  type: knowledgeItem.type || 1,
                  knowledgeName: knowledgeItem.name,
                  knowledgeId: knowledgeItem.knowledgeId,
                  videoId: null // 标记为无视频
                }
                hasVideo = false
                break
              }
            }
          }
          
          if (defaultContent) {
            setData({
              data: processedData,
              vidInfo: defaultContent,
              videoUrl: hasVideo ? '' : '', // 无视频时清空URL
              videoList: hasVideo ? [] : [] // 无视频时清空列表
            })
            
            if (hasVideo) {
              getVideoUrl()
            } else {
              // 没有视频，只显示概述
              setData({
                isShow: true,
                loading: false
              })
            }
          } else {
            setData({
              data: processedData,
              vidInfo: {},
              isShow: true,
              loading: false
            })
          }
        } else {
          setData({
            title: '',
            data: [],
            videoId: '',
            isShow: true
          })
        }
      })
      .catch((error) => {
        console.error('❌ 获取知识点视频列表失败:', error)
        setData({
          title: '',
          data: [],
          videoId: '',
          isShow: true
        })
      })
  }

  //判断视频播放次数
  const getVideoUrl = () => {
    if (useUserStore().memberInfo) {
      getVidSrc()
    } else {
      getVideoReviewNumApi().then((res : any) => {
        const num = res.data
        if (num < 2) {
          ElMessage.success(`剩余免费观看次数：${2 - num - 1}`)
          getVidSrc()
        } else {
          setData({
            isShow: true,
            loading: false
          })
          //开通会员弹窗
          state.showVip = true
        }
      })
    }
  }
  //获取知识点视频url
  const getVidSrc = () => {
    const param = {
      type: state.vidInfo.type, //1优学派 2菁优网
      videoId: state.vidInfo.videoId
    }
    getVideoUrlApi(param)
      .then((res : any) => {
        let data = res.data || ''
        setData({
          isShow: true,
          videoUrl: data,
          videoList: data ? [data] : [],
          loading: false
        })
        if (state.isFirst) {
          //初始化视频控件
          initPlayers()
        } else {
          switchVideo()
        }
        setData({
          isFirst: 0
        })
        if (data) {
          videoPause()
        }
        setVideo()
        videoScoreSave()
        setStudyState(1)
      })
      .catch(() => {
        setData({
          isShow: true,
          loading: false
        })
      })
  }
  //设置视频信息,默认第1个
  const setVideo = () => {
    const videos = state.videoList
    if (videos.length) {
      setData({
        videoUrl: videos[0],
        autoplay: true
      })
      videoPlay()
    } else {
      setData({
        videoUrl: '',
        autoplay: false
      })
    }
  }

  //记录视频播放次数
  const setUserVideoViewNumber = () => {
    const { chapterId, pointId, videoId, subject } = state
    const param = {
      time: 0,
      videoId,
      type: state.vidType, //1优学派 2菁优网
      subject,
      pointId,
      viewType: true, //true:观看次数+1；false:增加观看次数
      chapterId: ''
    }
    if (chapterId && chapterId != 'undefined') {
      param.chapterId = chapterId
    }
    if (!videoId) {
      return
    }
    setUserVideoViewNumberApi(param)
  }
  // 记录每日查看视频数量
  const analyseVideoNum = () => {
    const { subject } = state
    analyseVideoNumApi({ subject })
  }
  //记录视频播放状态
  const videoScoreSave = () => {
    analyseVideoNum()
    setUserVideoViewNumber()
  }
  //返回置顶
  const setTop = () => {
    const div : any = document.getElementById('menu_rt');
    div.scrollTop = 0;
  }

  // 知识点-切换视频
  const wekePlay = (e : any) => {
    // setTop()
    const { i, i2, i3 } = e.currentTarget.dataset
    const { data } = state
    
    // 根据新的数据结构获取视频信息
    const knowledgeItem = data[i]
    if (!knowledgeItem || !knowledgeItem.videoInfos || !knowledgeItem.videoInfos[i3]) {
      console.error('❌ 无法找到视频信息:', { i, i2, i3 })
      return
    }
    
    const info = knowledgeItem.videoInfos[i3]
    const { cover, videoId, videoName, type } = info
  
    
    if (videoId == state.videoId) {
      console.log('⚠️ 已经是当前播放的视频')
      return
    }
    
    //改变学习状态
    if (info.studyStatus != 2) {
      info.studyStatus = 1
    }
    
    // 更新数据中的视频信息
    data[i].videoInfos[i3] = info
    
    setData({
      poster: cover,
      videoId,
      title: videoName,
      data,
      dataI: i,
      dataI2: i2,
      dataI3: i3,
      dataType: 0,
      isOne: 0,
      vidType: type
    })
    
    //跳转第0秒
    state.dp?.seek(0)
    setData({
      slider: 0,
      vidInfo: info
    })
    
    // 标记用户已交互（点击切换视频）
    state.userHasInteracted = true
    
    getVideoUrl()
  }

  // 在左侧显示概述内容（无视频时）
  const showSummaryInLeft = (e: any) => {
    const { i } = e.currentTarget.dataset
    const { data } = state
    
    const knowledgeItem = data[i]
    if (!knowledgeItem || !knowledgeItem.summary) {
      console.error('❌ 无法找到知识点概述信息:', { i })
      return
    }
    
    console.log('📖 在左侧显示概述:', {
      knowledgeName: knowledgeItem.name,
      knowledgeId: knowledgeItem.knowledgeId,
      hasHttpUrl: knowledgeItem.summary.indexOf('https://') === 0
    })
    
    // 构造概述信息对象
    const summaryInfo = {
      summary: knowledgeItem.summary,
      isHttp: knowledgeItem.summary.indexOf('https://') === 0 ? 1 : 0,
      videoName: knowledgeItem.name,
      videoName3: knowledgeItem.name,
      type: knowledgeItem.type || 1,
      knowledgeName: knowledgeItem.name,
      knowledgeId: knowledgeItem.knowledgeId,
      videoId: null, // 标记为无视频
      userCollect: false,
      userLike: false,
      likeNum: 0
    }
    
    // 更新状态显示概述
    setData({
      vidInfo: summaryInfo,
      videoId: null,
      videoUrl: '',
      videoList: [],
      poster: '',
      title: knowledgeItem.name,
      dataI: i,
      dataI2: 0,
      dataI3: 0,
      dataType: 0,
      isOne: 0,
      vidType: knowledgeItem.type || 1
    })
    
    // 停止当前视频播放
    if (state.dp) {
      state.dp.pause()
    }
  }

  // 设置学习状态
  const setStudyState = (status : any) => {
    const { chapterId, pointId, videoId, subject, data, dataI, dataI2, dataI3, title, vidType, vidInfo } = state
    const param = {
      chapterId,
      pointId,
      videoId,
      type: vidType, //1优学派 2菁优网
      subject,
      source: vidType, //1同步章节 2知识点
      knowledgeName: title,
      status //0未学习 1正在学 2已学完
    }
    
    // 根据新的数据结构更新学习状态
    if (data[dataI] && data[dataI].videoInfos && data[dataI].videoInfos[dataI3]) {
      const currentVideo = data[dataI].videoInfos[dataI3]
      const currentStatus = currentVideo.studyStatus
            
      //非已学完判断
      if (currentStatus != 2) {
        videoModifyApi(param)
          .then(() => {
            // 更新数据中的学习状态
            data[dataI].videoInfos[dataI3].studyStatus = status
            setData({
              data
            })
          })
          .catch((error) => {
            console.error('❌ 学习状态更新失败:', error)
          })
        
        //改变当前播放视频的学习状态
        vidInfo.studyStatus = status
        setData({
          vidInfo
        })
      } 
    } else {
      console.warn('⚠️ 无法找到对应的视频数据来更新学习状态')
    }
  }
  //目录-点击
  const setCataVid = (e : any) => {
    const { i } = e.currentTarget.dataset
    const { data } = state
    for (const x of data) {
      x.active = ''
    }
    //默认第一个展开
    data[i].active = 'active'
    const list = data[i].knowledgeList
    list[0].active = 'up'
    //获取视频标题
    const info = list[0]?.videoInfos || []
    const title = info[0]?.videoName || ''
    const videoId = info[0]?.videoId || ''
    setData({
      title,
      data,
      videoId,
      isShow: true
    })
    //获取第一个视频
    if (videoId) {
      setData({
        dataI: i,
        dataI2: 0,
        dataI3: 0,
        isOne: 1
      })
      getVideoUrl()
    } else {
      //无视频显示概述,清空视频
      setData({
        dataI: i,
        videoId: '',
        videoUrl: ''
      })
    }
  }
  // 判断会员状态
  const getIsMember = () => {
    const isVip = JSON.parse(localStorage.memberInfo || '{}')
    if (isVip?.isMember) {
      return true
    } else {
      state.showVip = true
      return false
    }
  }

  //概述-显示(只能跳转h5)
  const gaisuShow = (e : any) => {
    if (getIsMember()) {
      const { i, i2 } = e.currentTarget.dataset
      const { data, subject } = state
      
      // 根据新的数据结构获取概述信息
      const knowledgeItem = data[i]
      if (!knowledgeItem) {
        console.error('❌ 无法找到知识点信息:', { i })
        return
      }
      
      const url = knowledgeItem.summary || ''    
      
      if (url) {
        if (url.indexOf('https://') == 0) {
          // 外部链接
          router.push({ name: "PointRoomTeachView", query: { url, subject } })
        } else {
          // HTML代码
          localStorage.explainStr = url
          router.push({ name: "PointRoomTeachView", query: { subject } })
        }
      } else {
        // 无知识点概述
        localStorage.explainStr = ''
        router.push({ name: "PointRoomTeachView", query: { subject } })
      }
    }
  }

  // 点赞
  const setThumbs = () => {
    const { chapterId, pointId, videoId, subject, data, dataI, dataI2, dataI3, vidInfo, title, vidType } = state
    const param : any = {
      chapterId,
      pointId,
      videoId,
      type: vidType, //1优学派 2菁优网
      subject,
      source: vidType, //1同步章节 2知识点
      knowledgeName: title
    }
    const isThumbs = state.vidInfo.userLike ? 0 : 1
    ElMessage.closeAll()
    if (isThumbs) {
      param.like = 1
      videoModifyApi(param)
        .then(() => {
          ElMessage.success('点赞成功')
          state.vidInfo.likeNum++
          state.vidInfo.userLike = true
          setData({
            data
          })
        })
    } else {
      param.like = 0
      videoModifyApi(param)
        .then(() => {
          ElMessage.success('已取消点赞')
          state.vidInfo.likeNum--
          state.vidInfo.userLike = false
          setData({
            data
          })
        })
    }
  }
  // 收藏
  const setCollect = () => {
    const { chapterId, pointId, videoId, subject, data, dataI, dataI2, dataI3, vidInfo, vidType } = state
    const param : any = {
      chapterId,
      pointId,
      videoId,
      type: vidType, //1优学派 2菁优网
      subject,
      source: vidType //1同步章节 2知识点
    }
    param.knowledgeName = vidInfo.videoName3 || vidInfo.videoName
    const isCollect = state.vidInfo.userCollect ? 0 : 1
    if (isCollect) {
      param.collect = 1
      videoModifyApi(param)
        .then(() => {
          ElMessage.success('收藏成功')
          state.vidInfo.userCollect = true
          setData({
            data
          })
        })
    } else {
      param.collect = 0
      videoModifyApi(param)
        .then(() => {
          ElMessage.success('已取消收藏')
          state.vidInfo.userCollect = false
          setData({
            data
          })
        })
    }
  }

  //组件-播放
  const videoPlay = () => {
    if (state.videoUrl) {
      // state.dp.play()
      setData({
        videoBtn: false
      })
      //尝试播放-处理浏览器自动播放策略
      setTimeout(() => {
        const video: any = document.getElementById('video')
        if (video) {
          const playPromise = video.play()
          if (playPromise !== undefined) {
            playPromise
              .then(() => {
                console.log('✅ 视频播放成功')
              })
              .catch((error: any) => {
                console.warn('⚠️ 视频播放失败:', error.message)
                setData({
                  videoBtn: true // 重新显示播放按钮
                })
                ElMessage.info('请点击播放按钮开始观看视频')
              })
          }
        }
      }, 100)
    } else {
      ElMessage.error('请先选择一个视频')
    }
  }

  //组件-暂停
  const videoPause = () => {
    state.dp.pause()
    setData({
      videoBtn: true
    })
  }

  function initPlayers() {
    let { videoName3, videoName, type, cover } = state.vidInfo
    let window1 : any = window
    let DPlayer : any = window1?.DPlayer
    state.dp = new DPlayer({
      container: document.getElementById('dplayer1'),
      autoplay: false, // 关闭自动播放，避免浏览器策略问题
      theme: '#1DDFAC', //进度条、音量颜色
      preload: 'auto',
      volume: 1,
      contextmenu: [],
      video: {
        title: videoName3 || videoName,
        url: state.videoUrl,
        pic: cover,
        type: type == 1 ? 'hls' : 'normal'
      }
    });

    state.dp.on('loadstart', function () {
      showPlayIcon()
      // 修改设置为倍速弹窗
      let setbtn : any = document.querySelector('.dplayer-setting-icon')
      setbtn.addEventListener('mouseenter', function () {
        if (document.querySelectorAll('.dplayer-hide-controller').length) {
          // 底部菜单隐藏时不显示
          return
        }
        //显示倍速弹窗
        let setting : any = document.querySelector('.dplayer-setting-box')
        setting.className =
          'dplayer-setting-box dplayer-setting-box-narrow dplayer-setting-box-speed dplayer-setting-box-open'
      })
      //倍速弹窗hover隐藏
      let setting : any = document.querySelector('.dplayer-setting-box')
      setting.addEventListener('mouseleave', function () {
        setting.className = 'dplayer-setting-box'
      })
      setting.addEventListener('click', function () {
        setting.className = 'dplayer-setting-box'
      })

      //隐藏视频标题
      let vidClass : any = document.querySelector('.dplayer-video-current')
      vidClass.addEventListener('mouseleave', function () {
        if (document.querySelectorAll('.dplayer-paused').length) {
          // 暂停时不隐藏
          return
        }
        let title : any = document.getElementById('fulltit')
        title.style.display = 'none'
      })
    })

    //设置倍速文字
    state.dp.on('ratechange', function () {
      speedChange('')
    });

    state.dp.on('ended', function () {
      // console.log('结束');
      videoPause()
      setStudyState(2)
      setJfShow2()
    });
    state.dp.on('pause', function () {
      showPlayIcon()
    });
    state.dp.on('play', function () {
      // console.log('开始播放');
      hidePlayIcon()
    });

    state.dp.on('volumechange', function () {
      // console.log('音量切换');
    })
  }

  fullScreenListeners()
  //监听全屏
  function fullScreenListeners() {
    document.addEventListener('fullscreenchange', function () {
      let fullIcon : any = document.querySelector('#fullIcon')
      if (document.fullscreenElement) {
        fullIcon.setAttribute('data-title', '退出全屏')
      } else {
        fullIcon.setAttribute('data-title', '全屏')
      }
    })
  }

  //显示播放图标
  function showPlayIcon() {
    let icon : any = document.querySelector('.dplayer-bezel-icon')
    if(icon){
      icon.className = 'dplayer-bezel-icon play'
    }
  }

  //隐藏播放图标
  function hidePlayIcon() {
    setTimeout(() => {
      let icon : any = document.querySelector('.dplayer-bezel-icon')
      if(icon){
        icon.className = 'dplayer-bezel-icon'
      }
    }, 0)
  }

  //切换视频
  function switchVideo() {
    let { videoName3, videoName, type, cover } = state.vidInfo
    let obj = {
      title: videoName3 || videoName,
      url: state.videoUrl,
      pic: cover,
      type: type == 1 ? 'hls' : 'normal'
    }
    let src = state.dp.video.src
    if (src == obj.url) {
      return
    }
    state.dp.switchVideo(obj);
    speedChange(1)
    //尝试播放-处理浏览器自动播放策略
    setTimeout(() => {
      const video: any = document.getElementById('video')
      if (video) {
        const playPromise = video.play()
        if (playPromise !== undefined) {
          playPromise
            .then(() => {
              console.log('✅ 视频切换并播放成功')
            })
            .catch((error: any) => {
              console.warn('⚠️ 视频切换后播放失败:', error.message)
              // 切换视频失败时，通常用户已经有了交互，这里可以静默处理
            })
        }
      }
    }, 100)
  }
  // 倍速切换
  function speedChange(num : any) {
    let video : any = document.getElementById('video')
    let speed = num || video.playbackRate
    let sptxt : any = document.querySelector('.dplayer-icon.dplayer-setting-icon')
    if (speed == 1) {
      sptxt.innerHTML = '倍速'
    } else if (speed == 2) {
      sptxt.innerHTML = '2.0X'
    } else {
      sptxt.innerHTML = speed + 'X'
    }
    //倍速弹窗选中变色
    let list : any = document.querySelectorAll('.dplayer-setting-speed-item')
    for (let i = 0; i < list.length; i++) {
      let num = Number(list[i].attributes['data-speed'].value)
      if (num == speed) {
        list[i].className = 'dplayer-setting-speed-item green'
      } else {
        list[i].className = 'dplayer-setting-speed-item'
      }
    }
  }
  //视频控件e
</script>

<style lang="scss" scoped>
  @import url('@/assets/styles/reset.css');

  .header_seat {
    width: 100%;
    height: 4.375rem;
    float: left;
  }

  .none {
    display: none !important
  }

  .nowrap {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
  }

  .nowrap2 {
    width: 100%;
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    word-break: break-all;
    padding-top: 6px;
    -webkit-line-clamp: 2;
  }

  .content {
    width: 100%;
    background: #F5F5F5;
    overflow-y: auto;
  }

  .inner {
    margin: 0 auto;
    width: 81.25rem;
  }

  .wrap {
    float: left;
    width: 100%;
    /* height: calc(100vh - 7.4375rem);
    overflow-y: auto; */
  }

  .wrap>div {
    /* height: calc(100vh - 7.4375rem);
    overflow-y: auto; */
    border: .0625rem solid #eaeaea;
    box-sizing: border-box;
    background: #fff;
  }

  /* 视频 */
  .menu_lt {
    float: left;
    width: calc(100% - 21.125rem - .625rem);
    padding: .625rem;
  }

  .wk_box {
    width: 100%;
  }

  .wk_box div {
    float: left;
  }

  .wk_tit {
    width: 100%;
  }

  .wk_p {
    line-height: 1.375rem;
    color: #2a2b2a;
    font-size: 1rem;
    margin: 0 .625rem 0 0;
  }

  .wk_status {
    width: 2.875rem;
    line-height: 1.375rem;
    border-radius: .25rem;
    text-align: center;
    font-size: .75rem;
  }

  .wk_status.status0 {
    color: #fff;
    background: #999;
  }

  .wk_status.status1 {
    color: #EF9D19;
    background: #FEF8E9;
  }

  .wk_status.status2 {
    color: #009C7F;
    background: #E5F9F6;
  }

  .wk_video {
    width: 100%;
    height: 41.8125rem;
    background: #000;
    margin: 1.25rem 0 0;
  }

  .wk_nodata {
    width: 100%;
    height: 41.8125rem;
    background: #000;
    margin: 1.25rem 0 0;
    color: #999999;
    font-size: .875rem;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-flow: column;
  }

  .wk_nodata img {
    width: 7.4375rem;
    height: 8rem;
    margin: 0 0 .625rem;
  }

  /* 仅概述显示区域 */
  .wk_summary_only {
    width: 100%;
    height: 41.8125rem;
    background: #f8f9fa;
    margin: 1.25rem 0 0;
    border-radius: 0.5rem;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .summary_title {
    width: 100%;
    height: 3rem;
    background: linear-gradient(90deg, #5A85EC 0%, #7B9EF7 100%);
    color: white;
    display: flex;
    align-items: center;
    padding: 0 1rem;
    font-size: 1rem;
    font-weight: 600;
  }

  .summary_title img {
    width: 1.5rem;
    height: 1.5rem;
    margin-right: 0.5rem;
  }

  .summary_content {
    flex: 1;
    width: 100%;
    overflow: hidden;
    position: relative;
  }

  .summary_iframe {
    border: 0;
    width: 100%;
    height: 100%;
  }

  .summary_html {
    width: 100%;
    height: 100%;
    padding: 1rem;
    box-sizing: border-box;
    overflow-y: auto;
    color: #2a2b2a;
    font-size: 0.875rem;
    line-height: 1.6;
    background: white;
  }

  .summary_html::-webkit-scrollbar {
    width: 6px;
  }

  .summary_html::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
  }

  .summary_html::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
  }

  .summary_html::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }

  /* 点赞收藏 */
  .wk_opt {
    width: 100%;
    height: 4rem;
    box-sizing: border-box;
    padding: 1.25rem .625rem;
    border-bottom: .0625rem solid #eaeaea;
  }

  .wk_collect {
    margin: 0 3.125rem 0 0;
    cursor: pointer;
  }

  .wk_collect img {
    width: 1.5rem;
    height: 1.5rem;
  }

  .wk_collect img:first-child,
  .wk_collect.active img:last-child {
    display: inline-block;
  }

  .wk_collect img:last-child,
  .wk_collect.active img:first-child {
    display: none;
  }

  .wk_thumbs {
    line-height: 1.5rem;
    color: #666666;
    font-size: .875rem;
    float: right;
    cursor: pointer;
  }

  .wk_thumbs img {
    float: left;
    width: 1.5rem;
    height: 1.5rem;
    margin: 0 .375rem 0 0;
  }

  .wk_thumbs img:nth-child(1),
  .wk_thumbs.active img:nth-child(2) {
    display: inline-block;
  }

  .wk_thumbs img:nth-child(2),
  .wk_thumbs.active img:nth-child(1) {
    display: none;
  }

  /* 知识点 */
  .iframe {
    border: 0;
    width: 100%;
    height: 100vh;
  }

  .wkinfo {
    float: left;
    width: 100%;
    color: #2a2b2a;
    font-size: .875rem;
    font-weight: 400;
    line-height: 1.625rem;
    box-sizing: border-box;
    padding: .625rem 1.25rem;
  }

  /* 正在播放 */
  .menu_rt {
    float: right;
    width: 21.125rem;
    border: .0625rem solid #eaeaea;
    background: #ffffff;
    max-height: 100vh;
    overflow-y: auto;
  }

  .menu_rt div {
    float: left;
  }

  .now_box {
    width: 100%;
    border-bottom: .0625rem solid #eaeaea;
  }

  .now_air {
    width: 100%;
    line-height: 1rem;
    color: #5a85ec;
    font-size: 1rem;
    margin: .8125rem 0 1.25rem;
  }

  .now_air img {
    float: left;
    width: 1rem;
    height: 1rem;
    margin: 0 .375rem 0 .625rem;
  }

  .now_img {
    width: 19.875rem;
    height: 11.1875rem;
    border-radius: .25rem;
    border: .0625rem solid #5a85ec;
    box-sizing: border-box;
    overflow: hidden;
    margin: 0 .625rem;
  }

  .now_img img {
    float: left;
    width: 19.875rem;
    height: 11.1875rem;
  }

  .now_name {
    width: 100%;
    line-height: 1.1875rem;
    color: #5a85ec;
    font-size: .875rem;
    box-sizing: border-box;
    padding: .375rem .625rem 1.25rem;
  }

  div.now_status {
    float: right;
    width: 3.75rem;
    line-height: 1.5rem;
    border-radius: 0 0 0 .25rem;
    text-align: center;
    font-size: .75rem;
    color: #fff;
    position: relative;
    z-index: 2;
    margin: -11.1875rem 0 0;
  }

  .now_status.status0 {
    background: #999;
  }

  .now_status.status1 {
    background: linear-gradient(150.8deg, #f6d22b 0%, #f29500 100%);
  }

  .now_status.status2 {
    background: linear-gradient(166.7deg, #08d8b8 0%, #00b392 100%);
  }


/* 列表 */
.vid_h2 {
  width: calc(100% - 1.25rem);
  line-height: 1.1875rem;
  border-radius: .25rem;
  background: #f5f5f5;
  color: #2a2b2a;
  font-size: .875rem;
  box-sizing: border-box;
  padding: .5625rem .625rem;
  margin: .625rem 0 0 .625rem;
}

.vid_ul {
  width: 100%;
  box-sizing: border-box;
  padding: .625rem 0 0 .625rem;
}

.vid_li {
  width: calc(100% - 0rem);
  /* height: 5.0625rem;
  overflow: hidden; */
  margin: 0 0 .625rem;
  cursor: pointer;
}

.vid_img,
.vid_img img {
  float: left;
  width: 8.75rem;
  height: 5.0625rem;
  border-radius: .25rem;
}

.vid_rt {
  width: calc(100% - 9.125rem);
  margin: 0 0 0 .375rem;
}

.vid_name {
  width: calc(100% - .625rem);
  max-height: 4rem;
  line-height: 1.5;
  color: #2a2b2a;
  font-size: .75rem;
}

.vid_li .vid_name {
  color: #5A85EC;
}

.vid_state {
  width: 100%;
  margin: 1rem 0 0;
}

.vid_status {
  width: 2.875rem;
  line-height: 1.3125rem;
  border-radius: .25rem;
  text-align: center;
  color: #ffffff;
  font-size: .75rem;
}

.vid_status.status0 {
  color: #fff;
  background: #999;
}

.vid_status.status1 {
  color: #EF9D19;
  background: #FEF8E9;
}

.vid_status.status2 {
  color: #009C7F;
  background: #E5F9F6;
}

.vid_h1 {
  line-height: 1.3125rem;
  color: #999999;
  font-size: .75rem;
  margin: 0 .3125rem 0 .625rem;
}

div.vid_thumbs {
  float: right;
  line-height: 1.125rem;
  color: #666666;
  font-size: .75rem;
  float: right;
  margin: 0 .625rem 0 0;
}

.vid_thumbs img {
  float: left;
  width: 1.125rem;
  height: 1.125rem;
  margin: 0 .375rem 0 0;
}

.vid_thumbs img:nth-child(1),
.vid_thumbs.active img:nth-child(2) {
  display: inline-block;
}

.vid_thumbs img:nth-child(2),
.vid_thumbs.active img:nth-child(1) {
  display: none;
}

.vid_tit:hover,
.vid_thumbs img:hover {
  cursor: pointer;
}

/* 新增样式 - 知识点分组 */
.knowledge_group_title {
  width: calc(100% - 1.25rem);
  line-height: 1.5rem;
  color: #333;
  font-size: 0.875rem;
  font-weight: 600;
  padding: 0.5rem 0.625rem;
  margin: 0.5rem 0 0.25rem 0.625rem;
  background: #f8f9fa;
  border-left: 0.25rem solid #5A85EC;
  border-radius: 0.25rem;
}

/* 视频列表项状态 */
.vid_li.active {
  background: rgba(90, 133, 236, 0.1);
  border-left: 0.25rem solid #5A85EC;
}

.vid_li.active .vid_name {
  color: #5A85EC !important;
  font-weight: 600;
}

/* 播放状态图标 */
.play_icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  padding: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.play_icon img {
  width: 1.5rem;
  height: 1.5rem;
}

/* 视频信息 */
.vid_info {
  width: 100%;
  margin: 0.25rem 0;
}

.knowledge_name {
  font-size: 0.75rem;
  color: #666;
  line-height: 1.2;
}

.summary_hint {
  font-size: 0.75rem;
  color: #5A85EC;
  line-height: 1.2;
}

.empty_hint {
  font-size: 0.75rem;
  color: #999;
  line-height: 1.2;
}

/* 无视频项目样式 */
.vid_li.no_video {
  opacity: 0.8;
}

.vid_li.no_video:hover {
  background: rgba(90, 133, 236, 0.05);
}

.vid_li.empty {
  opacity: 0.6;
  cursor: default;
}

/* 空列表样式 */
.empty_list {
  width: 100%;
  padding: 2rem 1rem;
  text-align: center;
  color: #999;
  font-size: 0.875rem;
}

.empty_list img {
  width: 4rem;
  height: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}
</style>
