<template>
          <!-- 顶部导航 -->
    <header class="page-header">
      <div class="breadcrumbs">
        <a href="#" class="back-link" @click.prevent="goBack">&lt; 返回</a>
        <span class="breadcrumb-separator">&nbsp;/&nbsp;</span>
        <span class="breadcrumb-item">AI精准学</span>
        <span class="breadcrumb-separator">&nbsp;/&nbsp;</span>
        <span class="breadcrumb-item active" > {{ query.type=='1' ?'基础训练':'提升训练' }}</span>
      </div>
    </header>
  <div class="training-page">
    
    <div class="left-sidebar" v-loading="loading">
      <knowledgeTree
         ref="knowledgeTreeRef"
        :selected="chapterId"
        :iswen="true"
        :options="options"
        :selectName="selectName"
        :showCurrentTaskIndicator="!!routeHasChapterId"
        currentTaskImageUrl="@/assets/img/synchronous/task-badge.png"
        @setChapterId="setChapterId"
        :defaultExpandAll="true"
      />
      <div class="sidebar-title">
        选择章节
        <span class="title-decoration"></span>
      </div>
    </div>

    <div class="main-content" v-loading="loading2">
      <div class="content-head">
        <div class="head-body">
          <img src="@/assets/img/percision/training/textbook.png" alt="" class="textbook" />
          <div class="head-title">
            当前教材：{{ bookVersionName }}
          </div>
          <div @click="onModify" class="head-switch">切换教材</div>
          <button @click="onQuestions">去玩耍</button>
        </div>

        <img v-if="learnNow.skilledSwitch" @click="onMark" src="@/assets/img/percision/training/superficiality.png" alt="" class="superficiality" />
        <div class="catalogue">
          <span>{{ pathName }} > {{ isTargetChapterId }} </span>
          <img src="@/assets/img/percision/training/dsj.png" alt="">
        </div>      
            
      </div>

      <div class="content-tip" v-if="query.contentType=='historyTask'">
        <div class="tip-content">
          <img src="@/assets/img/percision/training/mty.png" alt="" class="tip-avatar" />
          <div class="tip-text">本节为老师布置的任务，请在规定时间内完成。<span style="color:#DD2A2A">截止时间：2025/05/15</span></div>
        </div>
        <img src="@/assets/img/percision/training/tip_bg.png" class="tip-bg" alt="" />
      </div>
      <!-- 知识点训练列表 -->
      <div class="knowledge-training-list" v-if="pageStatus">
        <div class="lesson-section" v-if="lessonData.length > 0">

          <div class="knowledge-info">
            <div class="knowledge-box">
              <!-- 根据年级动态分配知识点数量：年级>6显示3个，否则显示2个 -->
              <!-- 当前年级: {{ learnUsers[0]?.gradeId || 0 }}, 每课知识点数: {{ pointsPerLesson }} -->
              <div 
                class="lesson-container" 
                v-for="(_, lessonIndex) in totalLessons" 
                :key="'lesson-' + lessonIndex"
              >
                <div class="lesson-header">
                  <div class="lesson-title">第{{ lessonIndex + 1 }}课</div>
                </div>
                
                <div class="lesson-content">
                  <!-- 遍历当前课的知识点 -->
              <div 
                class="knowledge-item" 
                    v-for="knowledge in getLessonKnowledgePoints(lessonIndex)" 
                    :key="knowledge.id"
              >
                    <div class="knowledge-name" @click="showKnowledgeDetail(knowledge)">
                  <span class="knowledge-title">{{ knowledge.name }}</span>
                </div>
                <div class="progress-area">
                  <div class="hexagon-group">
                    
                        <!-- 根据levelVos数据动态显示等级（根据训练类型过滤） -->
                        <template v-if="knowledge.levelVos && knowledge.levelVos.length > 0">
                          <div 
                            v-for="(levelItem, levelIndex) in getFilteredLevelVos(knowledge.levelVos)" 
                            :key="levelIndex"
                            class="level-wrapper"
                          >
                            <div class="level-item" style="position: relative;">
                               <img v-if="levelItem.status==1" style="position: absolute;top: -16px;left: 10px;width: 30px;height: 27px;" src="@/assets/img/percision/training/huangg.png" alt="">
                              <img 
                                :src="getLevelImage(levelItem.level, levelItem.correctRate)" 
                                :alt="getLevelName(levelItem.level)"
                                class="level-image"
                              />
                              <div class="level-info">
                                <!-- <div class="level-name">{{ getLevelName(levelItem.level) }}</div> -->
                                <div class="level-rate" :class="{ 'low-rate': levelItem.correctRate < 90 }" v-if="levelItem.correctRate">{{ formatCorrectRate(levelItem.correctRate) }}%</div>
                              </div>
                            </div>
                            <el-icon v-if="getFilteredLevelVos(knowledge.levelVos) && levelIndex < getFilteredLevelVos(knowledge.levelVos).length - 1">
                              <CaretRight color="#EAEAEA" />
                            </el-icon>
                          </div>
                        </template>
                        
                        <!-- 兜底显示：如果没有levelVos数据，显示原来的六边形 -->
                        <template v-else>
                    <!-- 基础训练六边形 -->
                    <div class="hexagon-wrapper">
                      <div class="hexagon-bg" :class="getHexagonBgClass(knowledge.correctRate || 0, 'basic')">
                        <div class="hexagon-content">
                          
                                <img v-if="(knowledge.correctRate || 0) >= 100"
                               :src="getSmallMedalIcon(1)"
                               class="medal-crown" />
                                <div class="percentage-text">{{ formatCorrectRate(knowledge.correctRate || 0) }}%</div>
                        </div>
                      </div>
                    </div>

                    <el-icon><CaretRight color="#EAEAEA" /></el-icon>
                    <!-- 进阶训练六边形 -->
                    <div class="hexagon-wrapper">
                            <div class="hexagon-bg" :class="getHexagonBgClass(knowledge.correctRate || 0, 'advanced')">
                        <div class="hexagon-content">
                                <div class="percentage-text">{{ formatCorrectRate(knowledge.correctRate || 0) }}%</div>
                                <img v-if="(knowledge.correctRate || 0) >= 100"
                               :src="getSmallMedalIcon(2)"
                               class="medal-crown" />
                        </div>
                      </div>
                    </div>
                        </template>
                  </div>
                </div>
                <div class="action-area">
                      <div class="action-btn study" @click="toPractice(knowledge)">
                    <img src="@/assets/img/percision/training/play.png" class="action-icon" />
                    去学习
                  </div>
                      <div class="action-btn practice" @click="toRecord(knowledge)">
                    <img src="@/assets/img/percision/training/practice.png" class="action-icon"  />
                    练习记录
                  </div>
                </div>
                
                    <!-- 知识点状态指示器 -->
                    <!-- <div class="knowledge-status" :class="getKnowledgeStatusClass(knowledge)">
                      {{ getKnowledgeStatusText(knowledge) }}
                    </div> -->
              </div>
            </div>

            <!-- 挑战按钮区域 -->
            <div class="challenge-area">
              <!-- 根据状态显示不同内容 -->
              <template v-if="getChallengeButtonStatus({knowledgePoints: getLessonKnowledgePoints(lessonIndex)}).showButton">
                <!-- 显示挑战按钮 -->
                <div class="challenge-btn" 
                  @click="handleChallengeClick({id: lessonIndex + 1, knowledgePoints: getLessonKnowledgePoints(lessonIndex)})">
                   {{ getChallengeButtonText({knowledgePoints: getLessonKnowledgePoints(lessonIndex)}) }}
              </div>
              </template>
              <template v-else>
                <!-- 显示状态图片 -->
                <div class="challenge-status-image">
                  <img 
                    :src="getChallengeButtonStatus({knowledgePoints: getLessonKnowledgePoints(lessonIndex)}).image"
                    :alt="getChallengeButtonStatus({knowledgePoints: getLessonKnowledgePoints(lessonIndex)}).type"
                    class="status-icon"
                    @click="handleStatusImageClick(getChallengeButtonStatus({knowledgePoints: getLessonKnowledgePoints(lessonIndex)}), {id: lessonIndex + 1, knowledgePoints: getLessonKnowledgePoints(lessonIndex)})"
                  />
            </div>
              </template>

            </div> 
              </div>
            </div>
            </div>
          </div>

        <div v-else class="empty-state">
          <img width="112px" height="138px" src="@/assets/img/synchronous/empty.png" alt="">
          <span class="empty-message" style="display: block;">暂无知识点数据</span>
        </div>
      </div>
      <!-- 单元测试 -->
      <div v-else v-loading="loading">
          <div class="test-box">
            <div class="test-wrap" v-for="item in testList">
              <div class="test-box-item">
                <div class="test-box-item-img">
                  <span class="red-text" v-if="item.score&&item.score!='0'">{{item.score}}分</span>
                </div>
                <div class="test-box-item-info">
                  <div class="test-box-item-info-title">
                    {{item.title}}
                  </div>
                  <div class="test-box-item-info-data">
                    <div>更新时间：{{item.reportDate	}}</div>
                    <div>浏览：{{ item.viewCount }}</div>
                  </div>
                </div>
                <div class="test-box-item-btn">
                  <div class="test-box-item-btn-it btn" @click="handleDownload(item)">
                    <img src="@/assets/img/percision/download.png" alt=""> 下载
                  </div>
                  <div class="test-box-item-btn-it blue-text" @click="testDetail(item)">
                    查看详情>
                  </div>
                </div>
              </div>
              <div class="hui-line"></div>
            </div>
          </div>
          <div class="pagination-box">
            <Pagination
                :total="pageData.total"
                :current="pageData.current"
                @currentSizeChange="currentSizeChange"
                @pageClick="pageClick"/>
          </div>
        </div>

      <!-- 知识点详情弹窗 -->
      <el-dialog
        v-model="knowledgeDetailVisible"
        :title="selectedKnowledge?.name || '知识点详情'"
        width="60%"
        :before-close="handleDetailClose"
      >
        <div class="knowledge-detail-content" v-if="selectedKnowledge">
          <div v-html="selectedKnowledge.desc"></div>
      </div>
      </el-dialog>
    </div>
    <div v-if="challengePop" class="elevate-overlay">
      <div class="elevate-ct" :style="{ backgroundImage: `url(${currentChallengeInfo.backgroundImage})` }">
        <div class="close-btn" @click="challengePop = false">
          <img src="@/assets/img/percision/training/hscc.png" alt="">
  </div>
        <!-- {{ currentChallengeInfo.levelName }} -->
        <div class="top-title">本次检测 {{ knowledgeList?.length || 0 }}个知识点</div>
        <div class="block-ct">
          <div 
            class="book-list" 
            v-for="(point, index) in knowledgeList" 
            :key="index"
          >
          <!-- {{ knowledgeList }} -->
            <img src="@/assets/img/percision/training/bookzsd.png" alt="">
            <div class="book-name">{{ index + 1 }}.{{ point.pointName }}</div>
            <div class="book-tl">题量<span class="num">{{ point.quesCount || 3 }}</span></div>
            <div class="book-tl">难度<span class="num">{{ point.degree }}</span></div>
          </div>
          <div class="prompt"> 共 <span>{{ knowledgeOll.quesCount }}</span>道题，要求 <span>{{ knowledgeOll.times }}</span> 分钟内完成</div>
          </div>
        <div class="challenge-fq">向<img :src="currentChallengeInfo.levelImage" :alt="currentChallengeInfo.levelName" class="challenge-level-icon">发起挑战吧，正确率≥90%即可过关！</div>
        <div class="book-challenge" @click="onChallenge">开始挑战</div>
        </div>
      </div>
    </div>
    <!-- 下载试卷 -->
  <downloadTrestDialog v-if="dialogVisible" ref="downloadTrestDialogRef" :paper-detail="dowmloadData" />
</template>

<script lang="ts" setup>
import { useRouter ,useRoute} from 'vue-router'
import knowledgeTree from "@/views/components/knowledgeTree/trainingTree.vue"
import { getBookChapterListApi, getMasteryApi, getBookChapterListsApi, getPointCategoryApi,getChapterListApi,getpointListApi } from "@/api/book"
import {  addTrainingApi, trainingInfoApi,detailsToPointApi} from "@/api/precise"
import { computed, nextTick, onMounted, onUnmounted, reactive, ref, watch } from 'vue'
import { getChapterReportListApi } from "@/api/report"
import { useUserStore } from "@/store/modules/user"
import { dataEncrypt, dataDecrypt } from "@/utils/secret"
import { storeToRefs } from 'pinia'
import { CaretRight } from '@element-plus/icons-vue'
const userStore = useUserStore()
const pathName=ref()
const { subjectObj, learnNow } = storeToRefs(userStore)
const learnUsers = JSON.parse(localStorage.learnUsers || "[]") || []
const router = useRouter()
const route = useRoute()
const query = reactive<any>(route.query)
const loading = ref(false)
const curChapterId =ref()
const loading2 = ref(false)
const pageStatus = ref(true)
const challengePop = ref(false)
const subjectEn=query.subjectEn
const trainingId = ref()
import downloadTrestDialog from "@/components/TruePaperDownload/index.vue"
const curLevel = ref()
// 存储选中的子级ID数组
const selectedChildIds = ref<string[]>([])

const knowledgeTreeRef = ref() // 添加ref用于访问知识树组件

const routeHasChapterId = ref(false) // 标记是否从路由获取了章节ID
const isTargetChapterId = ref()
const chapterPath = ref<any>([]) // 存储当前章节路径
const forceUpdatePath = ref(0) // 强制更新路径的计数器
const isDataLoaded = ref(false) // 标记数据是否已经加载过，避免重复调用API
// 本地缓存相关 - 参考 knowledge_graph_detail 的实现
const CACHE_KEY = 'ai_precision_basic_training_selected_chapter'

const isStatus = ref(true)

// 检查指定课程中所有知识点的correctRate是否有值
const checkLessonCorrectRateStatus = (lessonIndex: number): boolean => {
  const lessonKnowledgePoints = getLessonKnowledgePoints(lessonIndex)
  
  if (!lessonKnowledgePoints || lessonKnowledgePoints.length === 0) {
    return false
  }
  
  // 检查当前课程中所有知识点的correctRate
  for (const knowledge of lessonKnowledgePoints) {
    // 检查知识点本身的correctRate
    if (knowledge.correctRate !== null && knowledge.correctRate !== undefined && knowledge.correctRate !== 0) {
      continue // 这个知识点有correctRate，继续检查下一个
    }
    
    // 检查levelVos中的correctRate
    let hasCorrectRateInLevelVos = false
    if (knowledge.levelVos && knowledge.levelVos.length > 0) {
      for (const levelVo of knowledge.levelVos) {
        if (levelVo.correctRate !== null && levelVo.correctRate !== undefined && levelVo.correctRate !== 0) {
          hasCorrectRateInLevelVos = true
          break
        }
      }
    }
    
    // 如果知识点本身和levelVos中都没有有效的correctRate，返回false
    if (!hasCorrectRateInLevelVos) {
      return false
    }
  }
  
  // 所有知识点都有correctRate值
  return true
}

// 获取指定课程的correctRate状态
const getLessonCorrectRateStatus = (lessonIndex: number): boolean => {
  const status = checkLessonCorrectRateStatus(lessonIndex)

  return status
}
// 根据模式获取存储的选中信息（包含id和name）
const getStoredSelectedInfo = () => {
  const stored = localStorage.getItem(CACHE_KEY)
  try {
    return stored ? JSON.parse(stored) : null
  } catch (error) {
    console.error('解析存储的选中信息失败:', error)
    return null
  }
}

// 根据模式存储选中的信息（包含id和name）
const setStoredSelectedInfo = (id: string, name: string) => {
  const selectedInfo = { id, name }
  localStorage.setItem(CACHE_KEY, JSON.stringify(selectedInfo))
}

// 清除存储的选中信息
const clearStoredSelectedInfo = () => {
  localStorage.removeItem(CACHE_KEY)
}

// 初始化章节名称，优先使用存储的名称
const initChapterName = () => {
  const storedInfo = getStoredSelectedInfo()
  if (storedInfo && storedInfo.name) {
    return storedInfo.name
  }else{
    return userStore.chapterObj.chapterName
  }
 
}

const chapterName = ref(initChapterName())

// 初始化chapterId，优先使用存储的ID
const initChapterId = () => {
  const storedInfo = getStoredSelectedInfo()
  if (storedInfo) {
    return storedInfo.id
  }
  return userStore.chapterObj.chapterId
}

const chapterId = ref(initChapterId())

const selectName = computed(() => {
  return chapterName.value
})
// 查找节点是否存在 - 优化版：当chapterId相同时，优先选择非"单元测试"的节点
const findNodeById = (nodes: any[], targetId: string): any => {
  let foundNodes: any[] = []
  
  // 递归收集所有匹配的节点
  const collectMatchingNodes = (nodeList: any[]) => {
    for (const node of nodeList) {
      const nodeId = node.id || node.chapterId
      if (nodeId === targetId) {
        foundNodes.push(node)
      }
      if (node.children && node.children.length > 0) {
        collectMatchingNodes(node.children)
      }
    }
  }
  
  collectMatchingNodes(nodes)
  
  // 如果没有找到匹配的节点，返回null
  if (foundNodes.length === 0) {
    return null
  }
  
  // 如果只有一个节点，直接返回
  if (foundNodes.length === 1) {
    return foundNodes[0]
  }
  
  // 如果有多个节点，优先选择非"单元测试"的节点
  const nonTestNodes = foundNodes.filter(node => {
    const nodeName = node.chapterName || node.name || ''
    return nodeName !== "单元测试"
  })
  
  // 如果有非"单元测试"的节点，返回第一个
  if (nonTestNodes.length > 0) {
    return nonTestNodes[0]
  }
  
  // 如果所有节点都是"单元测试"，返回第一个
  return foundNodes[0]
}

// 查找第一个叶子节点 - 优化版：当chapterId相同时，优先选择非"单元测试"的节点
const findFirstLeafNode = (nodes: any[]): any => {
  let allLeafNodes: any[] = []
  
  // 递归收集所有叶子节点
  const collectLeafNodes = (nodeList: any[]) => {
    for (const node of nodeList) {
      if (node.children && node.children.length > 0) {
        collectLeafNodes(node.children)
      } else {
        allLeafNodes.push(node)
      }
    }
  }
  
  collectLeafNodes(nodes)
  
  if (allLeafNodes.length === 0) {
    return null
  }
  
  // 按chapterId分组
  const nodeGroups = new Map<string, any[]>()
  allLeafNodes.forEach(node => {
    const nodeId = node.id || node.chapterId
    if (!nodeGroups.has(nodeId)) {
      nodeGroups.set(nodeId, [])
    }
    nodeGroups.get(nodeId)!.push(node)
  })
  
  // 对于每个组，优先选择非"单元测试"的节点
  const preferredNodes: any[] = []
  nodeGroups.forEach((group, chapterId) => {
    if (group.length === 1) {
      preferredNodes.push(group[0])
    } else {
      // 有多个相同chapterId的节点，优先选择非"单元测试"的
      const nonTestNodes = group.filter(node => {
        const nodeName = node.chapterName || node.name || ''
        return nodeName !== "单元测试"
      })
      
      if (nonTestNodes.length > 0) {
        preferredNodes.push(nonTestNodes[0])
      } else {
        preferredNodes.push(group[0])
      }
    }
  })
  
  // 返回第一个优选节点
  return preferredNodes.length > 0 ? preferredNodes[0] : allLeafNodes[0]
}

const knowledgeList = ref<any>([])
const knowledgeOll = reactive({
  times: 120, // 时间，单位：分钟（从API返回的毫秒数转换而来）
  quesCount: 1 // 总题目数量
})

// 监听chapterId变化，用于调试
watch(() => chapterId.value, (newValue, oldValue) => {
  if (newValue) {
    // 强制刷新路径计算
    forceUpdatePath.value++
  }
}, { immediate: true })

// 监听chapterPath变化
watch(() => chapterPath.value, (newPath, oldPath) => {
  // 强制刷新路径计算
  forceUpdatePath.value++
}, { immediate: true, deep: true })

const testList = ref([] as any[])
// 计算属性：格式化章节路径文本 - 优化版
const chapterPathText = computed(() => {
  // 如果没有选中章节ID，返回默认文本
  if (!chapterId.value) {
    return "请选择章节"
  }
  
  // 如果没有章节数据，返回默认文本
  if (!options.value || options.value.length === 0) {
    return "请选择章节"
  }
  
  // 查找完整路径
  const fullPath = findChapterFullPath(options.value, chapterId.value)
  
  if (fullPath && fullPath.length > 0) {
    const pathText = buildSimplePathText(fullPath)
    
    // 更新全局路径变量
    chapterPath.value = fullPath
    
    return pathText
  }
  
  return "未找到章节"
})

// 查找章节完整路径 - 新的简化版本
const findChapterFullPath = (chapters: any[], targetId: string): any[] | null => {
  if (!chapters || chapters.length === 0) {
    return null
  }
  
  const searchPath = (nodes: any[], currentPath: any[] = [], depth: number = 0): any[] | null => {
    for (let i = 0; i < nodes.length; i++) {
      const chapter = nodes[i]
      const newPath = [...currentPath, chapter]
      
      // 类型转换比较 - 确保类型一致
      const chapterIdStr = String(chapter.id)
      const targetIdStr = String(targetId)
      
      // 额外的数字比较（处理大数字精度问题）
      const chapterIdNum = Number(chapter.id)
      const targetIdNum = Number(targetId)
      
      const isMatch = chapterIdStr === targetIdStr || chapterIdNum === targetIdNum
      
      // 如果找到目标章节
      if (isMatch) {
        return newPath
      }
      
      // 如果有子章节，递归搜索
      if (chapter.children && Array.isArray(chapter.children) && chapter.children.length > 0) {
        const result = searchPath(chapter.children, newPath, depth + 1)
        if (result) {
          return result
        }
      }
    }
    
    return null
  }
  
  const result = searchPath(chapters)
  return result
}

// 构建简单路径文本
const buildSimplePathText = (pathArray: any[]): string => {
  if (!pathArray || pathArray.length === 0) {
    return ""
  }
  
  const pathNames = pathArray
    .map((chapter, index) => {
      // 获取章节名称，优先级：chapterName > name > title
      const name = chapter.chapterName || chapter.name || chapter.title || `章节${index + 1}`
      return name.trim()
    })
    .filter(name => {
      const isValid = name && name.length > 0
      return isValid
    })
  
  const result = pathNames.join(' > ')
  return result
}

// 调试章节数据结构的函数
const debugChapterStructure = (chapters: any[], maxDepth: number = 3, currentDepth: number = 0) => {
  if (!chapters || chapters.length === 0 || currentDepth >= maxDepth) {
    return
  }
  
  chapters.forEach((chapter, index) => {
    if (chapter.children && chapter.children.length > 0 && currentDepth < maxDepth - 1) {
      debugChapterStructure(chapter.children, maxDepth, currentDepth + 1)
    }
  })
}

// 构建路径文本的辅助函数 - 优化版
const buildPathText = (pathArray) => {
  if (!pathArray || pathArray.length === 0) {
    return ""
  }
  
  // 处理路径数组，确保获取正确的章节名称
  const pathNames = pathArray
    .map((chapter, index) => {
      // 多重字段检查，确保获取到正确的名称
      let name = ''
      
      // 按优先级获取名称
      if (chapter.chapterName && chapter.chapterName.trim()) {
        name = chapter.chapterName.trim()
      } else if (chapter.name && chapter.name.trim()) {
        name = chapter.name.trim()
      } else if (chapter.title && chapter.title.trim()) {
        name = chapter.title.trim()
      } else {
        name = `未命名章节${index + 1}`
      }
      
      
      return name
    })
    .filter(name => {
      // 过滤掉无效名称
      const isValid = name && 
                      name !== '' && 
                      name !== '未命名章节' && 
                      !name.startsWith('未命名章节') && 
                      name.trim().length > 0
      
      return isValid
    })
  
  // 构建最终路径文本
  const result = pathNames.length > 0 ? pathNames.join(' > ') : ""
  
  return result
}

// 查找目标章节的辅助函数
const findTargetChapter = (chapters, targetId) => {
  
  const search = (nodes, path = []) => {
    for (const chapter of nodes) {
      const currentPath:any = [...path, chapter]
      if (chapter.id === targetId) {
        return { chapter, fullPath: currentPath }
      }
      
      if (chapter.children && chapter.children.length > 0) {
        const result = search(chapter.children, currentPath)
        if (result) return result
      }
    }
    return null
  }
  
  const result = search(chapters)
  return result ? result.chapter : null
}


// 新增：直接获取完整路径的函数
const getFullChapterPath = (chapters, targetId) => {
  
  // 首先调试章节结构
  debugChapterStructure(chapters, 2)
  
  const search = (nodes, path = []) => {
    for (const chapter of nodes) {
      const currentPath:any = [...path, chapter]
      
      if (chapter.id === targetId) {
        return currentPath
      }
      
      if (chapter.children && chapter.children.length > 0) {
        const result = search(chapter.children, currentPath)
        if (result) return result
      }
    }
    return null
  }
  
  const result = search(chapters)
  return result
}

const chapterData = reactive<any>({
  percentage1: null,
  percentage1i: null,
  strong1: false,
  rate1: 1,
  percentage2: null,
  percentage2i: null,
  strong2: false,
  rate2: 2,
  percentage3: null,
  percentage3i: null,
  strong3: false,
  rate3: 3,
  percentage0: null,
  percentage0i: null,
  strong0: false,
})
const options = ref([])

// 定义等级数据接口
interface LevelVo {
  level: number | null
  correctRate: any
  status: number // 状态字段：0-未开始，1-已完成，2-挑战过但未通过
  digestiveMarkers?: any // 🎯 添加错题消化标记字段
}

// 定义知识点接口
interface KnowledgePoint {
  id: string
  name: string
  correctRate?: any
  studyStatus?: number
  status?: number
  difficulty?: number
  levelVos?: LevelVo[]
  basicProgress?: number
  advancedProgress?: number
  isCompleted?: boolean
  completionLevel?: string | null
  total?: number
  desc?: string // 知识点描述
  digestiveMarkers?: any // 错题记录
  [key: string]: any // 允许其他动态属性
}

// 课程数据结构 - 直接使用知识点数组
const lessonData = ref<KnowledgePoint[]>([
      {
        id: 'k1',
    name: '知识点名称1',
    correctRate: 89.4,
    studyStatus: 0,
    status: 5,
    levelVos: [
      { level: 1, correctRate: 89.4, status: 1 }, // 青铜已完成，可以挑战白银
      { level: 2, correctRate: 0, status: 0 }
    ]
      },
      {
        id: 'k2', 
    name: '知识点名称2',
    correctRate: 100,
    studyStatus: 1,
    status: 1,
    levelVos: [
      { level: 1, correctRate: 100, status: 1 }, // 青铜已完成，可以挑战白银
      { level: 2, correctRate: 95, status: 0 }
    ]
  },
      {
        id: 'k3',
    name: '知识点名称3',
    correctRate: 60,
    studyStatus: 0,
    status: 2,
    levelVos: [
      { level: 1, correctRate: 60, status: 2 }, // 青铜挑战过但未通过，需要再次挑战
      { level: 2, correctRate: 0, status: 0 }
    ]
      },
      {
        id: 'k4',
    name: '知识点名称4',
    correctRate: 65,
    studyStatus: 0,
    status: 2,
    levelVos: [
      { level: 1, correctRate: 65, status: 2 }, // 青铜挑战过但未通过，需要再次挑战
      { level: 2, correctRate: 0, status: 0 }
    ]
  },
      {
        id: 'k5',
    name: '知识点名称5',
    correctRate: 80,
    studyStatus: 0,
    status: 2,
    levelVos: [
      { level: 2, correctRate: 80, status: 2 }, // 白银挑战过但未通过，需要再次挑战
      { level: 3, correctRate: 0, status: 0 }
    ]
      },
      {
        id: 'k6',
    name: '知识点名称6',
    correctRate: 85,
    studyStatus: 0,
    status: 2,
    levelVos: [
      { level: 2, correctRate: 85, status: 2 }, // 白银挑战过但未通过，需要再次挑战
      { level: 3, correctRate: 0, status: 0 }
    ]
  },
  {
    id: 'k7',
    name: '知识点名称7（含null等级）',
    correctRate: 0,
    studyStatus: 0,
    status: 5,
    levelVos: [
      { level: null, correctRate: 0, status: 0 }, // null等级，应该显示灰色图片
      { level: 1, correctRate: 0, status: 0 }
    ]
  },
  {
    id: 'k8',
    name: '知识点名称8（测试格式化）',
    correctRate: 95.00, // 测试.00格式
    studyStatus: 1,
    status: 1,
    levelVos: [
      { level: 1, correctRate: 95.00, status: 1 }, // 测试95.00显示为95
      { level: 2, correctRate: 87.50, status: 0 }  // 测试87.50正常显示
    ]
  }
])

// 处理挑战按钮点击
const handleChallengeClick = (lesson: { id: number; knowledgePoints: KnowledgePoint[] }) => {
  
  // 只获取当前课程的知识点ID列表，不累积之前的
  const currentPointIds = lesson.knowledgePoints
    .filter((point: KnowledgePoint) => point.id) // 确保ID存在
    .map((point: KnowledgePoint) => point.id);
  // 根据当前课程知识点状态计算挑战等级
  const challengeLevel = getChallengeLevel(lesson);
  const challengeLevelName = getLevelName(challengeLevel);
  
  // 获取挑战按钮文本用于调试验证
  const challengeButtonText = getChallengeButtonText(lesson);
  
  // 清空之前的选中ID，只使用当前课程的ID
  selectedChildIds.value = [...currentPointIds];
  curLevel.value = challengeLevel.toString() 
  addTrainingApi({
    bookId: query.bookId,
    pointIds: currentPointIds, // 直接使用当前课程的知识点ID
    subject:query.subject,
    chapterId:chapterId.value,
    level: challengeLevel.toString() // 动态传递挑战等级：1青铜 2白银 3黄金 4钻石

  }).then((res:any) => {
    if (res.code === 200) {
      if(res.data){
        trainingId.value = res.data
          trainingInfoApi({
           trainingId: res.data
         }).then((res: any) => {
           if (res.code == 200) {
             
             // 处理时间转换：将毫秒转换为分钟
             const timeInMilliseconds = res.data.times || 0;
             const timeInMinutes = convertMillisecondsToMinutes(timeInMilliseconds);
             
             // 显示挑战弹窗
             knowledgeList.value = res.data.pointItems
             challengePop.value = true;
             knowledgeOll.times = timeInMinutes; // 存储转换后的分钟数
             knowledgeOll.quesCount = res?.data?.quesCount;
           }
       })
    } 
  }
  })

  // 设置弹窗数据
  selectedLessonForChallenge.value = {
    id: lesson.id,
    knowledgePoints: lesson.knowledgePoints,
    pointIds: currentPointIds, // 使用当前课程的知识点ID
    currentChildIds: currentPointIds // 当前选中的子级ID就是当前课程的ID
  };
}

// 处理状态图片点击事件
const handleStatusImageClick = (buttonStatus: any, lesson: { id: number; knowledgePoints: KnowledgePoint[] }) => {
  // 🔍 统一字段：收集当前课程所有知识点ID
  const allPointIds: string[] = lesson.knowledgePoints
    .filter((knowledge: KnowledgePoint) => knowledge.id) // 确保ID存在
    .map((knowledge: KnowledgePoint) => knowledge.id);
  
  // 确定source字段
  let source = '0'
  if (query.type == '1') {
    source = '117' // 基础训练
  } else {
    source = '118' // 提升训练
  }
  
  if (buttonStatus.type === 'wrong_questions') {
    
    // 跳转到错题复习页面
    router.push({
      path: '/ai_percision/wrongList',
      query: {                    
        source: source,
        subject: query.subject,
        subjectEn: subjectObj.value.subject,
        allPointIds: allPointIds,
        curChapterId: curChapterId.value,
        num: allPointIds.length
      }
    })
    
    // 收集所有知识点的错题信息（用于其他用途）
    const wrongQuestions: any[] = []
    lesson.knowledgePoints.forEach(knowledge => {
      if (knowledge.digestiveMarkers) {
        if (Array.isArray(knowledge.digestiveMarkers) && knowledge.digestiveMarkers.length > 0) {
          wrongQuestions.push(...knowledge.digestiveMarkers)
        } else if (!Array.isArray(knowledge.digestiveMarkers)) {
          wrongQuestions.push(knowledge.digestiveMarkers)
        }
      }
    })
    
  } else if (buttonStatus.type === 'perfect') {
    // 可以在这里添加完美过关的庆祝逻辑
  }
}

// 存储当前选中的课程数据，用于挑战
const selectedLessonForChallenge = ref<any>(null);
  const goBack = () => {
    // 获取当前路由历史记录
    const currentHistory = window.history
    const referrer = document.referrer  
    
    // 检查是否从训练报告页面来的
    const isFromTrainingReport = checkIfFromTrainingReport()
    
    if (isFromTrainingReport) {
      
      // 构建knowledge_graph页面的路由参数
      const knowledgeGraphQuery = {
        bookId: query.bookId || subjectObj.value.bookId,
        subject: query.subject,
        chapterId: chapterId.value,
        type: query.type
      }
      
      router.push({
        path: '/ai_percision/knowledge_graph',
        query: knowledgeGraphQuery
      })
    } else {
      router.go(-1)
    }
  }
  
  // 检查是否从训练报告页面来的
  const checkIfFromTrainingReport = () => {
    // 方法1: 检查路由查询参数中是否有特定标识
    if (query.from === 'training_report' || query.pageSource === 'training_report') {
      return true
    }
    
    // 方法2: 检查sessionStorage中的页面来源标记
    const pageSource = sessionStorage.getItem('basic_training_source')
    if (pageSource) {
      if (pageSource.includes('training_report') || 
          pageSource.includes('paper_analysis') ||
          pageSource.includes('regarding_learning') ||
          pageSource.includes('foundation_report')) {
        // 清除标记，避免影响后续导航
        sessionStorage.removeItem('basic_training_source')
        return true
      }
    }
    
    // 方法3: 检查referrer URL
    const referrer = document.referrer
    if (referrer) {
      try {
        const referrerUrl = new URL(referrer)
        const referrerPath = referrerUrl.pathname
        
        
        // 检查是否包含训练报告相关的路径
        const trainingReportPaths = [
          '/ai_percision/training_report',
          '/ai_percision/paper_analysis', 
          '/ai_percision/regarding_learning',
          '/ai_percision/foundation_report'
        ]
        
        for (const path of trainingReportPaths) {
          if (referrerPath.includes(path)) {
            return true
          }
        }
      } catch (error) {
      }
    }
    
    // 方法4: 检查Vue Router的历史记录
    try {
      const history = router.options.history
      if (history && history.state) {
        const historyState = history.state
        
        // 检查历史状态中是否有相关信息
        if (historyState.back && typeof historyState.back === 'string') {
          const backRoute = historyState.back
          
          if (backRoute.includes('training_report') || 
              backRoute.includes('paper_analysis') ||
              backRoute.includes('regarding_learning') ||
              backRoute.includes('foundation_report')) {
            return true
          }
        }
      }
    } catch (error) {
    }
    
    return false
  }

  // 设置页面来源标记，供其他页面检测导航来源
  const setPageSourceForNavigation = () => {
    const currentPath = route.path 
    // 为当前页面设置标记，供其他页面检测
    sessionStorage.setItem('navigation_source', currentPath)
    
    // 如果是从训练报告相关页面来的，设置特殊标记
    const referrer = document.referrer
    if (referrer) {
      try {
        const referrerUrl = new URL(referrer)
        const referrerPath = referrerUrl.pathname   
        const trainingReportPaths = [
          '/ai_percision/training_report',
          '/ai_percision/paper_analysis',
          '/ai_percision/regarding_learning', 
          '/ai_percision/foundation_report'
        ]
        
        for (const path of trainingReportPaths) {
          if (referrerPath.includes(path)) {
            sessionStorage.setItem('basic_training_source', referrerPath)
            break
          }
        }
      } catch (error) {
      }
    }
  }


// 将毫秒转换为分钟的辅助函数
const convertMillisecondsToMinutes = (milliseconds: number): number => {
  if (!milliseconds || milliseconds <= 0) {
    return 2; // 默认2分钟
  }
  
  // 将毫秒转换为分钟：毫秒 ÷ 1000 ÷ 60
  const minutes = Math.ceil(milliseconds / 1000 / 60); // 向上取整
  
  // 确保最少1分钟
  return Math.max(minutes, 1);
};

// 开始挑战
const onChallenge = () => {
  // 显示挑战等级信息
  if (selectedLessonForChallenge.value) {
    const challengeLevel = getChallengeLevel(selectedLessonForChallenge.value);
    const challengeLevelName = getLevelName(challengeLevel);
  }
  
    router.push({
      path: '/ai_percision/answer_training',
      // path: '/ai_percision/entrance_assessment/doing_exercises',
      query: {
        data: dataEncrypt({
          reportId: trainingId.value ,
          pageSource: '1',
          bookId: subjectObj.value.bookId,
          chapterId:chapterId.value,
          level:curLevel.value,
          type:query.type,
          subject:query.subject,
          subjectEn:subjectObj.value.subject,
          curChapterId:curChapterId.value,
          selectedChildIds:selectedChildIds.value,
          learn:'record',
        }),
      }
    })
}

const getUrl = (item: any) => {
    let url = 'pen'
    if (item > 90) {
        url = 'strong'
    }
    return new URL(`../../../assets/img/percision/${url}.png`, import.meta.url).href //静态资源引入为url，相当于require()
}
// 递归查找章节，并返回包含该章节的完整路径
const findChapterById = (chapters:any, targetId:any, path:any = []) => {
  let deepestPath: any[] | null = null;

  for (const chapter of chapters) {
    // 确保章节有chapterName属性
    if (!chapter.chapterName && chapter.name) {
      chapter.chapterName = chapter.name
    }
    
    // 创建当前路径的副本
    const currentPath:any = [...path, chapter]

    // 如果找到目标章节，记录当前路径
    if (chapter.id === targetId) {
      // 记录找到的路径，但不立即返回
      deepestPath = currentPath;
      // 更新全局的章节路径
      chapterPath.value = deepestPath;
    }

    // 无论是否已找到匹配节点，都继续递归查找子节点
    if (chapter.children && chapter.children.length > 0) {
      const childResult: any[] | null = findChapterById(chapter.children, targetId, currentPath)
      // 如果子节点中找到了结果，优先使用子节点的结果（更深层次）
      if (childResult) {
        // 如果已经有记录但找到了更深的路径，或者还没有记录
        if (!deepestPath || childResult.length > deepestPath.length) {
          deepestPath = childResult;
          // 更新全局的章节路径
          chapterPath.value = deepestPath;
        }
      }
    }
  }

  return deepestPath;
}

onMounted(async () => {
  // 设置页面来源标记，供其他页面检测导航来源
  setPageSourceForNavigation()
  
  // 从路由参数中获取章节ID
  let targetChapterId = null

  if (query.chapterId) {
    targetChapterId = query.chapterId
  } else if (query.data) {
    try {
      const decryptedData = dataDecrypt(query.data)
      if (decryptedData && decryptedData.chapterId) {
        targetChapterId = decryptedData.chapterId
      }
    } catch (error) {
      console.error('Failed to decrypt query data:', error)
    }
  }

  // 获取章节列表，传入目标章节ID，避免重复调用
  await getBookChapterList(targetChapterId || undefined)
  
  // 延迟打印课程数据，确保数据已经加载完成
  setTimeout(() => {
    printLessonData()
    // 专门打印digestiveMarkers信息
    printCurrentLessonDigestiveMarkers()
  }, 1000)
})

// 智能选择节点：优先选择非"单元测试"的节点
const selectOptimalNode = (targetNode: any) => {
  if (!targetNode) return
  
  const nodeId = targetNode.id || targetNode.chapterId
  const nodeName = targetNode.chapterName || targetNode.name
  
  // 检查是否有相同chapterId的其他节点
  const allMatchingNodes = findAllNodesByChapterId(options.value, nodeId)
  let selectedNode = targetNode
  
  // 如果有多个相同chapterId的节点，优先选择非"单元测试"的节点
  if (allMatchingNodes.length > 1) {
    const nonTestNodes = allMatchingNodes.filter(node => {
      const name = node.chapterName || node.name || ''
      return name ==chapterName.value
    })
    
    if (nonTestNodes.length > 0) {
      selectedNode = nonTestNodes[0]
    }
  }
  
  const finalNodeId = selectedNode.id || selectedNode.chapterId
  const finalNodeName = selectedNode.chapterName || selectedNode.name
  
  chapterId.value = finalNodeId
  chapterName.value = finalNodeName
  pageStatus.value = finalNodeName !== "单元测试"
  isTargetChapterId.value = finalNodeName
  // 存储选中的信息
  setStoredSelectedInfo(finalNodeId, finalNodeName)

  // 使用nextTick确保DOM更新后再调用
  nextTick(() => {
    setChapterId(selectedNode, finalNodeName)
    // 延迟滚动到选中节点，确保DOM完全渲染
    setTimeout(() => {
      scrollToSelectedNode()
    }, 100)
  })
  
  // 根据页面状态决定加载什么数据
  if (!pageStatus.value) {
    getChapterReportList()
  } else {
    getMastery()
  }
  
  // 暴露调试功能到全局对象
  ;(window as any).debugBasicTraining = {
    findParentInfo,
    printCurrentSelectionInfo: () => {
      const currentNodeId = chapterId.value
      if (currentNodeId) {
        console.log('🔍 手动打印当前选中信息:')
        const parentInfo = findParentInfo(options.value, currentNodeId)
        const currentNode = findNodeById(options.value, currentNodeId)
        
        if (currentNode) {
          console.group('🎯 当前选中信息')
          console.log('📍 当前节点:', {
            id: currentNodeId,
            name: chapterName.value,
            data: currentNode
          })
          
          if (parentInfo) {
            console.log('👨‍👩‍👧‍👦 父级信息:', parentInfo)
          } else {
            console.log('👨‍👩‍👧‍👦 父级信息: 顶级节点')
          }
          
          if (currentNode.children && currentNode.children.length > 0) {
            console.log('👶 子级信息:', currentNode.children.map((child: any, index: number) => ({
              index: index + 1,
              id: child.id || child.chapterId,
              name: child.chapterName || child.name
            })))
          } else {
            console.log('👶 子级信息: 叶子节点')
          }
          console.groupEnd()
        }
      } else {
        console.warn('⚠️ 没有选中任何节点')
      }
    },
    // 手动打印课程数据的快捷方法
    printLessonData: () => {
      console.log('🎯 手动调用课程数据打印...')
      printLessonData()
    },
    // 打印指定课程的数据
    printSpecificLesson: (lessonIndex: number) => {
      const totalLessonsCount = totalLessons.value
      if (lessonIndex < 0 || lessonIndex >= totalLessonsCount) {
        console.error(`❌ 课程索引无效: ${lessonIndex}, 有效范围: 0-${totalLessonsCount - 1}`)
        return
      }
      
      const lessonNumber = lessonIndex + 1
      const lessonKnowledgePoints = getLessonKnowledgePoints(lessonIndex)
      
      console.group(`🎯 单独打印第${lessonNumber}课数据`)
      console.log('📋 课程信息:', {
        lessonNumber,
        lessonIndex,
        knowledgePointCount: lessonKnowledgePoints.length,
        knowledgePoints: lessonKnowledgePoints.map(kp => ({
          id: kp.id,
          name: kp.name,
          correctRate: kp.correctRate,
          levelVosCount: kp.levelVos?.length || 0
        }))
      })
      
      const debugLessonData = { id: lessonNumber, knowledgePoints: lessonKnowledgePoints }
      const debugChallengeButtonText = getChallengeButtonText(debugLessonData)
      const debugChallengeLevel = getChallengeLevel(debugLessonData)
      const debugButtonStatus = getChallengeButtonStatus(debugLessonData)
      
      console.log('🎮 挑战信息:', {
        challengeButtonText: debugChallengeButtonText,
        challengeLevel: debugChallengeLevel,
        challengeLevelName: getLevelName(debugChallengeLevel),
        buttonStatus: debugButtonStatus
      })
      
      const correctRateStatus = getLessonCorrectRateStatus(lessonIndex)
      
      console.log('📊 correctRate状态:', {
        hasCorrectRate: correctRateStatus,
        statusText: correctRateStatus ? '该课程所有知识点都有correctRate值' : '该课程存在没有correctRate值的知识点',
        challengeButtonText: debugChallengeButtonText
      })
      
      console.groupEnd()
    },
    setChapterId,
    lessonData: lessonData,
    chapterId: chapterId,
    chapterName: chapterName,
    options: options,
    // 获取课程相关的计算属性
    get totalLessons() { return totalLessons.value },
    get pointsPerLesson() { return pointsPerLesson.value },
    getLessonKnowledgePoints,
    // correctRate状态检查函数
    checkLessonCorrectRateStatus,
    getLessonCorrectRateStatus,
    // 检查所有课程的correctRate状态
    checkAllLessonsCorrectRateStatus: () => {
      const totalLessonsCount = totalLessons.value
      console.log('🔍 检查所有课程的correctRate状态:')
      
      for (let lessonIndex = 0; lessonIndex < totalLessonsCount; lessonIndex++) {
        const lessonNumber = lessonIndex + 1
        const status = getLessonCorrectRateStatus(lessonIndex)
        console.log(`📊 第${lessonNumber}课: ${status ? '✅有值' : '❌无值'}`)
      }
    }
  }
  
  // console.log('🔧 调试功能已暴露到 window.debugBasicTraining')
  // console.log('📋 可用的调试方法:')
  // console.log('  - printCurrentSelectionInfo(): 打印当前选中的所有信息')
  // console.log('  - findParentInfo(options, nodeId): 查找指定节点的父级信息')
}

// 查找所有具有相同chapterId的节点
const findAllNodesByChapterId = (nodes: any[], targetId: string): any[] => {
  let foundNodes: any[] = []
  
  const collectNodes = (nodeList: any[]) => {
    for (const node of nodeList) {
      const nodeId = node.id || node.chapterId
      if (nodeId === targetId) {
        foundNodes.push(node)
      }
      if (node.children && node.children.length > 0) {
        collectNodes(node.children)
      }
    }
  }
  
  collectNodes(nodes)
  return foundNodes
}

// 查找节点的父级信息
const findParentInfo = (nodes: any[], targetId: string, parentPath: any[] = []): any => {
  for (const node of nodes) {
    const nodeId = node.id || node.chapterId
    
    // 如果找到目标节点，返回父级信息
    if (nodeId === targetId) {
      if (parentPath.length === 0) {
        return null // 顶级节点，无父级
      }
      
      const parentNode = parentPath[parentPath.length - 1]
      return {
        parentId: parentNode.id || parentNode.chapterId,
        parentName: parentNode.chapterName || parentNode.name,
        parentPath: parentPath.map((p: any) => ({
          id: p.id || p.chapterId,
          name: p.chapterName || p.name
        })),
        depth: parentPath.length,
        fullParentPath: parentPath.map((p: any) => p.chapterName || p.name).join(' > ')
      }
    }
    
    // 递归查找子节点
    if (node.children && node.children.length > 0) {
      const result = findParentInfo(node.children, targetId, [...parentPath, node])
      if (result) {
        return result
      }
    }
  }
  
  return null
}

// 处理默认选中逻辑 - 优化版：优先选择非"单元测试"的节点
const handleDefaultSelection = (targetChapterId?: string) => {
  // 处理路由参数的情况
  if (targetChapterId) {
    // 在章节树中查找匹配的节点（已优化为优先选择非"单元测试"的节点）
    const matchedNode = findNodeById(options.value, targetChapterId)
    if (matchedNode) {
      selectOptimalNode(matchedNode)
      return
    }
  }

  // 尝试从缓存恢复选中状态
  const storedInfo = getStoredSelectedInfo()

  if (storedInfo && storedInfo.id && options.value.length > 0) {
    // 有存储信息，验证这个ID在当前options中是否存在
    const nodeExists = findNodeById(options.value, storedInfo.id)
    if (nodeExists) {
      // 智能选择最优节点（优先选择非"单元测试"的节点）
      selectOptimalNode(nodeExists)
    } else {
      // 存储的ID在当前options中不存在，使用默认选择
      selectFirstOption()
    }
  } else if (options.value.length > 0) {
    // 没有存储信息，使用options第一条数据作为默认值
    selectFirstOption()
  }
}

// 选择第一个可用选项 - 优化版：优先选择非"单元测试"的节点
const selectFirstOption = () => {
  const firstOption = findFirstLeafNode(options.value)
  if (firstOption) {
    // 使用智能选择逻辑
    selectOptimalNode(firstOption)
  }
}

// 滚动到选中的节点
const scrollToSelectedNode = () => {
  // 确保DOM已更新
  nextTick(() => {
    // 尝试获取知识树组件实例
    if (knowledgeTreeRef.value) {
      // 如果组件提供了scrollToSelected方法，则调用它
      if (typeof knowledgeTreeRef.value.scrollToSelected === 'function') {
        knowledgeTreeRef.value.scrollToSelected()
      } else {
        
        // 如果组件没有提供方法，则尝试通过DOM查找选中节点
        let selectedNode = document.querySelector('.el-tree-node.is-current')
        
        // 备用查找方式
        if (!selectedNode) {
          selectedNode = document.querySelector('.el-tree-node.is-current1')
        }
        
        if (!selectedNode && chapterId.value) {
          selectedNode = document.querySelector(`[data-key="${chapterId.value}"]`)
        }
        
        if (selectedNode) {
          // 使用scrollIntoView滚动到选中节点
          selectedNode.scrollIntoView({ behavior: 'smooth', block: 'center' })
        } else {
          console.warn('❌ 未找到选中的节点元素')
          // 打印所有可能的选择器以便调试
          const allCurrentNodes = document.querySelectorAll('.el-tree-node.is-current, .el-tree-node.is-current1, .is-current, .is-current1')
        }
      }
    } else {
      console.warn('❌ knowledgeTreeRef.value 为空，无法执行滚动')
    }
  })
}

// 打印当前课程所有知识点levelVos中digestiveMarkers的详细信息
const printCurrentLessonDigestiveMarkers = () => {
  if (lessonData.value.length === 0) {
    return
  }
  
  // 按照动态知识点数量分为一课的逻辑来打印
  const totalLessonsCount = totalLessons.value
  const pointsPerLessonCount = pointsPerLesson.value
  
  
  for (let lessonIndex = 0; lessonIndex < totalLessonsCount; lessonIndex++) {
    const lessonNumber = lessonIndex + 1
    const lessonKnowledgePoints = getLessonKnowledgePoints(lessonIndex)
    
    lessonKnowledgePoints.forEach((knowledge: KnowledgePoint, index: number) => {
      
      // 打印levelVos信息
      if (knowledge.levelVos && Array.isArray(knowledge.levelVos) && knowledge.levelVos.length > 0) {
        knowledge.levelVos.forEach((levelVo: LevelVo, levelIndex: number) => {

        })
      } else {

      }
      
      // 重点检查digestiveMarkers
      if (knowledge.digestiveMarkers !== undefined && knowledge.digestiveMarkers !== null) {
        if (Array.isArray(knowledge.digestiveMarkers)) {
          if (knowledge.digestiveMarkers.length > 0) {
            knowledge.digestiveMarkers.forEach((marker: any, markerIndex: number) => {

            })
          } else {

          }
        } else if (typeof knowledge.digestiveMarkers === 'string') {
          if (knowledge.digestiveMarkers.trim() !== '') {

          } else {

          }
        } else if (typeof knowledge.digestiveMarkers === 'object') {

        } else {

        }
      } else {

      }
    })
    
    // 汇总当前课程的digestiveMarkers情况
    const hasDigestiveMarkers = lessonKnowledgePoints.some((knowledge: KnowledgePoint) => {
      if (knowledge.digestiveMarkers !== undefined && knowledge.digestiveMarkers !== null) {
        if (Array.isArray(knowledge.digestiveMarkers)) {
          return knowledge.digestiveMarkers.length > 0
        } else if (typeof knowledge.digestiveMarkers === 'string') {
          return knowledge.digestiveMarkers.trim() !== ''
        } else {
          return true
        }
      }
      return false
    })

  }
}

// 打印选中左侧知识点下每一个课的数据
const printLessonData = () => {
  
  if (!chapterId.value) {
    console.warn('⚠️ 没有选中章节ID')
    return
  }
  
  if (!options.value || options.value.length === 0) {
    console.warn('⚠️ 没有章节选项数据')
    return
  }
  
  if (lessonData.value.length === 0) {
    console.warn('⚠️ 没有知识点数据')
    return
  }
  
  // 打印基本信息
  const pathResult = chapterPathText.value
  // 按照动态知识点数量分为一课的逻辑来打印
  const totalLessonsCount = totalLessons.value
  const pointsPerLessonCount = pointsPerLesson.value
   
   for (let lessonIndex = 0; lessonIndex < totalLessonsCount; lessonIndex++) {
     const lessonNumber = lessonIndex + 1
     const lessonKnowledgePoints = getLessonKnowledgePoints(lessonIndex)
     const startIndex = lessonIndex * pointsPerLessonCount
     const endIndex = Math.min(startIndex + pointsPerLessonCount, lessonData.value.length)
     
    
    // 打印课程基本信息
    // console.log('📋 课程基本信息123:', {
    //   lessonNumber,
    //   lessonIndex,
    //   startIndex,
    //   endIndex,
    //   knowledgePointCount: lessonKnowledgePoints.length
    // })
    
    // 打印每个知识点的详细信息
    lessonKnowledgePoints.forEach((knowledge: KnowledgePoint, index: number) => {
      // console.group(`📝 知识点${index + 1}: ${knowledge.name}`)
      
      // console.log('🔧 基本信息:', {
      //   id: knowledge.id,
      //   name: knowledge.name,
      //   correctRate: knowledge.correctRate,
      //   studyStatus: knowledge.studyStatus,
      //   status: knowledge.status
      // })
      
      // 详细打印levelVos数组
      if (knowledge.levelVos && Array.isArray(knowledge.levelVos)) {
        // console.log('🎯 等级数据 (levelVos):')
        knowledge.levelVos.forEach((levelItem: LevelVo, levelIndex: number) => {
          const levelName = getLevelName(levelItem.level)
          const statusText = levelItem.status === 1 ? '✅已完成' : 
                            levelItem.status === 2 ? '🔄挑战过' : '❌未完成'
          
          // console.log(`  📊 等级${levelIndex + 1} (${levelName}):`, {
          //   level: levelItem.level,
          //   correctRate: levelItem.correctRate,
          //   status: levelItem.status,
          //   statusText: statusText
          // })
        })
      } else {
        console.log('⚠️ 没有等级数据 (levelVos)')
      }
      
      // 打印digestiveMarkers信息
      if (knowledge.digestiveMarkers !== undefined && knowledge.digestiveMarkers !== null) {
        if (Array.isArray(knowledge.digestiveMarkers)) {
          if (knowledge.digestiveMarkers.length > 0) {
            // console.log('🔴 错题消化数据 (数组):', knowledge.digestiveMarkers)
          } else {
            // console.log('✅ 错题消化数据: 空数组')
          }
        } else if (typeof knowledge.digestiveMarkers === 'string') {
          if (knowledge.digestiveMarkers.trim() !== '') {
            // console.log('🔴 错题消化数据 (字符串):', knowledge.digestiveMarkers)
          } else {
            // console.log('✅ 错题消化数据: 空字符串')
          }
        } else if (typeof knowledge.digestiveMarkers === 'object') {
          // console.log('🔴 错题消化数据 (对象):', knowledge.digestiveMarkers)
        } else {
          // console.log('🔴 错题消化数据 (其他类型):', knowledge.digestiveMarkers)
        }
      } else {
        // console.log('✅ 错题消化数据: null/undefined')
      }
      
      console.groupEnd()
    })
    
    // 打印挑战按钮相关信息
    const lessonForChallenge:any = {
      id: lessonNumber,
      knowledgePoints: lessonKnowledgePoints
    }
    
    const challengeButtonText:any = getChallengeButtonText(lessonForChallenge)
    const challengeLevel = getChallengeLevel(lessonForChallenge)
    const challengeLevelName = getLevelName(challengeLevel)
    const buttonStatus = getChallengeButtonStatus(lessonForChallenge)
    
    // console.log('🎮 挑战按钮信息:', {
    //   challengeButtonText,
    //   challengeLevel,
    //   challengeLevelName,
    //   buttonStatus: {
    //     type: buttonStatus.type,
    //     showButton: buttonStatus.showButton,
    //     hasImage: !!buttonStatus.image
    //   }
    // })
    
    // 分析课程状态
    let allStatus1 = true // 所有第一条数据status都等于1
    let allStatus2WithRate = true // 所有第一条数据status都等于2且correctRate>0
    let hasAnyFirstLevelNotCompleted = false // 是否有第一条数据未过关
    let hasAnySecondLevelAnswered = false // 是否有第二条数据答过题
    let hasDigestiveMarkers = false // 是否有错题消化数据
    
    lessonKnowledgePoints.forEach((knowledge: KnowledgePoint, index: number) => {
      if (knowledge.levelVos && knowledge.levelVos.length > 0) {
        const firstLevel = knowledge.levelVos[0]
        const secondLevel = knowledge.levelVos[1]
        
        if (firstLevel) {
          if (firstLevel.status !== 1) {
            allStatus1 = false
            hasAnyFirstLevelNotCompleted = true
          }
          if (firstLevel.status !== 2 || (firstLevel.correctRate || 0) <= 0) {
            allStatus2WithRate = false
          }
        } else {
          allStatus1 = false
          allStatus2WithRate = false
          hasAnyFirstLevelNotCompleted = true
        }
        
        // 检查第二条数据
        if (secondLevel) {
          if (secondLevel.status > 0 || (secondLevel.correctRate && secondLevel.correctRate > 0)) {
            hasAnySecondLevelAnswered = true
          }
        }
      } else {
        allStatus1 = false
        allStatus2WithRate = false
        hasAnyFirstLevelNotCompleted = true
      }
      
      // 检查是否有错题消化数据
      if (knowledge.digestiveMarkers !== undefined && knowledge.digestiveMarkers !== null) {
        if (Array.isArray(knowledge.digestiveMarkers)) {
          if (knowledge.digestiveMarkers.length > 0) hasDigestiveMarkers = true
        } else if (typeof knowledge.digestiveMarkers === 'string') {
          if (knowledge.digestiveMarkers.trim() !== '') hasDigestiveMarkers = true
        } else {
          hasDigestiveMarkers = true
        }
      }
    })
    
    // 检查当前课程的correctRate状态
    const lessonCorrectRateStatus = getLessonCorrectRateStatus(lessonIndex)
    
    // 打印关键信息
    const currentLessonForChallenge = { id: lessonNumber, knowledgePoints: lessonKnowledgePoints }
    const currentChallengeButtonText = getChallengeButtonText(currentLessonForChallenge)
    
    
    console.groupEnd()
  }
}

const pageData = reactive({
  total: 0,
  current: 1,
  size: 10
})

const pageClick = (val: number) => {
  pageData.current = val
  getChapterReportList()
}
// 单元测试
const dialogVisible = ref(false)
const currentSizeChange = (currentPage: number, pageSize: number) => {
  pageData.current = currentPage
  pageData.size = pageSize
  // getChapterReportList()
}
//下载试卷
const downloadTrestDialogRef = ref()
const dowmloadData = reactive({
    id: '',
    title: ''
})
const handleDownload = ({ id, title }: any) => {
  Object.assign(dowmloadData, {
    id: id,
    title: title
  })
  dialogVisible.value = true
  nextTick(() => {
    downloadTrestDialogRef.value.dialogShow()
  })
}
const testDetail = (data: any) => {
  router.push({
    path: '/ai_percision/knowledge_graph_detail_unit/paper_detailU',
    query: {
      data: dataEncrypt({
        reportId: data.id,
        pageSource: '3'
      }),
    }
  })
}

//获取试卷列表
const getChapterReportList = async() => {
  loading2.value = true
  try {
    const res: any = await getChapterReportListApi({
      chapterId: chapterId.value,
      bookId: subjectObj.value.bookId,
      isSmall: 1,
      type: 1,
      size: pageData.size,
      current: pageData.current
    })
    if(res.code == 200) {
      testList.value = res.data.records || []
      pageData.total = Number(res.data.total)
    }
    loading2.value = false
  } catch (error) {
    testList.value = []
    loading2.value = false
  }
}

//获取章节列表
const getBookChapterList = async(targetChapterId?: string) => {
  if(query.contentType == 'historyTask'){
    subjectObj.value.bookId = query.bookId
  }
  loading.value = true
  loading2.value = true
  try {
    const res: any = await getChapterListApi({
      bookId:subjectObj.value.bookId,
      hierarchy: 3,
      type: query.type,
      chapterIds: query?.resourceIds?query?.chapterId:''
    })
    if(res.code == 200) {
      
      // 确保所有节点都有chapterName属性
      const processChapterData = (chapters) => {
        return chapters.map(chapter => {
          // 确保每个节点都有chapterName属性
          const processedChapter = {
            ...chapter,
            chapterName: chapter.chapterName || chapter.name || chapter.title || '未命名章节'
          }
          
          // 递归处理子节点
          if (processedChapter.children && processedChapter.children.length > 0) {
            processedChapter.children = processChapterData(processedChapter.children)
          }
          
          return processedChapter
        })
      }
      
      // 处理API返回的数据，确保所有节点都有chapterName
      const processedData = processChapterData(res.data || [])
      options.value = processedData
          
      // 只在这里调用一次选中逻辑，避免重复调用
      handleDefaultSelection(targetChapterId)
    }
    loading.value = false
    return res.data || []
  } catch (error) {
    options.value = []
    loading.value = false
    return []
  }
}
const getLast = (data: any) => {
  let id = ""
  if(data.children && data.children.length > 0) {
    // 构建路径
    const path = [data]
    const firstChild = data.children[0]
    if (firstChild) {
      const lastId = getLast(firstChild)
    
    // 如果是第一次调用（没有路径设置），则设置路径
    if (chapterPath.value.length === 0) {
      // 查找完整路径并设置
      findChapterById(options.value, lastId)
    }
    
    return lastId
    } else {
      // 如果children为空数组，返回当前节点id
      return data.id || ""
    }
  } else {
    id = data.id
    
    // 如果是第一次调用（没有路径设置），则设置路径
    if (chapterPath.value.length === 0) {
      chapterPath.value = [data]
    }
    
    return id
  }
}
// 左侧选中Id - 参考 knowledge_graph_detail 的实现
const setChapterId = (data: any, name: string) => {
  // 使用统一的ID字段
  const nodeId = data.id || data.chapterId
  const nodeName = data.chapterName || data.name
  
  // 查找并打印父级信息
  const parentInfo = findParentInfo(options.value, nodeId)
  
  // if (parentInfo) {
  //   console.log('👨‍👩‍👧‍👦 父级节点信息:', {
  //     parentId: parentInfo.parentId,
  //     parentName: parentInfo.parentName,
  //     parentPath: parentInfo.parentPath,
  //     depth: parentInfo.depth
  //   })
  // } else {
  //   console.log('👨‍👩‍👧‍👦 父级节点信息: 当前节点为顶级节点')
  // }

  
  // 打印子级信息
  // if (data.children && data.children.length > 0) {
  //   console.log('👶 子级节点信息:', {
  //     childrenCount: data.children.length,
  //     children: data.children.map((child: any, index: number) => ({
  //       index: index + 1,
  //       id: child.id || child.chapterId,
  //       name: child.chapterName || child.name,
  //       hasSubChildren: !!(child.children && child.children.length > 0),
  //       subChildrenCount: child.children ? child.children.length : 0
  //     }))
  //   })
  // } else {
  //   console.log('👶 子级节点信息: 当前节点为叶子节点，无子节点')
  // }
  
  // 打印完整路径层级信息
  const fullPath = findChapterFullPath(options.value, nodeId)
  // if (fullPath && fullPath.length > 0) {
  //   console.log('🗂️ 完整路径层级信息:', {
  //     totalLevels: fullPath.length,
  //     pathDetails: fullPath.map((item: any, index: number) => ({
  //       level: index + 1,
  //       id: item.id || item.chapterId,
  //       name: item.chapterName || item.name,
  //       isCurrentNode: (item.id || item.chapterId) === nodeId
  //     })),
  //     formattedPath: fullPath.map((item: any) => item.chapterName || item.name).join(' > ')
  //   })
  // }
  //   console.log(parentInfo.parentName,"parentInfo.parentNameparentInfo.parentName")
  //   console.log(chapterName.value,"chapterName.valuchapterName.valuchapterName.valuchapterName.valuchapterName.valu")
    pathName.value = parentInfo?.parentName

  // 打印选中的相关状态信息
  // console.log('📊 选中状态信息:', {
  //   selectedChapterId: chapterId.value,
  //   selectedChapterName: chapterName.value,
  //   currentPageStatus: pageStatus.value,
  //   hasStoredInfo: !!getStoredSelectedInfo(),
  //   routeChapterId: route.query.chapterId
  // })
  
  console.groupEnd()
  
  // 分隔线，便于区分不同的选择操作
  
  // 设置基本状态
  curChapterId.value = data.chapterId
  chapterId.value = nodeId
  chapterName.value = name || nodeName
  pageStatus.value = nodeName !== "单元测试"
  isTargetChapterId.value = nodeName

  // 当用户手动选择章节时，重置标记
  routeHasChapterId.value = false
  isDataLoaded.value = false
  
  // 查找完整路径
  const foundPath = findChapterFullPath(options.value, nodeId)
  
  if (foundPath && foundPath.length > 0) {
    chapterPath.value = foundPath
  } else {
    // 备用方案，至少设置当前节点
    chapterPath.value = [data]
  }

  // 存储选中的信息
  setStoredSelectedInfo(nodeId, name || nodeName)
  
  // 强制触发路径文本更新
  forceUpdatePath.value++
  
  // 根据页面状态决定加载什么数据
  if (!pageStatus.value) {

    getChapterReportList()
  } else {

    getMastery(true) // 强制重新加载
  }
    
  // 更新URL，保持状态同步（不刷新页面）
  router.replace({
    query: {
      ...route.query,
      chapterId: nodeId
    }
  })
  
  // 延迟滚动到选中节点，确保DOM完全渲染
  setTimeout(() => {
    scrollToSelectedNode()
  }, 100)
}

// 防抖标记，避免短时间内重复调用
let getMasteryTimeout: NodeJS.Timeout | null = null
let isGetMasteryRunning = false

// 知识点列表
const getMastery = async(forceReload: boolean = false) => {  
  // 如果数据已经加载过且不是强制重新加载，则跳过
  if (isDataLoaded.value && !forceReload) {

    return
  }
  
  // 如果正在执行中，跳过
  if (isGetMasteryRunning && !forceReload) {

    return
  }
  
  // 清除之前的定时器
  if (getMasteryTimeout) {
    clearTimeout(getMasteryTimeout)
    getMasteryTimeout = null
  }
  
  // 防抖：短时间内的重复调用合并为一次
  if (!forceReload) {
    getMasteryTimeout = setTimeout(async () => {
      await executeGetMastery(forceReload)
    }, 100) // 100ms防抖
    return
  }
  
  // 强制重新加载时立即执行
  await executeGetMastery(forceReload)
}

// 实际执行获取数据的函数
const executeGetMastery = async(forceReload: boolean = false) => {
  if (isGetMasteryRunning && !forceReload) {
    return
  }
  
  isGetMasteryRunning = true
  
  loading2.value = true

  
  try {
    const res: any = await getpointListApi({
      chapterId: chapterId.value,
      type:query.type
    })
    
    if (res.code === 200 && res.data && Array.isArray(res.data)) {

      
      // 🔍 调试信息：详细打印每个知识点的数据
      res.data.forEach((point: any, index: number) => {  
        // 详细检查levelVos数据
        if (point.levelVos && Array.isArray(point.levelVos)) {
          point.levelVos.forEach((levelItem: any, levelIndex: number) => {
            // console.log(levelItem.correctRate,"levelItem",levelIndex)
          })
        } else {
        }
        
        // 详细检查digestiveMarkers数据（支持any类型）
        if (point.digestiveMarkers !== undefined && point.digestiveMarkers !== null) {
          if (Array.isArray(point.digestiveMarkers)) {
            if (point.digestiveMarkers.length > 0) {
              point.digestiveMarkers.forEach((marker: any, markerIndex: number) => {
              })
            }
          }
        }
      })
      
      // 将API返回的知识点数据直接使用，保留原始属性
      lessonData.value = res.data.map((point: any): KnowledgePoint => ({
        ...point,
        // 确保关键属性存在
        id: point.id || '',
        name: point.name || '',
        correctRate: point.correctRate || 0,
        studyStatus: point.studyStatus || 0,
        status: point.status || 5, // 默认为未测试
        levelVos: point.levelVos ? point.levelVos.map((levelItem: any): LevelVo => ({
          level: levelItem.level,
          correctRate: levelItem.correctRate || 0,
          status: levelItem.status || 0, // 确保status字段存在
          digestiveMarkers: levelItem.digestiveMarkers // 🎯 添加digestiveMarkers字段
        })) : [], // 确保levelVos存在并且每项都有status字段
        digestiveMarkers: point.digestiveMarkers // 保持原始digestiveMarkers数据，支持any类型
      }));
      
      // 🎯 调试：打印原始API数据中的digestiveMarkers
      // console.log('🔍 原始API数据中的digestiveMarkers:', res.data.map((point: any) => ({
      //   name: point.name,
      //   levelVos: point.levelVos?.map((lv: any) => ({
      //     level: lv.level,
      //     digestiveMarkers: lv.digestiveMarkers
      //   }))
      // })))
      

      // 如果API返回了掌握度数据，则更新chapterData
      if (res.data[0] && res.data[0].trainCollect) {
      res.data[0].trainCollect.map((item:any) => {
        let num = Number(item.mastery)
        if (item.type == 1) {
          chapterData.percentage1 = num
          chapterData.percentage1i = parseFloat((num).toFixed(2))
        } else if (item.type == 2) {
          chapterData.percentage2 = num
          chapterData.percentage2i = parseFloat((num).toFixed(2))
        } else if (item.type == 3) {
          chapterData.percentage3 = num
          chapterData.percentage3i = parseFloat((num).toFixed(2))
        } else if (item.type == 0) {
          chapterData.percentage0 = num
          chapterData.percentage0i = parseFloat((num).toFixed(2))
        }
      })
    } else {
        // 如果没有掌握度数据，则根据知识点状态计算掌握度
        const totalPoints = lessonData.value.length;
        if (totalPoints > 0) {
          const masteredPoints = lessonData.value.filter(point => point.status === 1).length;
          const masteryPercentage = (masteredPoints / totalPoints) * 100;
          
          chapterData.percentage1 = masteryPercentage;
          chapterData.percentage1i = parseFloat(masteryPercentage.toFixed(2));
        } else {
          resetChapterData();
        }
      }
    } else {
      // 如果API没有返回有效数据，则重置
      lessonData.value = [];
      resetChapterData();
    }
    
    // 标记数据已加载
    isDataLoaded.value = true;
    
    // 数据加载完成后打印课程数据
    setTimeout(() => {
      printLessonData()
      // 专门打印digestiveMarkers信息
      printCurrentLessonDigestiveMarkers()
    }, 100)
    
  } catch (error) {
    console.error('获取知识点数据失败:', error);
    lessonData.value = [];
    resetChapterData();
  } finally {
    loading2.value = false;
    isGetMasteryRunning = false; // 重置执行标记

  }
}

// 重置章节数据
const resetChapterData = () => {
  chapterData.percentage1 = 0;
  chapterData.percentage1i = 0;
  chapterData.percentage2 = 0;
  chapterData.percentage2i = 0;
  chapterData.percentage3 = 0;
  chapterData.percentage3i = 0;
  chapterData.percentage0 = 0;
  chapterData.percentage0i = 0;
}

// 去学习
const toPractice = (val: KnowledgePoint) => {
  router.push({
        name: 'TeachRoomTeachVideo',
    query: {
          id: val.id,
          pointName: val.name,
          source: 'analysis',
          subject: query.subject,
    }
  })
}
// 去练习页面 - 修复函数参数问题
const onQuestions = () => {
  // 使用当前选中的章节信息

      router.push({
      path: '/ai_percision/assign_ment',
      // path: '/ai_percision/entrance_assessment/doing_exercises',
      query: {
        data: dataEncrypt({
          reportId: trainingId.value ,
          pageSource: '1',
          bookId: subjectObj.value.bookId,
          chapterId:chapterId.value,
          level:curLevel.value,
          type:query.type,
          subject:query.subject,
          subjectEn:subjectObj.value.subject,
          curChapterId:curChapterId.value,
          selectedChildIds:selectedChildIds.value,
          learn:'record',
        }),
      }
    })
}


// 获取知识点所属的课程索引
const getCurrentLessonIndex = (knowledge: KnowledgePoint): number => {
  for (let i = 0; i < lessonData.value.length; i++) {
    const lessonKnowledgePoints = getLessonKnowledgePoints(i)
    const found = lessonKnowledgePoints.some((kp: KnowledgePoint) => kp.id === knowledge.id)
    if (found) {
      return i
    }
  }
  return -1
}

// 查看记录
const toRecord = (val: KnowledgePoint) => {  
  // 获取当前课程的所有知识点
  const currentLessonIndex = getCurrentLessonIndex(val)
  if (currentLessonIndex === -1) {
    return
  }
  const currentLessonKnowledgePoints = getLessonKnowledgePoints(currentLessonIndex)
  
  // 检查所有知识点的levelVos数据中的status值
  let hasValidStatus = false
  
  currentLessonKnowledgePoints.forEach((knowledge: KnowledgePoint, index: number) => {
    if (knowledge.levelVos && knowledge.levelVos.length > 0) {
      knowledge.levelVos.forEach((levelVo: any, levelIndex: number) => {     
        // 检查status是否等于1或2
        if (levelVo.status === 1 || levelVo.status === 2) {
          hasValidStatus = true
        }
      })
    } else {
      console.log(`  知识点${index + 1} 没有levelVos数据`)
    }
  })
  
  // 根据检查结果决定是否跳转
  if (hasValidStatus) {
    // 继续执行原来的逻辑
  } else {
    return
  }

  router.push({
      path: '/ai_percision/foundation_report',
    query: {
      data: dataEncrypt({
          bookId: query.bookId,
          pointId:val.id,
          type:query.type,
          source:'training',
          chapterId:chapterId.value,
          learn:'record',
          subject:query.subject
      })
    }
  })
}

const bookVersionName = computed(() => {
  return subjectObj.value.editionName + learnNow.value.gradeName + (subjectObj.value.termName?subjectObj.value.termName:"")
})

// 根据年级计算每课显示的知识点数量
const pointsPerLesson = computed(() => {
  // 如果当前年级大于6，每课显示3个知识点，否则显示2个
  const gradeId = learnUsers[0]?.gradeId || 0
  return gradeId > 6 ? 3 : 2
})

// 根据知识点数量计算总课程数
const totalLessons = computed(() => {
  return Math.ceil(lessonData.value.length / pointsPerLesson.value)
})

// 获取指定课程的知识点
const getLessonKnowledgePoints = (lessonIndex: number): KnowledgePoint[] => {
  if (!lessonData.value || lessonData.value.length === 0) {
    return []
  }
  
  const startIndex = lessonIndex * pointsPerLesson.value
  const endIndex = startIndex + pointsPerLesson.value
  
  // 确保不会超出数组边界
  if (startIndex >= lessonData.value.length) {
    return []
  }
  
  const result = lessonData.value.slice(startIndex, endIndex)
  return result
}
// 获取通关状态
const getCompletionStatus = (knowledge: KnowledgePoint) => {
  return knowledge.isCompleted && knowledge.completionLevel
}

// 获取通关徽章图标
const getCompletionBadge = (knowledge: KnowledgePoint) => {
  if (knowledge.completionLevel === 'perfect' && (knowledge.basicProgress || 0) >= 100 && (knowledge.advancedProgress || 0) >= 100) {
    return new URL(`../../../assets/img/percision/training/组合 <EMAIL>`, import.meta.url).href
  }
  return ''
}

// 判断是否可以显示挑战按钮
const canShowChallengeButton = (lesson: { knowledgePoints: KnowledgePoint[] }) => {
  // 检查课程中是否有知识点达到挑战条件
  return lesson.knowledgePoints.some((knowledge: KnowledgePoint) => 
    (knowledge.correctRate || 0) >= 70 && knowledge.studyStatus !== 1
  )
}

// 获取挑战按钮的状态和显示类型
const getChallengeButtonStatus = (lesson: { knowledgePoints: KnowledgePoint[] }) => {
  if (!lesson.knowledgePoints || lesson.knowledgePoints.length === 0) {
    return {
      type: 'challenge', // 显示挑战按钮
      image: null,
      showButton: true
    }
  }
  
  // 🔍 调试信息：打印当前课程知识点的levelVos和digestiveMarkers数据

  lesson.knowledgePoints.forEach((knowledge, index) => {

    
    // 打印levelVos数据
    if (knowledge.levelVos && knowledge.levelVos.length > 0) {

        knowledge.levelVos.forEach((levelVo, levelIndex) => {

        })
    } else {}
    
    // 打印digestiveMarkers数据（支持any类型）
    if (knowledge.digestiveMarkers !== undefined && knowledge.digestiveMarkers !== null) {
      if (Array.isArray(knowledge.digestiveMarkers)) {
        if (knowledge.digestiveMarkers.length > 0) {
          knowledge.digestiveMarkers.forEach((marker, markerIndex) => {
          })
        }
      }
    }
  })

  // 检查所有知识点的levelVos数据
  let allLevelVosCompleted = true // 所有levelVos数据status都等于1
  let hasWrongQuestions = false // 是否有错题
  
  for (const knowledge of lesson.knowledgePoints) {
    if (!knowledge.levelVos || knowledge.levelVos.length === 0) {
      // 如果没有levelVos数据，说明还没完成所有等级
      allLevelVosCompleted = false
      break
    }
    
    // 检查当前知识点的所有levelVos是否都完成
    for (const levelVo of knowledge.levelVos) {
      if (levelVo.status !== 1) {
        allLevelVosCompleted = false
        break
      }
    }
    
    if (!allLevelVosCompleted) {
      break
    }
    
    // 🎯 检查每个levelVo的digestiveMarkers（在status都等于1的前提下）

    for (const levelVo of knowledge.levelVos) {

      if (levelVo.digestiveMarkers !== undefined && levelVo.digestiveMarkers !== null) {
        // 如果digestiveMarkers有值（非0、非空字符串、非空数组等），认为有错题
        if (Array.isArray(levelVo.digestiveMarkers)) {
          if (levelVo.digestiveMarkers.length > 0) {

            hasWrongQuestions = true
            break // 发现错题就跳出循环
          }
        } else if (typeof levelVo.digestiveMarkers === 'string') {
          if (levelVo.digestiveMarkers.trim() !== '') {

            hasWrongQuestions = true
            break // 发现错题就跳出循环
          }
        } else if (typeof levelVo.digestiveMarkers === 'number') {
          if (levelVo.digestiveMarkers > 0) {

            hasWrongQuestions = true
            break // 发现错题就跳出循环
          } else {

          }
        } else if (levelVo.digestiveMarkers) {
          // 其他类型（对象等），如果有值就认为有错题

          hasWrongQuestions = true
          break // 发现错题就跳出循环
        }
      } else {
        console.log('✅ 无错题 (null/undefined):', knowledge.name, 'level', levelVo.level, 'digestiveMarkers:', levelVo.digestiveMarkers)
      }
    }
    
    // 检查knowledge根级别的digestiveMarkers（作为备用检查）
    if (knowledge.digestiveMarkers) {
      // 如果是数组，检查长度
      if (Array.isArray(knowledge.digestiveMarkers)) {
        if (knowledge.digestiveMarkers.length > 0) {
          hasWrongQuestions = true
        }
      } else {
        // 如果不是数组但有值（字符串、对象等），也认为有错题
        hasWrongQuestions = true
      }
    }
  }
  
  // 根据当前课程所有levelVos的status状态返回对应的显示类型

  if (allLevelVosCompleted) {
    // 当前课程所有levelVos的status都等于1（全部完成）
    if (hasWrongQuestions) {
      // 有digestiveMarkers值，显示错题消化图片ctxh.png
      return {
        type: 'wrong_questions',
        image: new URL('@/assets/img/percision/training/ctxh.png', import.meta.url).href,
        showButton: false
      }
    } else {
      // digestiveMarkers无值，显示完美过关图片wmgg.png
      return {
        type: 'perfect',
        image: new URL('@/assets/img/percision/training/wmgg.png', import.meta.url).href,
        showButton: false
      }
    }
  } else {
    // 还有levelVos的status不等于1，显示挑战按钮
    return {
      type: 'challenge',
      image: null,
      showButton: true
    }
  }
}

// 获取挑战按钮文本（根据训练类型限制）
// 🎯 **优化逻辑**: 最高优先级检查当前课程的correctRate状态
const getChallengeButtonText = (lesson: { knowledgePoints: KnowledgePoint[] }) => {
  const { min, max } = getAllowedLevelRange()
  
  if (!lesson.knowledgePoints || lesson.knowledgePoints.length === 0) {
    return `挑战${getLevelName(min)}`
  }
  
  // 🔥 **最高优先级：首先检查当前课程的correctRate状态**
  // 需要根据知识点找到对应的课程索引来检查状态
  let currentLessonIndex = -1
  const totalLessonsCount = totalLessons.value
  const pointsPerLessonCount = pointsPerLesson.value
  
  // 通过知识点ID找到对应的课程索引
  for (let lessonIndex = 0; lessonIndex < totalLessonsCount; lessonIndex++) {
    const lessonKnowledgePoints = getLessonKnowledgePoints(lessonIndex)
    const hasMatchingKnowledge = lessonKnowledgePoints.some(kp => 
      lesson.knowledgePoints.some(lkp => lkp.id === kp.id)
    )
    if (hasMatchingKnowledge) {
      currentLessonIndex = lessonIndex
      break
    }
  }
  
  // 检查当前课程的correctRate状态
  let lessonHasCorrectRate = true
  if (currentLessonIndex >= 0) {
    lessonHasCorrectRate = checkLessonCorrectRateStatus(currentLessonIndex)
  }
  
  // 获取第一个有效的level值，用于确定挑战段位
  let firstValidLevel: number | null = null
  for (const knowledge of lesson.knowledgePoints) {
    const firstLevelVo = knowledge.levelVos?.[0]
    if (firstLevelVo && firstValidLevel === null && firstLevelVo.level !== null) {
      firstValidLevel = firstLevelVo.level
      break
    }
  }
  
  // 🎯 **最高优先级：如果当前课程的correctRate状态为false（没有值），显示"挑战该段位"**
  if (!lessonHasCorrectRate && firstValidLevel !== null) {
    const challengeLevel = Math.max(min, Math.min(firstValidLevel, max))
    const currentLevelName = getLevelName(challengeLevel)
    return `挑战${currentLevelName}`
  }
  
  // 如果没有有效的level值，使用最小等级
  if (firstValidLevel === null) {
    firstValidLevel = min
  }
  
  // 继续原有的逻辑检查所有correctRate是否为null
  let allCorrectRateNull = true
  
  for (const knowledge of lesson.knowledgePoints) {
    const firstLevelVo = knowledge.levelVos?.[0]
    if (firstLevelVo) {
      // 如果发现任何非null的correctRate，则不满足条件
      if (firstLevelVo.correctRate !== null && firstLevelVo.correctRate !== undefined) {
        allCorrectRateNull = false
      }
  } else {
      allCorrectRateNull = false
    }
  }
  
  // 如果所有correctRate都为null且有有效的level值，直接返回"挑战当前段位"
  if (allCorrectRateNull && firstValidLevel !== null) {
    const challengeLevel = Math.max(min, Math.min(firstValidLevel, max))
    const currentLevelName = getLevelName(challengeLevel)
    return `挑战${currentLevelName}`
  }
  
  // 检查每个知识点的levelVos第一条和第二条数据
  let allFirstLevelCompleted = true // 所有第一条数据status都等于1
  let allFirstLevelStatus2WithRate = true // 所有第一条数据status都等于2且correctRate>0
  let hasAnyFirstLevelNotCompleted = false // 是否有任何一条第一条数据未过关（status !== 1）
  let hasAnySecondLevelAnswered = false // 是否有任何一条第二条数据答过题
  let allFirstLevelNoCorrectRate = true // 所有第一条数据的correctRate都没有值（未答题）
  let firstLevelValue: any = null // 第一条数据的level值，用于确定下一段位
  
  for (const knowledge of lesson.knowledgePoints) {
    if (!knowledge.levelVos || knowledge.levelVos.length === 0) {
      // 如果没有levelVos数据，默认需要挑战青铜
      allFirstLevelCompleted = false
      allFirstLevelStatus2WithRate = false
      hasAnyFirstLevelNotCompleted = true
      allFirstLevelNoCorrectRate = false // 如果没有数据，则认为不是"未答题"状态
      break
    }
    
    const firstLevelVo = knowledge.levelVos?.[0]
    const secondLevelVo = knowledge.levelVos?.[1] // 获取第二条数据（进阶训练六边形）
    
    // 确保 firstLevelVo 存在
    if (!firstLevelVo) {
      allFirstLevelCompleted = false
      allFirstLevelStatus2WithRate = false
      hasAnyFirstLevelNotCompleted = true
      allFirstLevelNoCorrectRate = false // 如果没有firstLevelVo，则认为不是"未答题"状态
      break
    }
    
    // 记录第一条数据的level值（用于确定下一段位）
    if (firstLevelValue === null) {
      firstLevelValue = firstLevelVo.level
    }
    
    // 检查是否所有第一条数据status都等于1
    if (firstLevelVo.status !== 1) {
      allFirstLevelCompleted = false
      hasAnyFirstLevelNotCompleted = true
    }
    
    // 检查是否所有第一条数据status都等于2且correctRate>0
    if (firstLevelVo.status !== 2 || (firstLevelVo.correctRate || 0) <= 0) {
      allFirstLevelStatus2WithRate = false
    }
    
    // 检查第一条数据是否有correctRate值（判断是否未答题）
    if (firstLevelVo.correctRate !== undefined && firstLevelVo.correctRate !== null) {
      allFirstLevelNoCorrectRate = false
    } 
    // 检查第二条数据（进阶训练六边形）- 只要答过题就标记
    if (secondLevelVo) {
      
      // 检查是否答过题（status > 0 或者有correctRate）
      const hasAnswered = secondLevelVo.status > 0 || (secondLevelVo.correctRate && secondLevelVo.correctRate > 0)
      
      if (hasAnswered) {
        hasAnySecondLevelAnswered = true
      }
    } 
  }
  // 详细打印每个知识点的数据
  lesson.knowledgePoints.forEach((knowledge, index) => {
    const firstLevelVo = knowledge.levelVos?.[0]
    if (firstLevelVo) {
    }
  })

  
  // ⚠️ 以下优先级只在最高优先级(correctRate都为null)未满足时才执行
  // console.log(allFirstLevelNoCorrectRate,"allFirstLevelNoCorrectRateallFirstLevelNoCorrectRate")
  // 次优先级1: 如果所有第一条数据的correctRate都没有值（未答题），显示"挑战该段位"
  if (allFirstLevelNoCorrectRate && firstLevelValue !== null) {
    const challengeLevel = Math.max(min, Math.min(firstLevelValue, max))
    const currentLevelName = getLevelName(challengeLevel)
    return `挑战${currentLevelName}`
  }
  
  // 次优先级2: 如果第二条数据（levelVos[1]）有任何一条答过题，显示"再次挑战该段位"
  if (hasAnySecondLevelAnswered) {
    // 第二条数据（进阶训练六边形）对应当前训练类型的最高等级
    const advancedLevel = max // 基础训练中为白银(2)，提升训练中为钻石(4)
    const currentLevelName = getLevelName(advancedLevel)
    return `再次挑战${currentLevelName}`
  }
  
  // 次优先级3: 如果所有知识点的第一条levelVos数据status都等于1，显示挑战下一段位
  if (allFirstLevelCompleted && firstLevelValue !== null) {
    // 确保下一段位不超过训练类型的最大等级
    const nextLevel = Math.min(firstLevelValue + 1, max)
    const nextLevelName = getLevelName(nextLevel)
    return `挑战${nextLevelName}`
  }
  
  // 次优先级4: 如果有任何一条第一条数据未过关（status !== 1），显示"再次挑战该段位"
  if (hasAnyFirstLevelNotCompleted && firstLevelValue !== null) {
    // console.log(hasAnyFirstLevelNotCompleted,firstLevelValue,'hasAnyFirstLevelNotCompletedhasAnyFirstLevelNotCompleted')
    // 详细列出哪些知识点的第一条数据未过关
    lesson.knowledgePoints.forEach((knowledge, index) => {
      const firstLevelVo = knowledge.levelVos?.[0]
      if (firstLevelVo && firstLevelVo.status !== 1) {
        // console.log(`    知识点${index + 1} (${knowledge.name}): status=${firstLevelVo.status} (!== 1)`)
      }
    })
    
    // 确保挑战等级在允许范围内
    const challengeLevel = Math.max(min, Math.min(firstLevelValue, max))
    const currentLevelName = getLevelName(challengeLevel)
    // console.log(`  挑战等级: ${challengeLevel} (${currentLevelName})`)
    // console.log(`🎯 优先级3返回: 再次挑战${currentLevelName}`)
    return `再次挑战${currentLevelName}`
  }
  
  // 次优先级5: 如果所有知识点的第一条levelVos数据status都等于2且correctRate>0，显示"再次挑战当前段位"
  if (allFirstLevelStatus2WithRate && firstLevelValue !== null) {
    // 确保挑战等级在允许范围内
    const challengeLevel = Math.max(min, Math.min(firstLevelValue, max))
    const currentLevelName = getLevelName(challengeLevel)
    return `再次挑战${currentLevelName}`
  }
  
  // 次优先级6: 根据第一条数据的level显示对应段位挑战
  if (firstLevelValue !== null) {
    // 确保挑战等级在允许范围内
    const challengeLevel = Math.max(min, Math.min(firstLevelValue, max))
    const currentLevelName = getLevelName(challengeLevel)
    return `挑战${currentLevelName}`
  }
  
  return `挑战${getLevelName(min)}`
}

// 获取星级图标
const getStarIcon = (filled: boolean) => {
  return filled
    ? new URL(`../../../assets/img/percision/star1.png`, import.meta.url).href
    : new URL(`../../../assets/img/percision/star0.png`, import.meta.url).href
}

// 获取操作按钮图标
const getActionIcon = (percentage: number) => {
  if (percentage > 90) {
    return new URL(`../../../assets/img/percision/strong.png`, import.meta.url).href
  }
  return new URL(`../../../assets/img/percision/pen.png`, import.meta.url).href
}

// 获取成就徽章图标
const getMedalIcon = (type: string) => {
  const medalMap = {
    'basic': 'medal_2.png',
    'advanced': 'medal_3.png',
    'comprehensive': 'medal_1.png'
  }
  return new URL(`../../../assets/img/percision/training/${medalMap[type] || 'medal_1.png'}`, import.meta.url).href
}
// 新增辅助函数
const getHexagonClass = (percentage: number) => {
  if (percentage >= 90) return 'excellent'
  if (percentage >= 70) return 'good'
  if (percentage >= 50) return 'average'
  return 'poor'
}

// 获取掌握度状态类名
const getMasteryClass = (percentage: number) => {
  if (percentage >= 90) return 'excellent'
  if (percentage >= 70) return 'good'
  if (percentage >= 50) return 'average'
  return 'poor'
}

// 获取掌握度文本
const getMasteryText = (percentage: number) => {
  if (percentage >= 90) return '优秀掌握'
  if (percentage >= 70) return '良好掌握'
  if (percentage >= 50) return '一般掌握'
  return '需要加强'
}

// 获取小奖牌图标
const getSmallMedalIcon = (type: number) => {
  const medalMap = {
    1: 'medal_small_1.png',
    2: 'medal_small_2.png',
    3: 'medal_small_3.png',
    4: 'medal_small_4.png',
    5: 'medal_small_5.png',
    6: 'medal_small_6.png',
    7: 'medal_small_7.png',
    8: 'medal_small_8.png'
  }
  return new URL(`../../../assets/img/percision/training/${medalMap[type] || 'medal_small_1.png'}`, import.meta.url).href
}

// 获取完美掌握奖牌
const getPerfectMedalIcon = () => {
  return new URL(`../../../assets/img/percision/training/medal_1.png`, import.meta.url).href
}

// 判断是否显示挑战徽章
const shouldShowChallengeBadge = (percentage1: number, percentage2: number) => {
  return percentage1 >= 70 && percentage2 < 70
}

// 获取挑战徽章图标
const getChallengeBadgeIcon = () => {
  return new URL(`../../../assets/img/percision/training/组合 <EMAIL>`, import.meta.url).href
}

// 获取再次挑战徽章图标
const getRetryBadgeIcon = () => {
  return new URL(`../../../assets/img/percision/training/组合 <EMAIL>`, import.meta.url).href
}

// 获取完美掌握徽章图标
const getPerfectBadgeIcon = () => {
  return new URL(`../../../assets/img/percision/training/组合 <EMAIL>`, import.meta.url).href
}

const onMark = () => {
  router.push({
    path: '/ai_percision/knowledge_hotspot',
    query:{
      bookId:subjectObj.value.bookId,
      subject:query.subject,
      chapterId:chapterId.value,
      type:query.type
    }
  })
}
// 获取六边形背景类名
const getHexagonBgClass = (percentage: number, type: string) => {
  let baseClass = type
  if (percentage >= 90) {
    baseClass += ' excellent'
  } else if (percentage >= 70) {
    baseClass += ' good'
  } else if (percentage >= 50) {
    baseClass += ' average'
  } else {
    baseClass += ' poor'
  }
  return baseClass
}

// 获取知识点状态类名
const getKnowledgeStatusClass = (knowledge: KnowledgePoint) => {
  // 根据status值确定样式类名
  switch (knowledge.status) {
    case 1: return 'status-mastered'; // 已掌握
    case 2: return 'status-learning'; // 学习中
    case 3: return 'status-failed';   // 未掌握
    case 4: return 'status-testing';  // 测试中
    case 5: return 'status-untested'; // 未测试
    default: return 'status-unknown'; // 未知状态
  }
}

// 获取知识点状态文本
const getKnowledgeStatusText = (knowledge: KnowledgePoint) => {
  // 根据status值确定显示文本
  switch (knowledge.status) {
    case 1: return '已掌握';
    case 2: return '学习中';
    case 3: return '未掌握';
    case 4: return '测试中';
    case 5: return '未测试';
    default: return '未知';
  }
}

const onModify = () =>{
  console.log(learnUsers[0]?.learnId,"learnUserslearnUserslearnUsers")
    router.push({
    path: '/user/user_add',
    query: {
      learnId: learnUsers[0]?.learnId || '',
      pageType:'edit'
    }
  })
}

// 知识点详情弹窗相关
const knowledgeDetailVisible = ref(false)
const selectedKnowledge = ref<KnowledgePoint | null>(null)

const showKnowledgeDetail = (knowledge: KnowledgePoint) => {
  selectedKnowledge.value = knowledge
  knowledgeDetailVisible.value = true
}

const handleDetailClose = () => {
  knowledgeDetailVisible.value = false
  selectedKnowledge.value = null
}

// 获取知识点难度文本
const getDifficultyText = (point: KnowledgePoint) => {
  // 根据知识点的难度值返回对应的文本
  // 这里假设难度值在1-3之间，1为容易，2为中等，3为困难
  const difficultyLevel = point.difficulty || 1;
  
  switch (difficultyLevel) {
    case 1:
      return '容易';
    case 2:
      return '中等';
    case 3:
      return '困难';
    default:
      return '容易';
  }
}

// 格式化正确率显示：如果小数点后两位是00则显示整数
const formatCorrectRate = (rate: number): string => {
  if (rate === null || rate === undefined) {
    return '0';
  }
  
  // 转换为数字并保留两位小数
  const numRate = Number(rate);
  const fixedRate = numRate.toFixed(2);
  
  // 如果小数点后两位都是0，则返回整数
  if (fixedRate.endsWith('.00')) {
    return Math.floor(numRate).toString();
  }
  
  // 否则返回去掉末尾0的小数
  return parseFloat(fixedRate).toString();
}

// 根据训练类型获取允许的等级范围
const getAllowedLevelRange = () => {
  const trainingType = query.type
  if (trainingType === '1') {
    // 基础训练：青铜到白银 (1-2)
    return { min: 1, max: 2 }
  } else if (trainingType === '2') {
    // 提升训练：黄金到钻石 (3-4)
    return { min: 3, max: 4 }
  }
  // 默认基础训练
  return { min: 1, max: 2 }
}

// 根据训练类型过滤等级数据（保留level为null的项目显示灰色图片）
const getFilteredLevelVos = (levelVos: LevelVo[]) => {
  if (!levelVos || levelVos.length === 0) return []
  
  const { min, max } = getAllowedLevelRange()
  
  return levelVos.filter(levelVo => {
    // 如果level为null，保留该项目（显示灰色图片）
    if (levelVo.level === null || levelVo.level === undefined) {
      return true
    }
    // 如果level不为null，检查是否在允许的范围内
    return levelVo.level >= min && levelVo.level <= max
  })
}

// 获取等级名称
const getLevelName = (level: number | null) => {
  if (level === null || level === undefined) {
    return '未评级';
  }
  
  switch (level) {
    case 1:
      return '青铜';
    case 2:
      return '白银';
    case 3:
      return '黄金';
    case 4:
      return '钻石';
    default:
      return '未评级';
  }
}

// 获取下一个段位的名称（根据训练类型限制）
const getNextLevelName = (currentLevel: number | null) => {
  const { min, max } = getAllowedLevelRange()
  
  if (currentLevel === null || currentLevel === undefined) {
    return getLevelName(min);
  }
  
  // 如果当前等级已经是最高等级，返回当前等级名称
  if (currentLevel >= max) {
    return getLevelName(currentLevel);
  }
  
  // 返回下一个等级，但不超过最大等级
  const nextLevel = Math.min(currentLevel + 1, max);
  return getLevelName(nextLevel);
}

// 获取当前课程应该挑战的段位等级（用于API调用，根据训练类型限制）
const getChallengeLevel = (lesson: { knowledgePoints: KnowledgePoint[] }): number => {
  const { min, max } = getAllowedLevelRange()
  
  if (!lesson.knowledgePoints || lesson.knowledgePoints.length === 0) {
    return min; // 默认返回最小等级
  }
  
  // 检查每个知识点的levelVos第一条数据，确定挑战等级
  let allFirstLevelCompleted = true // 所有第一条数据status都等于1
  let allFirstLevelStatus2WithRate = true // 所有第一条数据status都等于2且correctRate>0
  let firstLevelValue: number | null = null // 第一条数据的level值
  
  for (const knowledge of lesson.knowledgePoints) {
    if (!knowledge.levelVos || knowledge.levelVos.length === 0) {
      // 如果没有levelVos数据，默认挑战青铜
      allFirstLevelCompleted = false
      allFirstLevelStatus2WithRate = false
      break
    }
    
    const firstLevelVo = knowledge.levelVos?.[0]
    if (!firstLevelVo) {
      allFirstLevelCompleted = false
      allFirstLevelStatus2WithRate = false
      break
    }
    
    // 记录第一条数据的level值
    if (firstLevelValue === null) {
      firstLevelValue = firstLevelVo.level
    }
    
    // 检查是否所有第一条数据status都等于1
    if (firstLevelVo.status !== 1) {
      allFirstLevelCompleted = false
    }
    
    // 检查是否所有第一条数据status都等于2且correctRate>0
    if (firstLevelVo.status !== 2 || (firstLevelVo.correctRate || 0) <= 0) {
      allFirstLevelStatus2WithRate = false
    }
  }
  
  // 优先级1: 如果所有知识点的第一条levelVos数据status都等于1，挑战下一段位
  if (allFirstLevelCompleted && firstLevelValue !== null) {
    const nextLevel = Math.min(firstLevelValue + 1, max) // 限制在允许的最高等级内
    return nextLevel
  }
  
  // 优先级2: 如果所有知识点的第一条levelVos数据status都等于2且correctRate>0，重新挑战当前段位
  if (allFirstLevelStatus2WithRate && firstLevelValue !== null) {
    // 确保挑战等级在允许范围内
    const challengeLevel = Math.max(min, Math.min(firstLevelValue, max))
    return challengeLevel
  }
  
  // 优先级3: 根据第一条数据的level挑战对应段位
  if (firstLevelValue !== null) {
    // 确保挑战等级在允许范围内
    const challengeLevel = Math.max(min, Math.min(firstLevelValue, max))
    return challengeLevel
  }
  
  // 默认挑战最小等级

  return min
}

// 获取等级图片（根据等级和正确率显示不同图片）
const getLevelImage = (level: number | null, correctRate?: number) => {
  if (level === null || level === undefined) {
    return new URL('@/assets/img/percision/training/huise.png', import.meta.url).href;
  }
  
  // 判断是否达到90%正确率阈值
  const isHighAccuracy = correctRate !== undefined && correctRate >= 90;
  
  // console.log(`🎨 getLevelImage - Level: ${level}, CorrectRate: ${correctRate}%, 高准确率: ${isHighAccuracy}`)
  
  switch (level) {
    case 1: // 青铜
      return isHighAccuracy 
        ? new URL('@/assets/img/percision/training/qingt.png', import.meta.url).href
        : new URL('@/assets/img/percision/training/qingtonghuise.png', import.meta.url).href;
    case 2: // 白银
      return isHighAccuracy
        ? new URL('@/assets/img/percision/training/baiyin.png', import.meta.url).href
        : new URL('@/assets/img/percision/training/baiyinhuangse.png', import.meta.url).href;
    case 3: // 黄金
      return isHighAccuracy
        ? new URL('@/assets/img/percision/training/huangjin.png', import.meta.url).href
        : new URL('@/assets/img/percision/training/huangjinhuise.png', import.meta.url).href;
    case 4: // 钻石
      return isHighAccuracy
        ? new URL('@/assets/img/percision/training/zuanshi.png', import.meta.url).href
        : new URL('@/assets/img/percision/training/zuanshihuise.png', import.meta.url).href;
    default:
      return new URL('@/assets/img/percision/training/huise.png', import.meta.url).href;
  }
}

// 获取等级图片
const getLeveltcImage = (level: number | null) => {
  if (level === null || level === undefined) {
    return new URL('@/assets/img/percision/training/huise.png', import.meta.url).href;
  }
  switch (level) {
    case 1:
      return new URL('@/assets/img/percision/training/medal_1.png', import.meta.url).href;
    case 2:
      return new URL('@/assets/img/percision/training/medal_2.png', import.meta.url).href;
    case 3:
      return new URL('@/assets/img/percision/training/medal_3.png', import.meta.url).href;
    case 4:
      return new URL('@/assets/img/percision/training/medal_5.png', import.meta.url).href;
    default:
      return new URL('@/assets/img/percision/training/medal_1.png', import.meta.url).href;
  }
}

// 获取挑战弹窗背景图片
const getChallengeBackgroundImage = (level: number | null) => {
  
  if (level === null || level === undefined) {
    const defaultBg = new URL('@/assets/img/percision/training/ytzk.png', import.meta.url).href;
    return defaultBg;
  }
  
  let backgroundImage = '';
  switch (level) {
    case 1:
      backgroundImage = new URL('@/assets/img/percision/training/ytzk.png', import.meta.url).href; // 挑战青铜
      break;
    case 2:
      backgroundImage = new URL('@/assets/img/percision/training/tzbytc.png', import.meta.url).href; // 挑战白银
      break;
    case 3:
      backgroundImage = new URL('@/assets/img/percision/training/tzhjtc.png', import.meta.url).href; // 挑战黄金
      break;
    case 4:
      backgroundImage = new URL('@/assets/img/percision/training/tczstc.png', import.meta.url).href; // 挑战钻石
      break;
    default:
      backgroundImage = new URL('@/assets/img/percision/training/ytzk.png', import.meta.url).href; // 默认青铜
      break;
  }
  
  return backgroundImage;
}

// 获取挑战弹窗中当前挑战段位的图片
const getCurrentChallengeLevelImage = () => {
  if (!selectedLessonForChallenge.value) {
    return new URL('@/assets/img/percision/training/qingtong.png', import.meta.url).href; // 默认青铜
  }
  
  const challengeLevel = getChallengeLevel(selectedLessonForChallenge.value);
  // 挑战弹窗显示彩色图片（不根据正确率判断），传入90以上确保显示彩色
  return getLevelImage(challengeLevel, 90);
}

// 计算属性：获取当前挑战的段位信息（根据训练类型）
const currentChallengeInfo = computed(() => {
  const { min } = getAllowedLevelRange()
  
  if (!selectedLessonForChallenge.value) {

    const result = {
      level: min,
      levelName: getLevelName(min),
      levelImage: getLeveltcImage(min),
      backgroundImage: getChallengeBackgroundImage(min)
    }

    return result
  }
  
  const challengeLevel = getChallengeLevel(selectedLessonForChallenge.value);
  const levelName = getLevelName(challengeLevel);
  const levelImage = getLeveltcImage(challengeLevel);
  const backgroundImage = getChallengeBackgroundImage(challengeLevel);
  
  const result = {
    level: challengeLevel,
    levelName,
    levelImage,
    backgroundImage
  }
  

  return result
})

// 计算总题量
const getTotalQuestions = () => {
  if (!selectedLessonForChallenge.value || !selectedLessonForChallenge.value.knowledgePoints) {
    return 6; // 默认值
  }
  
  // 计算所有知识点的题量总和
  return selectedLessonForChallenge.value.knowledgePoints.reduce((total: number, point: KnowledgePoint) => {
    return total + (point.total || 3); // 如果没有题量数据，默认为3题
  }, 0);
}

// 计算预估完成时间（分钟）
const getEstimatedTime = () => {
  const totalQuestions = getTotalQuestions();
  // 假设每题平均需要1分钟
  return Math.max(Math.ceil(totalQuestions), 6); // 至少6分钟
}

// Watch for changes in options data - 备用的选中逻辑（仅在必要时调用）
watch(() => options.value, (newOptions, oldOptions) => {
  // 只有在以下条件都满足时才执行：
  // 1. 新的options有数据
  // 2. 旧的options没有数据（首次加载）或数据不同
  // 3. 当前没有选中章节ID
  // 4. 不是从路由获取的章节ID
  // 5. 数据还没有加载过
  if (
    newOptions && 
    newOptions.length > 0 && 
    (!oldOptions || oldOptions.length === 0) &&
    !chapterId.value && 
    !routeHasChapterId.value &&
    !isDataLoaded.value
  ) {

    // 延迟执行，确保DOM完全渲染和树组件初始化完成
    setTimeout(() => {
      if (!chapterId.value && !isDataLoaded.value) { // 再次检查，避免重复设置

        handleDefaultSelection()
      }
    }, 500) // 增加延迟时间，确保树组件完全初始化
  }
}, { immediate: false }) // 改为非立即执行，避免初始化时的重复调用

// 组件卸载时清理
onUnmounted(() => {
  console.log("🔄 页面即将卸载，执行清理工作...")
  
  // 清理定时器
  if (getMasteryTimeout) {
    clearTimeout(getMasteryTimeout)
    getMasteryTimeout = null
  }
  isGetMasteryRunning = false
  
  // 清理页面来源标记（可选，根据需要保留或清理）
  // sessionStorage.removeItem('navigation_source')
  // 注意：basic_training_source 在使用后已被清理
  
  console.log("✅ 页面清理完成!")
})

</script>

<style scoped lang="scss">
.training-page {
  display: flex;
  min-height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height));
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.left-sidebar {
  position: relative;
  background: white;
  border-radius: 0 20px 20px 0;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);

  .sidebar-title {
    position: absolute;
    left: -14px;
    top: 10px;
    width: 179px;
    height: 46px;
    line-height: 46px;
    text-align: center;
    background: linear-gradient(135deg, #00c9a3 0%, #00a085 100%);
    color: white;
    font-size: 20px;
    font-weight: 700;
    border-radius: 0 10px 10px 0;
    z-index: 10;

    .title-decoration {
      position: absolute;
      bottom: -14px;
      left: 0;
      width: 0;
      height: 0;
      border-left: 7px solid transparent;
      border-right: 7px solid #00886e;
      border-top: 7px solid #00886e;
      border-bottom: 7px solid transparent;
    }
  }
}
.test-box {
  height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height) - 11.875rem);
  width: 54.5rem;
  margin-left: 1.5625rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: .625rem;
  position: relative;
  .test-wrap{
      width: calc(100% - .5rem);
      .hui-line{
        width: calc(100% - 1.75rem);
        border-bottom: .0625rem dashed #EAEAEA;
        margin: 0 0 0 .875rem;
        float: left;
      }
    }
    &-item {
      width: 100%;
      height: 6.875rem;
      display: flex;
      padding: 1.25rem 1.25rem 1.25rem 1.25rem;
      box-sizing: border-box;
      &:hover {
        background: #effdfb;
      }
      &-img {
        width: 3.1875rem;
        height: 100%;
        font-size: .75rem;
        background-image: url(@/assets/img/percision/test-img.png);
        background-size: 100%;
        background-repeat: no-repeat;
        text-align: center;
        span {
          display: inline-block;
          margin-top: 1.85rem;
          position: relative;
          left: .125rem;
        }
      }
    &-info {
      margin-left: 1rem;
      width: 40rem;
      margin-right: 1rem;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      &-title {
        color: #2a2b2a;
        font-size: 1rem;
        font-weight: 400;
      }
      &-data {
        div {
          height: 1.75rem;
          border-radius: .875rem;
          background: #fef8e9;
          color: #ef9d19;
          display: inline-block;
          box-sizing: border-box;
          padding: .375rem .75rem;
          font-size: .75rem;
          margin-right: .625rem;
        }
      }
    }
    &-btn {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      &-it {
        width: 5.25rem;
        height: 1.875rem;
        line-height: 1.875rem;
        border-radius: .25rem;
        font-size: .875rem;
        text-align: center;
        cursor: pointer;
        img {
          width: .875rem;
          height: .875rem;
        }
      }
    }
  }
  .learn-img {
    position: fixed;
    bottom: 1.875rem;
    left: 55%;
    width: 14.0625rem;
    height: 3.125rem;
  }
}
.btn {
  color: #ffffff;
  background: #00c9a3;
}
.grey-btn {
  background: #f5f5f5;
  color: #999999;
}
.red-text {
  color: #dd2a2a;
}
.blue-text {
  color: #009c7f;
}
.icon-sty {
  width: 1rem;
}
.main-content {
  flex: 1;
  margin-left: 10px;
  width: calc(100% - 378px);
  height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height));
  border-radius: 20px;
  overflow-x: hidden;
  overflow-y: auto;
  box-sizing: border-box;
  background-color: white;
  .content-head{
    position: relative;
    background: url('@/assets/img/percision/training/head-bg.png')center center no-repeat;
    background-size: cover;
    height: 121px;
    padding: 0 20px;
    .head-body{
      position: relative;
      padding: 20px 0 0 0px;
      font-size: 16px;
      font-weight: 600;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      .textbook{
        width: 24px;
        height: 30px;
        margin-right: 6px;
      }
      .head-title{
        margin-right: 10px;
      }
      .head-switch{
        display: flex;
        justify-content: center;
        align-items: center;
        width: 92px;
        height: 27px;
        font-size: 14px;
        font-weight: 600;
        color: #fff;
        background: #00957f;
        border-radius: 14px;
        cursor: pointer;
      }
    }
    .catalogue{
      background: #fff;
      border-radius: 22px;
      margin-top: 20px;
      line-height: 33px;
      padding-left: 16px;
      display: flex;
      span{
        color: rgba(102, 102, 102, 1);
        font-size: 16px;
      }
      img{
        width: 14px;
        height: 9px;
        margin: 12px 12px 12px auto;
      }
    }
    .superficiality{
      position: absolute;
      right: 0;
      top:0;
      width: 120px;
      height: 39px;
      cursor: pointer;
    }
    .head-select{
      position: relative;
      margin: 22px 0 0 20px;
      width: 882px;
      height: 33px;
    }
    .head-select :deep(.el-select__wrapper) {
        border-radius: 15px;
    }
  }
  .tip-content{
    padding: 0 16px;
    display: flex;
    justify-content: flex-start;
    height: 44px;
    align-items: center;
    .tip-avatar{
      width: 28px;
      height: 28px;
      margin-right: 10px;
    }
    .tip-text{
      color: #3294DB;
      font-size: 12px;
    }
  }
  .content-tip{
    position: relative;
    margin: 10px auto;
    width: 882px;
    height: 44px;
    border-radius: 4px;
    background: rgba(236, 247, 255, 1);
    .tip-bg{
      position: absolute;
      top: 0;
      left: 0;
      width: 882px;
      height: 44px;
    }
  }
}

.knowledge-training-list {
  margin-top: 10px;
  padding:0 20px;
  .lesson-section {
    margin-bottom: 30px;
    
    background: #FAFBFD;
    // border-radius: 8px;
    // overflow: hidden;

    .lesson-tag {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 60px;
      height: 24px;
      text-align: center;
      border: 1px solid #5A85EC;
      background: rgba(238, 243, 253, 1);
      color: rgba(90, 133, 236, 1);
      font-size: 12px;
      font-weight: 400;
      // border-radius: 0 0 8px 0;
    }
  }
}

.knowledge-info {
 
  .knowledge-box {
  display: flex;
    flex-direction: column;
    gap: 10px;
  }
}

.knowledge-item {
  display: flex;
  align-items: center;
  margin: 0 20px;
  padding: 20px 0;
  position: relative;
  transition: all 0.2s ease;
  border-bottom: 1px dashed rgba(234, 234, 234, 1);
  
  &:last-child {
    border-bottom: none;
  }

  .knowledge-name {
    width: 280px;
    padding-right: 20px;
    border-right: 1px solid #EAEAEA;
    margin-right: 16px;

    .knowledge-title {
      font-size: 16px;
      font-weight: 400;
      color: #333;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2; /* 限制为两行 */
      line-clamp: 2; /* 标准属性，用于兼容性 */
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      padding-right: 30px; /* 为状态标签留出空间 */
    }
  }

  .progress-area {
    display: flex;
    align-items: center;
    margin-right: 20px;

    .hexagon-group {
      display: flex;
      gap: 20px;
      align-items: center;
    }
  }

  .action-area {
    display: flex;
    gap: 8px;
  }
}

.hexagon-wrapper {
  display: flex;
  flex-direction: column;
  align-items: center;

  .hexagon-bg {
    width: 50px;
    height: 50px;
    position: relative;
    background: url('@/assets/img/percision/training/medal_small_1.png')center no-repeat;
    background-size: cover;
    &.basic {
      // 基础训练 - 绿色系
      &.excellent {
        background: url('@/assets/img/percision/training/medal_small_2.png')center no-repeat;
      }
      &.good {
        background: url('@/assets/img/percision/training/medal_small_3.png')center no-repeat;

      }
      &.average {
        background: url('@/assets/img/percision/training/medal_small_4.png')center no-repeat;

      }
      &.poor {
        background: url('@/assets/img/percision/training/medal_small_5.png')center no-repeat;

      }
    }

    &.advanced {
      // 进阶训练 - 蓝色系
      &.excellent {
        background: url('@/assets/img/percision/training/medal_small_6.png')center no-repeat;

      }
      &.good {
        background: url('@/assets/img/percision/training/medal_small_7.png')center no-repeat;

      }
      &.average {
        background: url('@/assets/img/percision/training/medal_small_8.png')center no-repeat;
      }
      &.poor {
        background: url('@/assets/img/percision/training/medal_small_6.png')center no-repeat;

      }
    }

    .hexagon-content {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      color: white;

      .percentage-text {
        font-size: 10px;
        font-weight: 600;
        margin-bottom: 2px;
        font-size: 12px;
        padding: 3px;
        position: absolute;
        bottom: -50px;
        color: rgba(0, 156, 127, 1);
        left: 50%;
        transform: translateX(-50%);
        background: rgba(229, 249, 246, 1);
      }

      .medal-crown {
        position: absolute;
        top: -12px;
        left: 50%;
        transform: translateX(-50%);
        width: 16px;
        height: 16px;
      }
    }
  }
}

// 等级显示样式
.level-wrapper {
  display: flex;
  align-items: center;
  gap: 10px;

  .level-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;

    .level-image {
      width: 60px;
      height: 50px;
      object-fit: contain;
    }

    .level-info {
      text-align: center;

      .level-name {
        font-size: 10px;
        color: #666;
        font-weight: 500;
        margin-bottom: 2px;
      }

      .level-rate {
        font-size: 12px;
        font-weight: 600;
        color: #00c9a3;
        padding: 2px 6px;
        background: rgba(229, 249, 246, 1);
        border-radius: 8px;
        
        &.low-rate {
          color: rgba(221, 42, 42, 1);
          background: rgba(251, 233, 233, 1);
        }
      }
    }
  }
}

// 按钮样式
.action-btn {
  display: flex;
  align-items: center;
  height: 19px;
  font-size: 14px;
  align-items: center;
  justify-content: space-between;
  color: #999999;
  cursor: pointer;
  .action-icon {
    width: 16px;
    height: 16px;
    margin-right: 6px;
    &.study-icon {
      background: url('@/assets/img/percision/pen.png') no-repeat center;
      background-size: contain;
      margin-right: 6px;
    }

    &.practice-icon {
      background: url('@/assets/img/percision/strong.png') no-repeat center;
      background-size: contain;
    }
  }

  &.study {
    background: #f8f9fa;
    padding-left: 6px;
    // border: 1px solid #e9ecef;
    text-decoration: underline;
    &:hover {
      // background: #e9ecef;
      color: #495057;
    }
  }

  &.practice {
    background: #f8f9fa;
    padding-left: 6px;
    // border: 1px solid #e9ecef;
    text-decoration: underline;
    &:hover {
      // background: #e9ecef;
      color: #495057;
    }
  }
}

// 成就徽章样式
.achievement-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 500;
  color: white;
  z-index: 2;

  &.challenge {
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  }

  &.retry {
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
  }

  &.perfect {
    background: transparent;
    padding: 0;

    .perfect-icon {
      width: 60px;
      height: auto;
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    }
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.lesson-section {
  animation: fadeInUp 0.5s ease-out;

  &:nth-child(1) { animation-delay: 0.1s; }
  &:nth-child(2) { animation-delay: 0.2s; }
  &:nth-child(3) { animation-delay: 0.3s; }
}

.knowledge-status {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 500;
  color: white;
}

.status-mastered {
  background: linear-gradient(166.7deg, #08d8b8 0%, #00b392 100%);
}

.status-learning {
  background: linear-gradient(150.8deg, #5a85ec 0%, #3a65cc 100%);
}

.status-failed {
  background: linear-gradient(150.8deg, #f07f4c 0%, #c95656 100%);
}

.status-testing {
  background: linear-gradient(150.8deg, #f6d22b 0%, #f29500 100%);
}

.status-untested {
  background: #bbbbbb;
}

.status-unknown {
  background: #999999;
}

.knowledge-detail-content {
  padding: 20px;
  max-height: 60vh;
  overflow-y: auto;
  line-height: 1.6;
  font-size: 14px;
  color: #333;
  
  :deep(table) {
    border-collapse: collapse;
    width: 100%;
    margin: 10px 0;
    
    td, th {
      border: 1px solid #ddd;
      padding: 8px;
      text-align: center;
    }
    
    tr:nth-child(even) {
      background-color: #f2f2f2;
    }
  }
  
  :deep(br) {
    margin-bottom: 5px;
  }
}

.knowledge-name {
  cursor: pointer;
  
  &:hover .knowledge-title {
    color: #5a85ec;
    text-decoration: underline;
  }
}

.lesson-container {
  border: 1px solid #EAEAEA;
  overflow: hidden;
  background: #FFFFFF;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
  position: relative;
}

.lesson-title {
  position: absolute;
  text-align: center;
  font-size: 12px;
  color: #5A85EC;
  width: 60px;
  line-height: 24px;
  border: 1px solid #5A85EC;
  background: rgba(238, 243, 253, 1);
}

.lesson-content {
  padding: 10px 0 0 0;
  background: rgba(250, 251, 253, 1);
}

.empty-state {
  // display: flex;
  // justify-content: center;
  // align-items: center;
  height: 300px;
  border-radius: 8px;
  margin-top: 200px;
  width: 900px;
  text-align: center;
}

.empty-message {
  font-size: 16px;
  color: #999;
}

.challenge-area {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.challenge-btn {
  width: 112px;
  height: 48px;
  background: url('@/assets/img/percision/training/challenge_btn_bg.png')center no-repeat;
  background-size: 100%;
  font-size: 16px;
  font-weight: 700;
  color: #fff;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 1;
  transition: opacity 0.3s ease;
  
  &:hover {
    opacity: 0.9;
    transform: scale(1.02);
  }
}

.challenge-status-image {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 112px;
  height: 48px;
  
  .status-icon {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    
    &:hover {
      transform: scale(1.01);
    }
    
    // 针对不同状态图片的特殊样式
    &[alt="perfect"] {
      // 完美完成图片样式
      opacity: 1;

    }
    
    &[alt="wrong_questions"] {
      // 错题复习图片样式
      opacity: 1;

      cursor: pointer;
      
      &:hover {
        transform: scale(1.02);

      }
    }
  }
}
.page-header {
  margin-bottom: 18px;
  margin-top: 10px;
}

.breadcrumbs {
  font-size: 14px;
  color: #606266;
  display: flex;
  align-items: center;
}

.breadcrumbs .back-link {
  color: #00bfa5;
  font-size: 16px;
  text-decoration: none;
}
.breadcrumbs .back-link:hover {
  color: #00bfa5;
}

.breadcrumb-separator {
  color: #c0c4cc;
  margin: 0 5px;
}

.breadcrumb-item {
  color: #606266;
}
.breadcrumb-item.active {
  color: #303133;
  font-weight: 500;
}
</style>
<style lang="scss" scoped>
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.elevate-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.elevate-ct {
  width: 812px;
  height: 516px;
  border-radius: 20px;
  position: relative;
  background-repeat: no-repeat;
  background-size: 100%;
  color: #fff;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
}
.top-title{
  width: 160px;
  margin: 0 auto;
  padding-top: 86px;
  font-size: 16px;
  text-align: center;
  font-weight: bold;
  color: #fff; /* 内部为白色文字 */
  text-shadow: 
    1px 1px 0 rgba(217, 78, 50, 1),  
    -1px -1px 0 rgba(217, 78, 50, 1),  
    1px -1px 0 rgba(217, 78, 50, 1),  
    -1px 1px 0 rgba(217, 78, 50, 1); /* 外部为红色描边 */
  }
  .block-ct{
    width: 652px;
    height: 146px;background: rgba(255, 255, 255, 1);margin: 0 auto;margin-top: 20px;box-shadow: 0 0 3px 3px rgba(208, 192, 169, 1);border-radius: 14px;
    padding: 30px 30px 20px 30px;
    text-align: center;
    .book-list{
    padding: 0 20px 0 0px;
    display: flex;
    margin-bottom: 20px;
    img{
      width: 24px;
      height: 24px;
      margin-right: 8px;
    }
    .book-name{
      color: rgba(50, 58, 87, 1);
      font-size: 16px;
      width: 300px;
      text-align: left;
      white-space: nowrap;       /* 防止文字换行 */
      overflow: hidden;          /* 隐藏超出部分 */
      text-overflow: ellipsis; 
    }
    .book-tl{
      color: rgba(50, 58, 87, 1);
      font-size: 16px;
      padding-left: 46px;
      span{
        font-size: 16px;
        font-weight: 700;
        color: rgba(255, 151, 31, 1);
        padding-left: 10px;
      }
    }
  }
  .prompt{
    text-align: center;
    display: flex;
    color: rgba(42, 43, 42, 1);
    font-size: 16px;
    width: 260px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    display: inline-block;
    // padding-top: 20px;
    span{
      font-size: 20px;
      color: rgba(255, 151, 31, 1);
    }
  }
  }
  .challenge-fq{
  display: flex;
  color: rgba(50, 58, 87, 1);
  justify-content: center;
  align-items: center;
  margin: 26px auto 0 auto;
  font-size: 16px;
  
  .challenge-level-icon {
    width: 66px;
    height: 30px;
    margin: 0 5px;
    object-fit: contain;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    transition: transform 0.3s ease;
    
    &:hover {
      transform: scale(1.1);
    }
  }
  
  img{
    width: 66px;
    height: 30px;
    margin: 0 5px;
  }
}
.book-challenge{
    width: 112px;
    height: 46px;
    background: url('@/assets/img/percision/training/challenge_btn_bg.png')center no-repeat;
    background-size: 100%;
    font-weight: 700;
    color: #fff;
    cursor: pointer;
    font-size: 16px;
    line-height: 46px;
    text-align: center;
    margin: 40px auto 0 auto;
  }
.click-bt{
 display: flex;
 cursor: pointer;
}

.close-btn {
  position: absolute;
  top: -10px;
  right: -20px;
  margin-top: 20px;
  border-radius: 50%;
  // background-color: rgba(0, 0, 0, 0.1);
  color: #333;
  font-size: 24px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
  img{
    width: 54px;
    height: 54px;
  }
}

.close-btn:hover {
  // background-color: rgba(0, 0, 0, 0.2);
  transform: scale(1.1);
}
</style>

