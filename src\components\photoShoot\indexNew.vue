<template>
    <div class="camera-container">
        <!-- 拍照按钮 -->
        <div class="upload-btn" v-if="!videoState.isShoot" @click="openVideo">拍照上传</div>
    </div>
    <el-dialog
        v-model="videoState.dialogVisible"
        title="拍照"
        width="600"
        height="400"
        @close="closeVideo"
        :close-on-click-modal="false"
    >
        <div v-if="hasCamera" v-loading="loading" class="center-box">
            <!-- 视频预览 -->
            <div v-if="!photo">
                <video ref="video" autoplay playsinline class="video-preview"></video>
            </div>
            <!-- 裁剪组件 -->
            <div v-if="photo" class="cropper-container">
              <vue-cropper
                ref="cropper"
                :src="photo"
                :aspect-ratio="NaN"
                :drag-mode="'move'"
                :view-mode="2"
                :auto-crop-area="0.8"
                :background="false"
                :responsive="true"
                :restore="false"
                :check-cross-origin="false"
                :check-orientation="true"
                guides
              ></vue-cropper>
            </div>
            <div style="display: flex; justify-content: center; align-items: center;">
                <button v-if="!photo" @click="capture" class="capture-btn">拍照</button>
                <div v-else>
                    <button @click="reShoot">重新拍照</button>
                    <button @click="rotateImage(-90)">↺ 左转</button>
                    <button @click="rotateImage(90)">↻ 右转</button>

                    <button @click="crop">保存</button>
                </div>
            </div>
        </div>
        <div v-else style="height: 6.25rem;line-height: 6.25rem; text-align: center;">
            摄像头打开失败，请检查是否正确接入摄像头
        </div>
    </el-dialog>
</template>
<script lang="ts" setup>
import { ref, reactive } from 'vue'
import VueCropper from 'vue-cropperjs'
import 'cropperjs/dist/cropper.css'
import { uploadApi } from "@/api/user"
import { ElMessage } from 'element-plus'

const props = defineProps({
  length: {
    type: Number,
    default: 0
  }
})

const emit = defineEmits(['setImg'])

const video = ref<HTMLVideoElement | null>(null)
const photo = ref('')
const croppedPhoto = ref('')
const showCropper = ref(false)
const loading = ref(false)
const hasCamera = ref(true)
const cropper = ref<any>(null)
const videoState = reactive({
  dialogVisible: false, 
  isShoot: false
})
let stream: MediaStream | null = null

const openVideo = () => {
  videoState.dialogVisible = true
  startCamera()
}

const closeVideo = () => {
  videoState.dialogVisible = false
  hasCamera.value = true
  photo.value = ''
  if (stream) {
    stream.getTracks().forEach(track => track.stop())
  }
}

const startCamera = async () => {
  try {
    loading.value = true
    stream = await navigator.mediaDevices.getUserMedia({
      video: { width: 1280, height: 720, facingMode: 'environment' },
      audio: false
    })
    if (video.value) {
      video.value.srcObject = stream
    }
    loading.value = false
    hasCamera.value = true
  } catch (err) {
    hasCamera.value = false
    loading.value = false
    console.error('摄像头访问失败:', err)
  }
}
// 旋转图片
const rotateImage = (degrees: number) => {
  if (cropper.value) {
    cropper.value.rotate(degrees)
  }
}

const reShoot = () => {
  photo.value = ""
  startCamera()
}
const isIOS = () => {
  return (
    /iPad|iPhone|iPod/.test(navigator.userAgent) ||
    (navigator.platform === 'MacIntel' && navigator.maxTouchPoints > 1))
}
const isAndroidDevice = () => {
  // 更全面的安卓设备检测
  const ua = navigator.userAgent;
  const platform = navigator.platform;
  
  // 标准安卓检测
  if (/Android/i.test(ua)) return true;
  
  // 鸿蒙系统检测
  if (/HarmonyOS/i.test(ua)) return true;
  
  // 小米/华为平板检测
  if (/MiPad|HUAWEI/i.test(ua)) return true;
  
  // Linux ARM设备(大部分安卓设备)
  if (/Linux arm|Linux aarch64/i.test(platform)) return true;
  
  // 触摸设备且不是iOS
  if (navigator.maxTouchPoints > 1 && !isIOS()) {
    // 移动设备尺寸判断
    const isMobileSize = window.screen.width < 1024 || 
                        window.screen.height < 1024;
    return isMobileSize;
  }
  
  return false;
};

const capture = async () => {
  if (!video.value || !stream) return;

  try {
    const isAndroid = isAndroidDevice();
    const isIos = isIOS();
    
    // 安卓设备需要额外延迟
    if (isAndroid) {
      await new Promise(resolve => setTimeout(resolve, 200));
    }

    // 创建Canvas并设置尺寸
    const canvas = document.createElement('canvas');
    const videoElement = video.value;
    canvas.width = videoElement.videoWidth;
    canvas.height = videoElement.videoHeight;
    
    const ctx = canvas.getContext('2d', { willReadFrequently: true });
    if (!ctx) throw new Error('无法获取Canvas上下文');

    // 先绘制原始图像
    ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);
    
    // 检查是否全黑
    const imageData = ctx.getImageData(0, 0, 1, 1).data;
    if (imageData[0] === 0 && imageData[1] === 0 && imageData[2] === 0) {
      throw new Error('获取到全黑图像');
    }

    // 创建新Canvas用于旋转
    const rotatedCanvas = document.createElement('canvas');
    rotatedCanvas.width = canvas.width;
    rotatedCanvas.height = canvas.height;
    const rotatedCtx = rotatedCanvas.getContext('2d', { willReadFrequently: true });
    if (!rotatedCtx) throw new Error('无法获取旋转Canvas上下文');

    // 执行旋转操作
    rotatedCtx.save();
    
    // 仅对非iOS和非Android设备旋转180度
    if (!isAndroid && !isIos) {
      rotatedCtx.translate(rotatedCanvas.width / 2, rotatedCanvas.height / 2);
      rotatedCtx.rotate(Math.PI);
      rotatedCtx.drawImage(canvas, -canvas.width / 2, -canvas.height / 2);
    } else {
      // iOS和Android设备直接绘制
      rotatedCtx.drawImage(canvas, 0, 0);
    }
    
    rotatedCtx.restore();

    // iOS设备需要填充背景色
    if (isIos) {
      const tempCtx = rotatedCanvas.getContext('2d');
      if (tempCtx) {
        // 先绘制图像，再填充背景
        tempCtx.globalCompositeOperation = 'source-over';
        tempCtx.drawImage(rotatedCanvas, 0, 0);
        tempCtx.globalCompositeOperation = 'destination-over';
        tempCtx.fillStyle = '#ffffff';
        tempCtx.fillRect(0, 0, rotatedCanvas.width, rotatedCanvas.height);
      }
    }

    // 获取图像数据
    photo.value = rotatedCanvas.toDataURL('image/jpeg', 0.92);

    // 停止视频流但不重置video元素
    stream.getTracks().forEach(track => track.stop());

    showCropper.value = true;
  } catch (err: any) {
    ElMessage.error(`拍照失败: ${err.message}`);
    if (!photo.value) {
      setTimeout(() => {
        reShoot();
        setTimeout(() => capture(), 500);
      }, 300);
    }
  }
};

const crop = () => {
  if (cropper.value) {
    const croppedCanvas = cropper.value.getCroppedCanvas()
    croppedPhoto.value = croppedCanvas.toDataURL('image/png')
    showCropper.value = false
  }
  upload()
  closeVideo()
}

const upload = async () => {
  if (!cropper.value || !croppedPhoto.value) return;
  
  try {
    // 检查裁剪结果是否有效
    const croppedCanvas = cropper.value.getCroppedCanvas();
    if (croppedCanvas.width === 0 || croppedCanvas.height === 0) {
      throw new Error('无效的裁剪结果');
    }
    const isIos = isIOS();

    // 苹果设备可能需要额外处理
    if (isIos) {
      const tempCanvas = document.createElement('canvas');
      tempCanvas.width = croppedCanvas.width;
      tempCanvas.height = croppedCanvas.height;
      const tempCtx = tempCanvas.getContext('2d');
      if (tempCtx) {
        tempCtx.fillStyle = '#ffffff';
        tempCtx.fillRect(0, 0, tempCanvas.width, tempCanvas.height);
        tempCtx.drawImage(croppedCanvas, 0, 0);
        croppedPhoto.value = tempCanvas.toDataURL('image/jpeg', 0.9);
      }
    }

    const file = base64ToFile(croppedPhoto.value, `photo_${Date.now()}.jpg`);
    const formData = new FormData();
    formData.append("file", file);
    
    const res: any = await uploadApi(formData);
    emit('setImg', res.data);
    
  } catch (err) {
    console.error('上传失败:', err);
    // 可以在这里添加用户提示
  }
};

const base64ToFile = (base64: string, filename: string) => {
  const arr = base64.split(',')
  const mime = arr[0]?.match(/:(.*?);/)![1]
  const bstr = atob(arr[1]?arr[1]:'')
  
  let n = bstr.length
  const u8arr = new Uint8Array(n)
  
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  
  return new File([u8arr], filename, { type: mime })
}
</script>

<style scoped>
.video-preview, .photo-preview {
    max-width: 100%;
    height: auto;
    display: block;
    margin: .625rem 0;
}

.cropper-container {
  width: 100%;
  max-width: 31.25rem;
  height: 18.75rem;
  margin: 0 auto;
  background-color: #f5f5f5;
}

.video-preview {
  width: 100%;
  max-height: 18.75rem;
  background-color: #000;
}
/* 苹果设备特殊样式 */
@supports (-webkit-touch-callout: none) {
  .cropper-container {
    background-color: #fff;
  }
}
button {
    margin: .3125rem;
    padding: .5rem 1rem;
    background: #42b983;
    color: white;
    border: none;
    border-radius: .25rem;
    cursor: pointer;
}

button:hover {
    background: #369f6b;
}
.photo1 {
    width: 50rem;
    height: 31.25rem;
}
.upload-btn {
    width: 8.125rem;
    padding: .375rem 0;
    border-radius: .25rem;
    border: .0625rem solid #dddddd;
    background: #ffffff;
    color: #666666;
    text-align: center;
    font-size: .875rem;
    font-weight: 400;
    cursor: pointer;
    margin-bottom: .625rem;
}
.camera-container {
    display: flex;
    flex-direction: row-reverse;
    padding-right: 10rem;
}
.center-box {
    display: flex;
    align-items: center;
    flex-direction: column;
}
:deep(.cropper-modal) {
    cursor: pointer;
}

:deep(.cropper-container) {
    cursor: move;
}
.el-dialog__title{
  font-size: 1rem !important;
}
</style>