import axios, { type AxiosInstance, type AxiosRequestConfig } from "axios"
import { useUserStoreHook } from "@/store/modules/user"
import { ElMessage } from "element-plus"
import { get } from "lodash-es"
import { getToken } from "@/utils/cache/cookies"
import router from "@/router"
import { Encrypt, Decrypt } from "@/utils/secret"
// 创建请求实例
function createService() {
  const service = axios.create()
  // 请求拦截
  service.interceptors.request.use(
    (config : any) => {
      const url = config.url || ""
      const isSecret = config.method == "get" ? config.params?.needSecret : config.data.needSecret
      if (isSecret && isSecret != undefined) {
        if (config.method == "get") {
          const text = { text: Encrypt(JSON.stringify(config.params)) }
          config.params = text
        } else if (config.method == "post") {
          const text = { text: Encrypt(JSON.stringify(config.data)) }
          config.data = text
        }
      }
      if (url.indexOf("/webapi/seid") > -1 && config.method?.toLocaleUpperCase() === "POST") {
        Object.assign(config.data, { source: "seid" })
      }
      //判断get请求,添加请求头
      if (config.method == "get" || config.headers['Content-Type'] == 'application/x-www-form-urlencoded' || config.headers['Content-Type'] == 'multipart/form-data') {
        //小优平板
        // config.headers["deviceType"] = "801"
        // config.headers["ts-app-source"] = "xiaoyeoo"
        // config.headers["sdk-type"] = "true"
        // config.headers["Authorization"] = localStorage.token || ''
        // config.headers["learnId"] = localStorage.learnNow ? JSON.parse(localStorage.learnNow)?.learnId : ''
        // config.headers["ts-user-id"] = localStorage.userInfo ? JSON.parse(localStorage.userInfo)?.sysUserId : ''
        // config.headers["gradeId"] = localStorage.learnNow ? JSON.parse(localStorage.learnNow)?.gradeId : ''
        // config.headers["termId"] = localStorage.learnNow ? JSON.parse(localStorage.learnNow)?.termId : ''

        //自习室
        config.headers["deviceType"] = "801"
        config.headers["ts-app-source"] = "xiaoyeoo"
        config.headers["sdk-type"] = "true"
        // config.headers["port-type"] = "web"
        config.headers["Authorization"] = localStorage.token || ''
        config.headers["learnId"] = localStorage.userInfo ? JSON.parse(localStorage.userInfo)?.learnId : ''
        config.headers["ts-user-id"] = localStorage.userInfo ? JSON.parse(localStorage.userInfo)?.sysUserId : ''
        config.headers["gradeId"] = localStorage.learnNow ? JSON.parse(localStorage.learnNow)?.gradeId : ''
        config.headers["termId"] = localStorage.learnNow ? JSON.parse(localStorage.learnNow)?.termId : ''
      }
      if (localStorage.getItem("isIframe")) {
         config.headers["Authorization"] = localStorage.token || ''
      }  
      config.headers["port-type"] = "web"
      return config
    },
    // 发送失败
    (error) => Promise.reject(error)
  )
  // 响应拦截（可根据具体业务作出相应的调整）
  service.interceptors.response.use(
    (response) => {
      const data = response.data as any
      let code = data.code,
        msg = data.msg || "Error"
      if (response?.config?.responseType === "blob") {
        //处理流数据转成excel和json
        const { data } = response
        const type = data.type
        if (type == "application/json") {
          // return response
          //将 Blob对象 读成json字符串
          const reader : any = new FileReader()
          reader.readAsText(data, "utf-8")
          reader.onload = function () {
            const res = JSON.parse(reader.result)
            code = res.code
            msg = res.data
            //复用之前逻辑
            if (code) {
              return res
              if (code == 200) {
                return res
              } else if (code == 509911 || code == 500) {
                // TOKEN已失效,请重新登录
                ElMessage.closeAll()
                if (
                  msg.indexOf("重新登录") > -1 ||
                  msg.indexOf("认证令牌已过期") > -1 ||
                  msg.indexOf("没有认证") > -1
                ) {
                  useUserStoreHook().logout()
                  ElMessage.error("登录信息已失效,请重新登录")
                } else {
                  ElMessage.error(msg)
                }
                // return Promise.reject(msg)
                return res
              } else {
                return res
              }
            }
          }
        } else {
          //表格
          return response
        }
      } else {
        if (code) {
          if (code == 200) {
            let needSecret = false
            if (response.config.url?.includes("V2")) {
              if (response.config.method == "get") {
                needSecret = JSON.parse(Decrypt(response.config.params.text))?.needSecret
              } else if (response.config.method == "post") {
                needSecret = JSON.parse(response.config.data).text
                  ? JSON.parse(Decrypt(JSON.parse(response.config.data).text)).needSecret
                  : false
              }
              if (needSecret && data.data) {
                data.data = JSON.parse(Decrypt(data.data))
              }
            }
            return data
          } else if (code == 509911 || code == 500 || code == 401) {
            // TOKEN已失效,请重新登录
            ElMessage.closeAll()
            if (msg.indexOf("重新登录") > -1 || (msg.indexOf("已过期") > -1 && msg.indexOf("会员已过期") == -1) || msg.indexOf("没有认证") > -1 || msg.indexOf("另一台设备登录") > -1) {
              useUserStoreHook().logout()
              ElMessage.error("登录信息已失效,请重新登录")
            } else {
              if (msg.indexOf('Source must not be null') > -1) {
                //积分没数据
                return
              }
              ElMessage.error(msg)
            }
            return Promise.reject(data)
          } else if (code == 5001) {
            // TOKEN已失效,请重新登录
            ElMessage.closeAll()
            ElMessage.error(msg)
            const userInfo = JSON.parse(localStorage.getItem("userinfo") || "{}")
            router.push({ path: userInfo.orgType == 5 ? "/self_study_management/teacher_management" : "/vip/buy" })
            return Promise.reject(data)
          } else {
            // 其他错误码
            ElMessage.closeAll()
            ElMessage.error(msg)
            return Promise.reject(data)
          }
        } else {
          ElMessage.closeAll()
          return Promise.reject(data)
          // ElMessage.error("非本系统的接口")
          // return Promise.reject(new Error("非本系统的接口"))
        }
      }
    },
    (error) => {
      // Status 是 HTTP 状态码
      const status = get(error, "response.status")
      switch (status) {
        case 400:
          error.msg = "请求错误"
          break
        case 401:
          // Token 过期时，直接退出登录并强制刷新页面（会重定向到登录页）
          // error.msg = "您的账号已在另一台设备登录 !" //登录信息已失效,请重新登录
          useUserStoreHook().logout(true)
          // location.reload()
          break
        case 403:
          error.msg = "拒绝访问"
          break
        case 404:
          error.msg = "请求地址出错"
          break
        case 408:
          error.msg = "请求超时"
          break
        case 500:
          error.msg = "服务器内部错误"
          break
        case 501:
          error.msg = "服务未实现"
          break
        case 502:
          error.msg = "网关错误"
          break
        case 503:
          error.msg = "服务不可用"
          break
        case 504:
          error.msg = "网关超时"
          break
        case 505:
          error.msg = "HTTP 版本不受支持"
          break
        default:
          error.msg = "服务器超时了"
          break
      }
      ElMessage.closeAll()
      ElMessage.error(status == 401 ? error.response.data.data : error.data)
      return Promise.reject(error)
    }
  )
  return service
}

/** 创建请求方法 */
function createRequestFunction(service : AxiosInstance) {
  return function <T>(config : AxiosRequestConfig) : Promise<T> {
    const configDefault = {
      // 100.沃同学 101.考试大师 102.学数季 103爱得森 104小优小程序,105趣印,106迪沃用户,107小霸王,108飞硕 801 小优平板 
      headers: {
        //小优平板
        // "deviceType": "801",
        // // "typeId": "-1",
        // "ts-app-source": "xiaoyeoo",
        // "gradeId": localStorage.learnNow ? JSON.parse(localStorage.learnNow)?.gradeId : '',
        // "termId": localStorage.learnNow ? JSON.parse(localStorage.learnNow)?.termId : '',
        // "sdk-type": "true",
        // "Authorization": localStorage.token || '',
        // "learnId": localStorage.learnNow ? JSON.parse(localStorage.learnNow)?.learnId : '',
        // "ts-user-id": localStorage.userInfo ? JSON.parse(localStorage.userInfo)?.sysUserId : ''

        //自习室
        "deviceType": "801",
        // "typeId": "-1",
        "ts-app-source": "xiaoyeoo",
        "gradeId": localStorage.learnNow ? JSON.parse(localStorage.learnNow)?.gradeId : '',
        "termId": localStorage.learnNow ? JSON.parse(localStorage.learnNow)?.termId : '',
        "sdk-type": "true",
        "Authorization": config?.headers?.Authorization || getToken(),
        "learnId": localStorage.userInfo ? JSON.parse(localStorage.userInfo)?.learnId : '',
        "ts-user-id": localStorage.userInfo ? JSON.parse(localStorage.userInfo)?.sysUserId : ''
      },
      timeout: 30000,
      baseURL: import.meta.env.VITE_BASE_API,
      data: {}
    }
    return service(Object.assign(configDefault, config))
  }
}
// 记录已经在统一拦截里toast错误的code，不在此内的，业务根据需要自己弹窗错误
export const hadToastErrorMsgCode = [509911, 500, 401]
/** 用于网络请求的实例 */
export const service = createService()
/** 用于网络请求的方法 */
export const request = createRequestFunction(service)