<template>
    <div v-loading="analyseState.loading">
        <div class="base-data">
            <div class="base-data-title">本学期总体情况</div>
            <div class="base-data-content">
                <div class="base-data-content-item bg1">
                    <div class="base-data-content-item-title">训练次数</div>
                    <div class="base-data-content-item-data"><span>{{ trainReport.trainCount }}</span>次</div>
                    <div class="base-data-content-item-bottom">打败了{{ (trainReport.trainDefeatRate * 100).toFixed(2) }}%同年级用户！</div>
                </div>
                <div class="base-data-content-item bg2">
                    <div class="base-data-content-item-title">做题量</div>
                    <div class="base-data-content-item-data"><span>{{ trainReport.quesCount }}</span>题</div>
                    <div class="base-data-content-item-bottom">打败了{{ (trainReport.quesDefeatRate * 100).toFixed(2) }}%同年级用户！</div>
                </div>
                <div class="base-data-content-item bg3">
                    <div class="base-data-content-item-title">题目整体难度</div>
                    <div class="base-data-content-item-data"><span>{{ getDegreeName(trainReport.degreeType) }}</span></div>
                    <div class="progress-box">
                        <div class="progress-box-item" :class="trainReport.degreeType >= 1?'blue':''">
                            <span class='line'></span>
                        </div>
                        <div class="progress-box-item" :class="trainReport.degreeType >= 2?'blue':''">
                            <span class='line'></span>
                        </div>
                        <div class="progress-box-item" :class="trainReport.degreeType >= 3?'blue':''">
                            <span class='line'></span>
                        </div>
                        <div class="progress-box-item" :class="trainReport.degreeType >= 4?'blue':''">
                            <span class='line'></span>
                        </div>
                        <div class="progress-box-item" :class="trainReport.degreeType >= 5?'blue':''"></div>
                    </div>
                </div>
                <div class="base-data-content-item bg4">
                    <div class="base-data-content-item-title">平均正确率</div>
                    <div class="base-data-content-item-data"><span>{{ trainReport.correctRate }}</span>%</div>
                    <div class="base-data-content-item-bottom">打败了{{ (trainReport.correctDefeatRate * 100).toFixed(2) }}%同年级用户！</div>
                </div>
            </div>
        </div>
        <div class="base-data" v-if="!isWen">
            <div class="base-data-title">本学期知识点掌握情况</div>
            <div class="knowledge-data-content">
                <div class="knowledge-data-content-item green">
                    <div class="knowledge-data-content-item-title"><span></span>强项知识点</div>
                    <div class="knowledge-data-content-item-data"><span>{{ masteryOfPoint.strongPoint }}</span>个</div>
                </div>
                <div class="knowledge-data-content-item yellow">
                    <div class="knowledge-data-content-item-title"><span></span>待加强知识点</div>
                    <div class="knowledge-data-content-item-data"><span>{{ masteryOfPoint.reinforcePoint }}</span>个</div>
                </div>
                <div class="knowledge-data-content-item red">
                    <div class="knowledge-data-content-item-title"><span></span>薄弱知识点</div>
                    <div class="knowledge-data-content-item-data"><span>{{ masteryOfPoint.weaknessPoint }}</span>个</div>
                </div>
                <div class="knowledge-data-content-item blue">
                    <div class="knowledge-data-content-item-title"><span></span>待定知识点</div>
                    <div class="knowledge-data-content-item-data"><span>{{ masteryOfPoint.waitPoint }}</span>个</div>
                </div>
                <div class="knowledge-data-content-item blue">
                    <div class="knowledge-data-content-item-title"><span></span>未训练知识点</div>
                    <div class="knowledge-data-content-item-data"><span>{{ masteryOfPoint.notStart }}</span>个</div>
                </div>
            </div>
            <div class="knowledge-point">
                <div class="knowledge-point-item" v-for="(item,i) in pointReports" :key="i">
                    <div class="knowledge-point-item-left">
                        <div class="knowledge-point-item-left-top">
                            <div>{{ item.pointName }}</div>
                            <div>掌握度：<span :class="Number(item.pointRate) > 0.85?'green-text':Number(item.pointRate) > 0.65?'yellow-text':Number(item.pointRate) > 0?'red-text':''">{{ Number(item.pointRate) < 0 ? '未训练':((item.pointRate * 100).toFixed(2) + '%') }}</span></div>
                        </div>
                        <div>
                            <el-progress :percentage="Number(item.pointRate) < 0? 0: (Number(item.pointRate) * 100)" :show-text="false" :status="Number(item.pointRate) > 0.85?'success':Number(item.pointRate) > 0.65?'warning':'exception'" />
                        </div>
                    </div>
                    <div class="knowledge-point-item-right">
                        <div class="btn" @click="goRegardingLearning(item)"><img src="@/assets/img/percision/play-blue.png" /> 去学习</div>
                        <div class="btn" @click="goGraphDetail(item)">查看图谱</div>
                    </div>
                </div>
                <div v-if="pointReports.length > 0" class="pagination-box">
                    <Pagination
                        :total="pageData.total"
                        :layout="'total, prev, pager, next, jumper'"
                        :current="pageData.current"
                        @currentSizeChange="currentSizeChange" />
                </div>
            </div>
        </div>
        <div class="base-data" v-if="!isWen">
            <div class="base-data-title">能力雷达图</div>
            <div class="radar-box">
                <EchartsComponent v-if="options.radar.indicator.length > 0" :width="'29.8125rem'" :height="'20.5625rem'" :option="options"></EchartsComponent>
                <div v-else class="empty">
                    <img src="@/assets/img/percision/empty.png" alt="no-data" />
                    <p>空空如也</p>
                </div>
            </div>
        </div>
        <div class="base-data">
            <div class="base-data-title">练习记录</div>
            <div class="record-box" v-if="recordData.length > 0" v-loading="analyseState.recordloading">
                <el-table :data="recordData" class="table">
                    <el-table-column prop="createTime" label="时间"></el-table-column>
                    <el-table-column prop="content" label="内容" />
                    <el-table-column prop="title" label="章节" />
                    <el-table-column prop="quesCount" label="题量" >
                    </el-table-column>
                    <el-table-column prop="correct" label="成绩">
                        <template #default="scope">
                            <template v-if="scope.row.status==2">
                              <span v-if="Number(scope.row.score)">{{scope.row.score}}分</span>
                              <span v-else>正确率{{scope.row.correctRate}}%</span>
                            </template>
                            <span v-else-if="scope.row.status==1">未完成</span>
                        </template>
                    </el-table-column>
                    <el-table-column label="详情">
                        <template #default="scope">
                            <div style="color:#009C7F;text-decoration: underline;cursor: pointer;" @click="goTrainingDetail(scope.row)">查看详情 ></div>
                        </template>
                    </el-table-column>
                </el-table>
                <div class="pagination-box">
                    <Pagination
                        :total="pageData1.total"
                        :current="pageData1.current"
                        :layout="'total, prev, pager, next, jumper'"
                        @currentSizeChange="currentSizeChange1" />
                </div>
            </div>
            <div v-else class="empty">
                <img src="@/assets/img/percision/empty.png" alt="no-data" />
                <p>空空如也</p>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router'
import { useUserStore } from "@/store/modules/user"
import { computed, onMounted, reactive, ref } from 'vue'
import { dataDecrypt, dataEncrypt, getDegreeName, mergeObject } from "@/utils/secret"
import { userAnalyseApi } from '@/api/analyse'
import { listApi, detailsToPointApi } from '@/api/training'
import { getChapterByBookAndPointApi } from '@/api/book'
import EchartsComponent from "@/components/echarts/index.vue"
import { options } from "./data"
import { storeToRefs } from 'pinia'
import { wenke } from '@/utils/user/enum'
const userStore = useUserStore()
const { learnNow, userInfo, subjectObj } = storeToRefs(userStore)
const router = useRouter()
const analyseState = reactive({
  loading: false,
  recordloading: false
})
class IPage {
    total = 0
    current = 1
    size = 10
}
let pageData = reactive(new IPage())
let pageData1 = reactive(new IPage())
class ITrain {
    trainCount = 0
    trainDefeatRate = 0
    quesCount = 0
    quesDefeatRate = 0
    degree = 0
    correctRate = 0
    correctDefeatRate = 0
    degreeType = 0
}
class IMastery {
    strongPoint = 0
    waitPoint = 0
    weaknessPoint = 0
    reinforcePoint = 0
    notStart = 0
}
// 总体情况数据
let trainReport = reactive(new ITrain())
// 知识点掌握情况数据
let masteryOfPoint = reactive(new IMastery())

const isWen = computed(()=>{
  return wenke.includes(Number(subjectObj.value.id))
})
const pointReports = ref([] as any[])
const pointReportsAll = ref([] as any[])
const recordData = ref([])

onMounted(()=> {
    init()
})
const init = () => {
    userAnalyse()
    getTraininglist()
}
const userAnalyse = async () => {
    analyseState.loading = true
    const params: any = {
        subject: subjectObj.value.id
    }
    if (sessionStorage.getItem("tabName") != "Preparation") {
        params.bookId = subjectObj.value.bookId
    }
    userAnalyseApi(params).then((res: any) => {
        if (res.data) {
            trainReport = mergeObject(trainReport, res.data.trainReport)
            trainReport.degreeType = getDegreeType(res.data.trainReport.degree) as number
            masteryOfPoint = mergeObject(masteryOfPoint, res.data)
            pointReportsAll.value = res.data.pointReports
            pointReports.value = setPageData(1,pointReportsAll.value)
            let nameArr = [] as any[]
            let dataArr = [] as any[]
            res.data.personalTopicReports.map((item: any) => {
                nameArr.push({name: item.topicName, max: 1})
                dataArr.push(item.topicRate)
            })
            options.radar.indicator = nameArr
            options.series[0].data[0].value = dataArr
        } else {
            trainReport = reactive(new ITrain())
            masteryOfPoint = reactive(new IMastery())
            pageData = reactive(new IPage())
            pageData1 = reactive(new IPage())
            pointReportsAll.value = []
            pointReports.value = []
        }
        analyseState.loading = false

    }).catch((error) => {
        analyseState.loading = false
    })
}
const getDegreeType = (degree: number) => {
    if (Number(degree) > 0 && Number(degree) <= 0.2){
        return 5
    } else if (Number(degree) > 0.2 && Number(degree) <= 0.4){
        return 4
    } else if (Number(degree) > 0.4 && Number(degree) <= 0.6){
        return 3
    } else if (Number(degree) > 0.6 && Number(degree) <= 0.8){
        return 2
    } else if (Number(degree)> 0.8 && Number(degree) <= 1){
        return 1
    }
}
const getTraininglist = () => {
    analyseState.recordloading = true
    const params = {
        bookId: subjectObj.value.bookId,
        subject: subjectObj.value.subject,
        trainStatus: 2,
        order: 0,
        current: pageData1.current,
        size: pageData1.size,
        learnType: 0
    }
    listApi(params).then((res: any) => {
        if (res.data) {
          //处理正确率和分数
          let list = res.data?.records || []
          if (list.length) {
            for (let i of list) {
              i.score = parseFloat((Number(i.score)).toFixed(2))
              i.correctRate = parseFloat((Number(i.correctRate)).toFixed(2))
              // 处理content字段中的-null
              if (i.content && typeof i.content === 'string') {
                if (i.content.includes('-null')) {
                  i.content = i.content.replace(/-null/g, '')
                }
              }
            }
          }
          recordData.value = list
          pageData1.total = Number(res.data.total)
        }
        analyseState.recordloading = false

    }).catch((error) => {
        analyseState.recordloading = false
    })
}
// 跳训练详情
const goTrainingDetail = async (data2: any) => {
  // router.push({
  //     path: '/academic_report/training_reportA',
  //     query: {
  //       data: dataEncrypt({
  //           trainingId:data.trainingId,
  //           showStep: '0'
  //         })
  //     }
  // })
    if (isWen) {
        router.push({
            path: '/academic_report/training_reportA',
            query: {
            data: dataEncrypt({
                trainingId:data2.trainingId,
                showStep: '0',
                isWenk:false
                })
            }
        })
    } else {

      const chapterData: any = await getChapterByBookAndPoint(data2.pointIds[0])
      analyseState.loading = true
      detailsToPointApi({pointId: data2.pointIds[0]}).then((res: any) => {
          if (res.code == 200) {
              const data = res.data
              localStorage.setItem('diagnosticReport', JSON.stringify(data))
              router.push({
                  path: '/academic_report/training_reportA',
                  query: {
                    data: dataEncrypt({
                        trainingId:data2.trainingId,
                        sourceId: chapterData.id,
                        showStep: '0'
                      })
                  }
              })
          }
          analyseState.loading = false
      }).catch((error) => {
          analyseState.loading = false
      })
  }
}
const goGraphDetail = async (data: any) => {
    const chapterData: any = await getChapterByBookAndPoint(data.pointId)
    // 需要设置章节数据
    userStore.setChapterId(chapterData.id, chapterData.nameStr)
    router.push({
        path: '/academic_report/knowledge_graph_detailA'
    })
}
// const goRegardingLearning = async (data: any) => {
//     const chapterData: any = await getChapterByBookAndPoint(data.pointId)
//     router.push({
//         path: '/academic_report/regarding_learningA',
//         query: {
//             data: dataEncrypt({
//                 sourceId: chapterData.id,
//                 chapterId: chapterData.id,
//                 pointId: [data.pointId],
//                 subject: subjectObj.value.id
//             })
//         }
//     })
// }
const goRegardingLearning = async (data: any) => {
        router.push({
        name: 'TeachRoomTeachVideo',
        query: { 
          id: data.pointId,
          source: 'analysis',
          subject: subjectObj.value.id,
        } 
    })
}
// 获取章节数据
const getChapterByBookAndPoint = (pointId: any) => {
    return new Promise((resolve, reject) => {
        getChapterByBookAndPointApi({
            bookId: subjectObj.value.bookId,
            pointId
        }).then((res: any) => {
            if (res.data) {
                resolve(res.data)
            }
        }).catch((error) => {
            reject()
        })
    })
}
const setPageData = (currentPage: number, data: any) => {
    pageData.current = currentPage
    pageData.total = data.length
    const index = (currentPage - 1)*10
    return data.slice(index, index + 10)
}
const currentSizeChange1 = (currentPage: number, pageSize: number) => {
  pageData.current = currentPage
  pageData.size = pageSize
  getTraininglist()
}
const currentSizeChange = (currentPage: number, pageSize: number) => {
  pageData.current = currentPage
  pageData.size = pageSize
  pointReports.value = setPageData(currentPage,pointReportsAll.value)
}
defineExpose({ init })

</script>
<style lang="scss" scoped>
.base-data {
    min-height: 12.5625rem;
    background-color: #ffffff;
    padding-top: .625rem;
    box-sizing: border-box;
    margin-bottom: 1.25rem;
    &-title {
        width: 24rem;
        height: 2.5625rem;
        line-height: 2.5625rem;
        // margin: .625rem 0;
        border-left: .3125rem solid #5A85EC;
        background: linear-gradient(270deg, #ffffff 0%, #eef3fd 100%);
        color: #5a85ec;
        font-size: 1.125rem;
        font-weight: 700;
        padding-left: .9375rem;
        box-sizing: border-box;
        margin-bottom: .625rem;
    }
    &-content {
        width: 100%;
        display: flex;
        justify-content: space-around;
        &-item {
            width: 16.375rem;
            height: 7.5rem;
            border-radius: .25rem;
            background-size: contain;
            background-repeat: no-repeat;
            padding: .625rem 1.25rem;
            box-sizing: border-box;
            &-title {
                color: #7079a8;
                font-size: .875rem;
                font-weight: 400;
            }
            &-data {
                color: #37226d;
                font-size: .875rem;
                font-weight: 400;
                margin: .5rem 0;
                span {
                    color: #222e71;
                    font-size: 1.875rem;
                    font-weight: 700;
                    margin-right: .3125rem;
                }
            }
            &-bottom {
                color: #222e71;
                font-size: .875rem;
                font-weight: 400;
            }
        }
    }
}
.bg1 {
    background-image: url(@/assets/img/academic/term-bg1.png);
}
.bg2 {
    background-image: url(@/assets/img/academic/term-bg2.png);
}
.bg3 {
    background-image: url(@/assets/img/academic/term-bg3.png);
}
.bg4 {
    background-image: url(@/assets/img/academic/term-bg4.png);
}
.pagination-box{
    margin-top: 1.875rem;
}
.progress-box {
    width: 13.75rem;
    height: 1rem;
    border-radius: .5rem;
    background: #0a3a701a;
    display: flex;
    &-item {
        width: 2.8125rem;
        height: 100%;
        position: relative;
        padding: .0625rem 0;
        box-sizing: border-box;
        &:first-child {
            border-top-left-radius: .5rem;
            border-bottom-left-radius: .5rem;
        }
        &:last-child {
            border-top-right-radius: .5rem;
            border-bottom-right-radius: .5rem;
        }
        span {
            position: absolute;
            right: 0rem;
            display: inline-block;
            height: .875rem;
            width: .0625rem;
            background: #ffffff;
        }
    }
}
.blue {
    background-color: #0A3A70;
}
.empty {
  text-align: center;
  img {
    width: 7.4375rem;
    height: 8rem;
  }
  p {
    text-align: center;
    color: #999999;
    font-size: .875rem;
    font-weight: 400;
  }
}
.knowledge-data-content {
    display: flex;
    justify-content: space-around;
    padding: .625rem 1.25rem;
    box-sizing: border-box;
    &-item {
        width: 12.875rem;
        height: 5.6875rem;
        border-radius: .25rem;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        &-title {
            font-size: .875rem;
            font-weight: 400;
            display: flex;
            align-items: center;
            span {
                width: 1rem;
                height: 1rem;
                border-radius: 50%;
                display: inline-block;
                margin-right: .375rem;
            }
        }
        &-data {
            font-size: .875rem;
            font-weight: 400;
            margin-top: .75rem;
            span {
                font-size: 1.875rem;
                font-weight: 700;
                margin-right: .3125rem;
            }
        }
    }
    .green {
        background: #e5f9f6;
        .knowledge-data-content-item-title {
            color: #009c7f;
            span {
                background: #00c9a3;
            }

        }
    }
    .yellow {
        background: #FDF5E8;
        .knowledge-data-content-item-title {
            color: #EF9D19;
            span {
                background: #EF9D19;
            }

        }
    }
    .red {
        background: #FCE9E9;
        .knowledge-data-content-item-title {
            color: #DD2A2A;
            span {
                background: #DD2A2A;
            }

        }
    }
    .blue {
        background: #EEF3FD;
        .knowledge-data-content-item-title {
            color: #5A85EC;
            span {
                background: #5A85EC;
            }

        }
    }
}
:deep(.el-progress-bar__outer) {
  height: .625rem!important;
}
.knowledge-point {
    padding: 0rem 1.25rem 1.25rem 1.25rem;
    &-item {
        display: flex;
        padding-top: 1.25rem;
        margin-bottom: .625rem;
        &:first-child {
            border-top: .0625rem dashed #EAEAEA;
        }
        &-left {
            width: 53.25rem;
            margin-right: 1.875rem;
            &-top {
                display: flex;
                justify-content: space-between;
                color: #2a2b2a;
                font-size: .875rem;
                font-weight: 400;
                margin-bottom: .625rem;
            }
        }
        &-right {
            display: flex;
            padding-top: .5rem;
            .btn {
                width: 6.125rem;
                height: 1.9375rem;
                border-radius: .25rem;
                color: #009c7f;
                cursor: pointer;
                background: #e5f9f6;
                display: flex;
                align-items: center;
                justify-content: center;
                &:first-child {
                    margin-right: 1.25rem;
                }
                img {
                    width: 1rem;
                    height: 1rem;
                    margin-right: .375rem;
                }
            }
        }
    }
}
.yellow-text {
    color: #ef9d19;
}
.green-text {
    color: #009C7F
}
.red-text {
    color: #DD2A2A
}
.radar-box {
    width: 100%;
    display: flex;
    justify-content: center;
}
.record-box {
    padding: 1.25rem;
}
</style>
