<!-- 登录页 -->
<template>
  <div class="content">
    <div class="inner">
      <div class="header">
        <div class="logo">
          <!-- <img src="@/assets/img/login/logo1.png" class="logo_img" /> -->
          <img src="@/assets/img/login/logo.png" class="logo_img" />
          <div class="logo_h1">AI伴学</div>
          <div class="logo_h2">（学生端）</div>
        </div>
        <div class="blank">
                    <!-- 需跳转 ls.xyedu.com  6.23修改  fx.xyedu.com -->  
                     <!-- {{ urlAddress == 'https://ai.xyedu.com/login'}} -->
                    
          <a v-if="urlAddress == 'https://ai.xyedu.com/login'" href="https://fx.xyedu.com/login" target="_blank" style="float: left;">
            <img src="@/assets/img/login/share.png" class="blank_img1" />
            <img src="@/assets/img/login/blank.png" class="blank_img2" />
          </a>

          <a v-else href="https://tiku.xiaoyeoo.com/login" target="_blank" style="float: left;">
            <img src="@/assets/img/login/share.png" class="blank_img1" />
            <img src="@/assets/img/login/blank.png" class="blank_img2" />
          </a>
          
        </div>
      </div>
      <div class="wrap">
        <!-- 扫码登录 -->
        <div class="wechat" v-show="other==0">
          <div class="we_h1">
            <img src="@/assets/img/login/wechat.png" />微信扫码登录
          </div>
          <div class="we_tip">扫码登录小程序即可自动注册成功</div>
          <div class="we_qrcode">
            <!-- <img src="@/assets/img/qrcode.png" /> -->
            <!-- 扫码成功 -->
            <div class="we_success none">扫码成功，请授权登录</div>
          </div>
          <div class="other" @click="setOther(1)">
            <img src="@/assets/img/login/other.png" />其他登录方式
          </div>
          <div class="agent">
            注册登录即代表同意<router-link class="link" :to="'/user_agent'">《平台用户协议》</router-link>和<router-link class="link"
              :to="'/user_agent2'">《隐私政策》</router-link>
          </div>
        </div>
        <!-- 其他登录 -->
        <div class="another" v-show="other==1">
          <div class="tabs">
            <!-- active选中 disabled禁用 -->
            <div class="tab" :class="tab==1?'active':''" @click="tabs(1)">密码登录</div>
            <div class="tab" :class="tab==2?'active':''" @click="tabs(2)">验证码登录</div>
            <!-- <div class="tab" :class="tab==3?'active':''" @click="tabs(3)">注册</div> -->
          </div>
          <!-- 密码登录 -->
          <div class="form" v-show="tab==1">
            <el-form ref="form1Ref" :model="param1" :rules="rules1" @keyup.enter="pwdLogin" @submit.prevent>
              <el-form-item prop="phone" class="tel">
                <label class="label">手机号</label>
                <img src="@/assets/img/login/tel.svg" class="tel_img" />
                <el-input class="input" v-model="param1.phone" placeholder="请输入手机号" type="text" tabindex="1"
                  maxlength="11" prefix-icon="Iphone" @input="setNumber" />
              </el-form-item>
              <el-form-item prop="password" class="tel pwd2">
                <label class="label">密码</label>
                <img src="@/assets/img/login/pwd.svg" class="tel_img" />
                <el-input class="input" v-model.trim="param1.password" placeholder="请输入6-20位密码" type="password"
                  tabindex="2" prefix-icon="Lock" show-password maxlength="20" />
              </el-form-item>
              <div class="passbox">
                <div class="remember" @click="setRemember">
                  <span class="agree" :class="param1.remember=='1'?'checked':''">
                    <img src="@/assets/img/login/check1.svg" />
                    <img src="@/assets/img/login/check2.svg" />
                  </span>
                  记住密码
                </div>
                <div class="forget"><router-link class="link" :to="'/forget'">忘记密码？</router-link></div>
              </div>
              <div class="submit" :loading="loading" @click.prevent="pwdLogin">登 录</div>
              <div class="other none" @click="setOther(0)">
                <img src="@/assets/img/login/scan.svg" />微信扫码登录
              </div>
            </el-form>
          </div>
          <!-- 验证码登录 -->
          <div class="form" v-show="tab==2">
            <el-form ref="form2Ref" :model="param2" :rules="rules2" @keyup.enter="msgLogin" @submit.prevent>
              <el-form-item prop="phone" class="tel">
                <label class="label">手机号</label>
                <img src="@/assets/img/login/tel.svg" class="tel_img" />
                <el-input class="input" v-model="param2.phone" placeholder="请输入手机号" type="text" tabindex="1"
                  maxlength="11" prefix-icon="Iphone" @input="setNumber2" />
              </el-form-item>
              <el-form-item prop="code" class="msg">
                <label class="label">验证码</label>
                <img src="@/assets/img/login/msg.svg" class="tel_img" />
                <el-input class="input" v-model="param2.code" placeholder="请输入验证码" type="text" tabindex="2"
                  maxlength="6" prefix-icon="Iphone" @input="setNumber3" />
                <el-button type="primary" class="code" @click="sendMsg" :loading="param2.sendLoad"
                  :disabled="param2.disabled == 1">
                  {{ param2.sendtxt }}
                </el-button>
              </el-form-item>
              <div class="submit" :loading="loading" @click.prevent="msgLogin">登 录</div>
              <div class="other none" @click="setOther(0)">
                <img src="@/assets/img/login/scan.svg" />微信扫码登录
              </div>
            </el-form>
          </div>
          <!-- 注册 -->
          <div class="form" v-show="tab==3">
            <div class="step1" v-show="step==1">
              <el-form ref="form3Ref" :model="param3" :rules="rules3" @keyup.enter="nextStep" @submit.prevent>
                <el-form-item prop="phone" class="tel">
                  <label class="label">手机号</label>
                  <img src="@/assets/img/login/tel.svg" class="tel_img" />
                  <el-input class="input" v-model="param3.phone" placeholder="请输入手机号" type="text" tabindex="1"
                    maxlength="11" prefix-icon="Iphone" @input="setNumber4" />
                </el-form-item>
                <el-form-item prop="code" class="msg">
                  <label class="label">验证码</label>
                  <img src="@/assets/img/login/msg.svg" class="tel_img" />
                  <el-input class="input" v-model="param3.code" placeholder="请输入验证码" type="text" tabindex="2"
                    maxlength="6" prefix-icon="Iphone" @input="setNumber5" />
                  <el-button type="primary" class="code" @click="sendMsg2" :loading="param3.sendLoad"
                    :disabled="param3.disabled == 1">
                    {{ param3.sendtxt }}
                  </el-button>
                </el-form-item>
                <div class="submit" :loading="loading" @click.prevent="nextStep">下一步</div>
              </el-form>
            </div>
            <div class="step2" v-show="step==2">
              <el-form ref="form4Ref" :model="param4" :rules="rules4" @keyup.enter="msgLogin2" @submit.prevent>
                <el-form-item prop="password" class="tel">
                  <label class="label">请设置密码</label>
                  <img src="@/assets/img/login/pwd.svg" class="tel_img" />
                  <el-input class="input" v-model.trim="param4.password" placeholder="请输入6-20位密码" type="password"
                    tabindex="1" prefix-icon="Lock" show-password maxlength="20" />
                </el-form-item>
                <el-form-item prop="password2" class="tel pwd2">
                  <label class="label">请确认密码</label>
                  <img src="@/assets/img/login/pwd.svg" class="tel_img" />
                  <el-input class="input" v-model.trim="param4.password2" placeholder="请输入6-20位密码" type="password"
                    tabindex="2" prefix-icon="Lock" show-password maxlength="20" />
                </el-form-item>
                <div class="submit" :loading="loading" @click.prevent="msgLogin2">确 定</div>
                <div class="back" @click="setStep(1)">返 回</div>
              </el-form>
            </div>
          </div>
          <!-- <div class="agent">
            <span class="agree" :class="isAgree?'checked':''" @click="setAgree">
              <img src="@/assets/img/login/check1.svg" />
              <img src="@/assets/img/login/check2.svg" />
            </span>
            <span @click="setAgree">请先阅读和勾选同意</span><router-link class="link"
              :to="'/user_agent'">《平台用户协议》</router-link>和<router-link class="link"
              :to="'/user_agent2'">《隐私政策》</router-link>
          </div> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { reactive, ref, getCurrentInstance, onMounted } from "vue"
  import { useUserStore } from "@/store/modules/user"
  import { type FormInstance, FormRules, ElMessage } from "element-plus"
  import { setToken } from "@/utils/cache/cookies"
  import { pwdLoginApi, sendCodeApi, registerApi } from "@/api/login"
  import { userGetAllApi } from "@/api/user"
  import router from "@/router"
  import { gradeNameList } from '../../utils/user/enum'
  const urlAddress = ref()
  const loading = ref(false)

  onMounted(() => {
    urlAddress.value = window.location.href
    init()
  })
  const init = () => {
    checkPwd()
  }

  //切换其他登录
  const other = ref(1)
  const setOther = (num : any) => {
    other.value = num
  }
  // tab菜单切换
  const tab = ref(1)
  const tabs = (num : any) => {
    //清除校验
    form1Ref.value.clearValidate()
    form2Ref.value.clearValidate()
    tab.value = num
  }

  //扫码登录s
  const qrcode = ref('')

  //扫码登录e

  // 账号登录s
  const form1Ref = ref<FormInstance | any>()
  const param1 = reactive({
    phone: "",
    password: "",
    remember: localStorage.remember || '0',
    type: 1
  })
  const rules1 : FormRules = {
    phone: [{ required: true, message: "请输入账号/手机号", trigger: "blur" }],
    password: [
      { required: true, message: "请输入密码", trigger: "change" }
    ]
  }

  //输入数字
  const setNumber = (val : string) => {
    val = val.replace(/[^\d]/g, "")
    param1.phone = val
  }
  // 记住密码
  const setRemember = () => {
    if (param1.remember == '0') {
      param1.remember = '1'
      localStorage.remember = "1"
    } else {
      param1.remember = '0'
      localStorage.remember = "0"
    }
  }
  // 判断记住密码
  const checkPwd = () => {
    if (param1.remember == '1') {
      param1.phone = useUserStore().phone
      param1.password = useUserStore().password
    } else {
      param1.phone = useUserStore().phone
      // localStorage.removeItem("phone")
      localStorage.removeItem("password")
      localStorage.removeItem("remember")
    }
  }
  //勾选同意
  const isAgree = ref(true)
  const setAgree = () => {
    const red = isAgree.value ? false : true
    isAgree.value = red
  }
  // 账号登录
  const pwdLogin = () => {
    form1Ref.value?.validate((valid : boolean, objects : any) => {
      if (valid) {
        if (!isAgree.value) {
          ElMessage.error("请先阅读和勾选同意《平台用户协议》和《隐私政策》")
          return
        }
        loading.value = true
        let phone = param1.phone,
          password = param1.password
        const param = {
          phone,
          password,
          type: 1//1账号密码 2短信
        }
        pwdLoginApi(param)
          .then((res : any) => {
            loading.value = false
            //记住密码
            if (param1.remember) {
              localStorage.phone = phone
              localStorage.password = password
            } else {
              localStorage.removeItem("phone")
              localStorage.removeItem("password")
            }
            userGetAll(res)
          })
          .catch(() => {
            loading.value = false
          })
      }
    })
  }

  //获取用户列表
  const userGetAll = async (res2 : any) => {
    checkPwd()
    let data = res2.data || ''
    if (data?.token) {
      //追加手机号
      res2.data.phone = localStorage.phone
      let userInfo = JSON.stringify(res2.data),
        token = data.token, sysUserId = data.sysUserId
      localStorage.isLogin = "true"
      localStorage.token = token
      localStorage.sysUserId = sysUserId
      localStorage.userInfo = userInfo
      await setToken(token)
      useUserStore().token = token
      useUserStore().sysUserId = sysUserId
      useUserStore().userInfo = userInfo
      //获取学习用户
      if (data?.sysUserId) {
        userGetAllApi().then((res : any) => {
          // console.log(res,"打印机那你  登录 re s")
          const arr:any = [res.data]
          if (arr.length) {
            //判断学习用户数
            arr.forEach((item : any) => {
              item.gradeName = gradeNameList[item.gradeId]
            })
            localStorage.learnUsers = JSON.stringify(arr)
            let usersNow = localStorage.learnNow ? JSON.parse(localStorage.learnNow) : ''
            if (usersNow) {
              //匹配当前学生是否在学生列表
              let isTrue = 0
              for (const i of arr) {
                if (usersNow.learnId == i.learnId) {
                  isTrue = 1
                  break
                }
              }
              if (!isTrue) {
                //没匹配到，默认第一个
                arr[0].isDefault = true
                usersNow = arr[0]
              }
            } else {
              // 当前没学生，默认第一个
              let isTrue = 0
              for (const i of arr) {
                if (i.isDefault) {
                  //有默认时
                  isTrue = 1
                  usersNow = i
                  break
                }
              }
              if (!isTrue) {
                arr[0].isDefault = true
                usersNow = arr[0]
              }
            }
            // 缓存选中学科
            //缓存学生列表
            useUserStore().learnUsers = localStorage.learnUsers
            useUserStore().setlearnNow(usersNow)
            //跳首页
            router.replace({ name: "KnowledgeGraph" })
          } else {
            //跳新增学习用户
            router.push({ name: "UserAdd", query: { pageType: 'add' } })
          }
        })
          .catch(() => {
            loading.value = false
          })
      }
    }
  }
  // 账号登录e

  // 短信登录s
  const form2Ref = ref<FormInstance | any>()
  const param2 = reactive({
    phone: "",
    code: "",
    sendtxt: "发送验证码",
    disabled: 0,
    second: 60,
    sendLoad: false
  })
  let interval : any = null
  const rules2 : FormRules = {
    phone: [
      { required: true, message: "请输入您的手机号", trigger: "blur" },
      { min: 11, max: 11, message: "请输入正确的手机号", trigger: "blur" }
    ],
    code: [{ required: true, trigger: "blur", message: "请输入短信验证码" }]
  }

  //输入数字
  const setNumber2 = (val : string) => {
    val = val.replace(/[^\d]/g, "")
    param2.phone = val
  }
  //输入数字
  const setNumber3 = (val : string) => {
    val = val.replace(/[^\d]/g, "")
    param2.code = val
  }
  //获取验证码
  const sendMsg = () => {
    const phone = param2.phone
    param2.second = 60
    ElMessage.closeAll()
    if (phone == "") {
      ElMessage.error("请输入您的手机号")
      return
    }
    if (phone.length != 11) {
      ElMessage.error("手机号格式不对")
      return
    }
    const data = {
      phone: phone,
      sendType: 2 //1=注册 2=账号登陆 3=找回密码4=注销账号
    }
    param2.sendLoad = true
    sendCodeApi(data)
      .then(() => {
        param2.sendLoad = false
        param2.disabled = 1
        ElMessage.success("验证码已发送")
        param2.sendtxt = param2.second + "S"
        interval = setInterval(remainTime, 1000)
      })
      .catch(() => {
        param2.sendLoad = false
      })
  }
  //倒计时
  const remainTime = () => {
    if (param2.second == 0) {
      clearInterval(interval) //停止计时器
      param2.disabled = 0
      param2.sendtxt = "重发验证码"
    } else {
      param2.second--
      param2.sendtxt = param2.second + "S"
    }
  }
  // 验证码登录
  const msgLogin = () => {
    form2Ref.value?.validate((valid : boolean) => {
      if (valid) {
        if (!isAgree.value) {
          ElMessage.error("请先阅读和勾选同意《平台用户协议》和《隐私政策》")
          return
        }
        loading.value = true
        const data = {
          code: param2.code,
          phone: param2.phone,
          type: 2 //登录类型 1账号密码 2短信
        }
        pwdLoginApi(data)
          .then((res) => {
            loading.value = false
            userGetAll(res)
          })
          .catch(() => {
            loading.value = false
          })
      }
    })
  }
  // 短信登录e

  // 注册s
  const step = ref(1)
  const setStep = (num : any) => {
    step.value = num
  }
  const form3Ref = ref<FormInstance | any>()
  const param3 = reactive({
    phone: "",
    code: "",
    sendtxt: "发送验证码",
    disabled: 0,
    second: 0,
    sendLoad: false
  })
  let interval2 : any = null
  const rules3 : FormRules = {
    phone: [
      { required: true, message: "请输入您的手机号", trigger: "blur" },
      { min: 11, max: 11, message: "请输入正确的手机号", trigger: "blur" }
    ],
    code: [{ required: true, trigger: "blur", message: "请输入短信验证码" }]
  }
  const form4Ref = ref<FormInstance | any>()
  const param4 = reactive({
    password: "",
    password2: "",
    flag: false
  })
  //自定义校验
  const equalToPassword = (rule : any, value : any, callback : any) => {
    if (param4.password !== param4.password2) {
      callback(new Error("两次输入的密码不一致"))
    } else {
      callback()
    }
  }
  // 自定义密码验证规则
  const passwordRules : any = (val : any) : any => {
    const hasNumber = /\d/.test(val); // 检查是否有数字
    const hasLetter = /[a-zA-Z]/.test(val); // 检查是否有英文字母
    console.log(123, hasNumber && hasLetter)
    return hasNumber && hasLetter; // 必须同时满足
  }
  const validatePassword : any = (rule : any, value : any, callbakck : any) => {
    if (passwordRules(value)) {
      param4.flag = false
      callbakck()
    } else {
      param4.flag = true
      callbakck(new Error("密码需大于6位，为数字、英文字母的组合"))
    }
  }
  const validatePassword2 : any = (rule : any, value : any, callbakck : any) => {
    if (passwordRules(value)) {
      callbakck()
    } else {
      callbakck(new Error("密码需大于6位，为数字、英文字母的组合"))
    }
  }
  const rules4 : FormRules = {
    password: [
      { required: true, message: "请输入密码", trigger: "blur" },
      { min: 6, max: 20, message: "密码长度在6到20个字符", trigger: "blur" },
      { validator: validatePassword }
    ],
    password2: [
      { required: true, message: "请输入确认密码", trigger: "blur" },
      { required: true, validator: equalToPassword, trigger: "blur" },
      { validator: validatePassword2 }
    ]
  }

  //输入数字
  const setNumber4 = (val : string) => {
    val = val.replace(/[^\d]/g, "")
    param3.phone = val
  }
  //输入数字
  const setNumber5 = (val : string) => {
    val = val.replace(/[^\d]/g, "")
    param3.code = val
  }
  //获取验证码
  const sendMsg2 = () => {
    const phone = param3.phone
    param3.second = 60
    ElMessage.closeAll()
    if (phone == "") {
      ElMessage.error("请输入您的手机号")
      return
    }
    if (phone.length != 11) {
      ElMessage.error("手机号格式不对")
      return
    }
    const data = {
      phone: phone,
      sendType: 1 //1=注册 2=账号登陆 3=找回密码4=注销账号
    }
    param3.sendLoad = true
    sendCodeApi(data)
      .then(() => {
        param3.sendLoad = false
        param3.disabled = 1
        ElMessage.success("验证码已发送")
        param3.sendtxt = param3.second + "S"
        interval2 = setInterval(remainTime2, 1000)
      })
      .catch(() => {
        param3.sendLoad = false
      })
  }
  //倒计时
  const remainTime2 = () => {
    if (param3.second == 0) {
      clearInterval(interval2) //停止计时器
      param3.disabled = 0
      param3.sendtxt = "重发验证码"
    } else {
      param3.second--
      param3.sendtxt = param3.second + "S"
    }
  }
  // 下一步
  const nextStep = () => {
    form3Ref.value?.validate((valid : boolean) => {
      if (valid) {
        if (!isAgree.value) {
          ElMessage.error("请先阅读和勾选同意《平台用户协议》和《隐私政策》")
          return
        }
        setStep(2)
      }
    })
  }
  // 注册
  const msgLogin2 = () => {
    form3Ref.value?.validate((valid : boolean) => {
      if (valid) {
        if (!isAgree.value) {
          ElMessage.error("请先阅读和勾选同意《平台用户协议》和《隐私政策》")
          return
        }
        form4Ref.value?.validate((valid : boolean) => {
          if (valid) {
            loading.value = true
            const param = {
              phone: param3.phone,
              code: param3.code,
              newPassword: param4.password,
              confirmPassword: param4.password2
            }
            registerApi(param)
              .then(() => {
                loading.value = false
                ElMessage.success("注册成功")
                //重置数据
                resetData()
                setTimeout(() => {
                  //返回登录页
                  setStep(1)
                  setOther(1)
                  tabs(1)
                }, 2000)
              })
              .catch(() => {
                loading.value = false
              })
          }
        })
      }
    })
  }
  //重置数据
  const resetData = () => {
    param3.phone = ""
    param3.code = ""
    param3.sendtxt = "发送验证码"
    param3.disabled = 0
    param3.second = 0
    param3.sendLoad = false
    param4.password = ""
    param4.password2 = ""
    param4.flag = false
  }
  // 注册e
</script>

<style lang="scss" scoped>
  @import url('@/assets/styles/reset.css');

  .none {
    display: none !important;
  }

  /* 隐藏滚动条 */
  ::-webkit-scrollbar {
    width: 0;
    height: 0;
    color: transparent;
    display: none;
  }

  .content {
    width: 100%;
    height: 100vh;
    background: url(@/assets/img/login/bg.png) no-repeat;
    background-size: cover;
    background-attachment: fixed;
    overflow-y: auto;
  }

  .inner {
    margin: 0 auto;
    width: 68.625rem;
    height: 100%;
  }

  .inner div {
    float: left;
  }

  .header {
    width: 100%;
    margin: 1.875rem 0 2.5rem;
  }

  .logo_img {
    float: left;
    width: 5.625rem;
    height: 5.625rem;
  }

  .logo_h1 {
    line-height: 3.5rem;
    color: #009c7f;
    font-size: 2.5rem;
    font-weight: 700;
    margin: 2.1875rem .5rem 0 1.25rem;
  }

  .logo_h2 {
    line-height: 1.625rem;
    color: #f3943c;
    font-size: 1.25rem;
    font-weight: 700;
    margin: 3.625rem 0 0;
  }

  div.blank {
    float: right;
    margin: 3.125rem 0 0;
  }

  .blank:hover {
    cursor: pointer;
  }

  .blank_img1 {
    width: 2.5rem;
    height: 2.5rem;
    margin: 0 .625rem 0 0;
  }

  .blank_img2 {
    width: 10.5rem;
    height: 2.5rem;
  }

  .wrap {
    width: 68.625rem;
    height: 43.75rem;
    border-radius: 1.875rem;
    background: #ffffff;
    box-shadow: 0 .25rem 1.875rem 0 #a7d1c9;
  }

  /* 扫码登录 */
  .wechat {
    width: 100%;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    flex-flow: column;
  }

  .we_h1 {
    height: 2.5rem;
    line-height: 2.5rem;
    opacity: 1;
    color: #2a2b2a;
    font-size: 1.875rem;
    font-weight: 700;
    margin: 1.5625rem 0 1.25rem;
  }

  .we_h1 img {
    float: left;
    width: 2.4375rem;
    height: 2.0625rem;
    margin: .375rem .625rem 0;
  }

  .we_tip {
    line-height: 1.9375rem;
    color: #999999;
    font-size: 1.5rem;
    margin: 0 0 1.875rem;
  }

  .we_qrcode {
    width: 23.625rem;
    height: 23.625rem;
    border-radius: 4.375rem;
    border: .0625rem solid #ececec;
    background: #ffffff;
    box-shadow: 0 0 5.625rem 0 #0000001a;
    box-sizing: border-box;
    overflow: hidden;
  }

  .we_qrcode img {
    width: 20.125rem;
    height: 20.125rem;
    margin: 1.75rem;
  }

  .we_success {
    width: 23.625rem;
    height: 23.625rem;
    border-radius: 4.375rem;
    background: rgba(0, 0, 0, 0.5);
    margin: -23.625rem 0 0;
    position: relative;
    z-index: 2;
    color: #ffffff;
    font-size: 1.875rem;
    font-weight: 700;
    text-align: center;
    line-height: 23.625rem;
  }

  .other {
    width: 11.1875rem;
    height: 2.75rem;
    border-radius: .625rem;
    border: .0625rem solid #d3d3d3;
    box-sizing: border-box;
    color: #2a2b2a;
    font-size: 1.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fefefe;
    margin: 1.875rem 0 2.5rem;
  }

  .other:hover {
    cursor: pointer;
  }

  .other img {
    width: 1.1875rem;
    height: 1.4375rem;
    margin: 0 .625rem 0 0;
  }

  .agent {
    width: 61.125rem;
    border-top: .0625rem solid #ededed;
    color: #999999;
    font-size: 1.25rem;
    padding: 1.25rem 0 0;
    text-align: center;
    margin: auto 3.75rem 0;
  }

  .agent:hover {
    cursor: pointer;
  }

  .link {
    color: #009C7F;
  }

  /* tab */
  .tabs {
    width: 33.75rem;
    margin: 2.1875rem 17.4375rem 0;
    height: 2.25rem;
    border-bottom: .125rem solid #EAEAEA;
    box-sizing: border-box;
  }

  .tab {
    // width: 11.25rem;
    width: 16.875rem;
    text-align: center;
    line-height: 1.625rem;
    color: #2a2b2a;
    font-size: 1.25rem;
    padding: 0 0 .625rem;
    display: inline-block;
    position: relative;
    top: -0.125rem;
  }

  .tab:hover {
    cursor: pointer;
  }

  .tab.active {
    font-weight: 700;
    border-bottom: .125rem solid #00C9A3;
  }

  /* 密码登录 */
  .another {
    width: 100%;
  }

  .form {
    height: 35.3125rem;
  }

  .tel,
  .pwd,
  .msg {
    float: left;
    width: 28.875rem;
    margin: 2.5rem 19.875rem 1.875rem;
    line-height: 1.3125rem;
    color: #2a2b2a;
    font-size: 1rem;
  }

  .pwd2 {
    margin-top: 0;
  }

  .pwd,
  .msg {
    margin: 0 19.875rem 1.25rem;
  }

  .label {
    color: #2a2b2a;
    font-size: 1rem;
    width: 100%;
  }

  .input {
    width: 100%;
    height: 3.3125rem;
    border-radius: .375rem;
    background: #f5f5f6;
    display: flex;
    align-items: center;
    border: .0625rem solid #F5F5F6;
  }

  .input :deep(.el-input__wrapper) {
    background: none;
    box-shadow: none;
    height: 3.3125rem;
    font-size: 1rem;
  }

  .input.active {
    border: .0625rem solid #00C9A3;
  }

  .input img,
  .tel_img {
    float: left;
    width: 1.125rem;
    height: 1.125rem;
    margin: -0.125rem .625rem -3.25rem .875rem;
    position: relative;
    z-index: 9;
  }

  .input input,
  .el_txt {
    width: 100%;
    height: 1.3125rem;
    line-height: 1.3125rem;
    background: none;
    color: #2A2B2A;
    font-size: 1rem;
  }

  ::-webkit-input-placeholder {
    color: #999;
  }

  .passbox {
    width: 28.875rem;
    margin: 0 19.875rem;
  }

  .remember {
    color: #2a2b2a;
    font-size: 1rem;
    height: 1.3125rem;
    display: flex;
    align-items: center;
  }

  .remember:hover {
    cursor: pointer;
  }

  .remember .agree img {
    width: 1rem;
    height: 1rem;
    margin: -0.1875rem .375rem 0;
  }

  div.forget {
    float: right;
    line-height: 1.3125rem;
    color: #009c7f;
    font-size: 1rem;
  }

  .forget:hover {
    cursor: pointer;
  }

  .submit {
    width: 15.25rem;
    text-align: center;
    line-height: 3.5625rem;
    border-radius: 2.1563rem;
    background: #00C9A3;
    box-shadow: 0 .25rem .9375rem 0 #00000040;
    color: #ffffff;
    font-size: 1.25rem;
    font-weight: 700;
    margin: 2.4375rem 26.6875rem 4.625rem;
  }

  .submit.disbled {
    background: #bebebe;
    pointer-events: none;
  }

  .another .other {
    margin: 0 28.6875rem 2.5rem;
  }

  .agree {
    display: inline-block;
    position: relative;
    top: .125rem;
    left: -0.375rem;
  }

  .submit:hover,
  .agree:hover {
    cursor: pointer;
  }

  .agree img {
    width: 1.25rem;
    height: 1.25rem;
  }

  .agree img:nth-child(1),
  .checked.agree img:nth-child(2) {
    display: inline-block;
  }

  .agree img:nth-child(2),
  .checked.agree img:nth-child(1) {
    display: none;
  }

  /* 验证码登录 */
  .code {
    width: 7.5rem;
    height: 1.5625rem;
    line-height: 1.5625rem;
    text-align: center;
    border: none;
    border-left: .0625rem solid #a5a5a5 !important;
    color: #009c7f !important;
    font-size: 1rem;
    margin: -3.25rem 0 0 auto;
    position: relative;
    left: -0.125rem;
    z-index: 20;
    background: #f5f5f6 !important;
    box-sizing: border-box;
  }

  .code:hover,
  .code:active,
  .code:focus {
    border: none !important;
    border-left: .0625rem solid #a5a5a5 !important;
    color: #009c7f !important;
    cursor: pointer;
    background: #f5f5f6 !important;
  }

  .code.disabled,
  .is-disabled.code {
    pointer-events: none;
    cursor: not-allowed;
    opacity: .5;
    color: #999 !important;
  }

  /* 注册 */
  .back {
    width: 5rem;
    text-align: center;
    line-height: 1.8125rem;
    color: #009C7F;
    font-size: 1.25rem;
    margin: -2.125rem 31.8125rem 0;
  }

  .back:hover {
    cursor: pointer;
  }

  /* 忘记密码 */
  .form2 .tel {
    margin-bottom: 1.25rem;
  }

  .form2 .submit {
    margin-top: 1.0625rem;
  }
</style>
