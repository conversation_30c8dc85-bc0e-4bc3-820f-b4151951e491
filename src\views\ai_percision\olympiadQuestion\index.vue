<!-- 我的积分 -->
<template>
    <div class="content">
        <div class="header">
            <div class="header-img">
                <img src="@/assets/img/percision/back_o.png" alt="back" @click="goBack"></img>
                <img src="@/assets/img/percision/olympiad_text.png" alt="text"></img>
            </div>
            <div class="header-text">
                <template v-if="learnNow.gradeId < 10">{{ subjectObj.subjectName }}{{ subjectObj.editionName }}{{ learnNow.gradeName }}{{ subjectObj.termName }}</template>
                <template v-else>{{ subjectObj.subjectName }}{{ subjectObj.editionName }}{{ subjectObj.typeName }}</template>
            </div>
        </div>
        <div style="display: flex">
            <div class="left" v-loading="loadingTree">
                <el-tree
                    ref="knowledgeTreeRef"
                    class="custom-tree"
                    node-key="id"
                    :props="defaultProps"
                    :data="treeData"
                    @node-click="setPointId"
                    :default-expand-all = "true"
                    >

                    <template #default="{ node, data }">
                        <div v-if="!node.isLeaf" class="custom-node" :class="node.expanded?'border-left':''">
                            <span class="tree-h1" >{{ node.label }}</span>
                            <el-icon class="expand-icon">
                                <ArrowDown v-if="node.expanded"></ArrowDown>
                                <ArrowUp v-else></ArrowUp>
                            </el-icon>
                        </div>
                        <div v-else class="custom-node isLeaf">
                            <span>{{ node.label }}</span>
                        </div>
                    </template>
                </el-tree>
            </div>
            <div class="main" v-loading="loading">
                <img class="record-sty" @click="goRecord" src="@/assets/img/percision/record.png" alt="record"></img>
                <div class="main-box">
                    <div class="main-box-flex" v-if="options.length > 0">
                        <img class="way-start" src="@/assets/img/percision/final_start.png" alt="start"></img>
                        <div v-for="(item, index) in options">
                            <div v-if="index == 0" class="way-box">
                                <img class="way-sty" src="@/assets/img/percision/way1.png" alt=""></img>
                                <div class="island-box position-right">
                                    <img class="way-sty" :src="getIsland(index, item.status3)" alt=""></img>
                                    <img v-if="item.status3 == 1" class="way-status" src="@/assets/img/percision/olympiad_success.png" alt=""></img>
                                    <img v-if="item.status3 == 2" class="way-status" src="@/assets/img/percision/final_fail.png" alt=""></img>
                                    <div v-if="item.status3 !== 0" class="way-rate-box">
                                        <img src="@/assets/img/percision/report.png" alt=""></img>
                                        正确率：<span class="way-rate">{{ item.correctRate?.endsWith('.00')?parseInt(item.correctRate):item.correctRate }}</span>%
                                    </div>
                                    <div class="way-point-box">
                                        <div class="way-point-box-cont">
                                            {{ index + 1 }}. {{ item.name }}
                                        </div>
                                        <div v-if="item.status3 == 0" class="start-btn-box">
                                            <img @click="reChallenge(item)" src="@/assets/img/percision/start-btn.png" alt=""></img>
                                        </div>
                                        <div v-if="item.status3 == 2" class="way-point-box-btn">
                                            <div @click="reChallenge(item)">再次闯关</div>
                                            <div @click="goLearning(item)"><img src="@/assets/img/percision/play.png" alt=""></img>去学习</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else-if="index % 2 !== 0" class="way-box">
                                <img class="way-sty" src="@/assets/img/percision/way2.png" alt=""></img>
                                <div class="island-box position-left top50">
                                    <img class="way-sty" :src="getIsland(index, item.status3)" alt=""></img>
                                    <img v-if="item.status3 == 1" class="way-status" src="@/assets/img/percision/olympiad_success.png" alt=""></img>
                                    <img v-if="item.status3 == 2" class="way-status" src="@/assets/img/percision/final_fail.png" alt=""></img>
                                    <img v-if="showChain(item.status3, index)" class="way-status-chain" src="@/assets/img/percision/chain.png" alt=""></img>
                                    <div v-if="item.status3 !== 0" class="way-rate-box text-right">
                                        <img src="@/assets/img/percision/report.png" alt=""></img>
                                        正确率：<span class="way-rate">{{ item.correctRate?.endsWith('.00')?parseInt(item.correctRate):item.correctRate }}</span>%
                                    </div>
                                    <div class="way-point-box">
                                        <div class="way-point-box-cont">
                                            {{ index + 1 }}. {{ item.name }}
                                        </div>
                                        <div v-if="showStart(item.status3, index)" class="start-btn-box">
                                            <img @click="reChallenge(item)" src="@/assets/img/percision/start-btn.png" alt=""></img>
                                        </div>
                                        <div v-if="item.status3 == 2" class="way-point-box-btn">
                                            <div @click="reChallenge(item)">再次闯关</div>
                                            <div @click="goLearning(item)"><img src="@/assets/img/percision/play.png" alt=""></img>去学习</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div v-else-if="index % 2 === 0" class="way-box">
                                <img class="way-sty" src="@/assets/img/percision/way3.png" alt=""></img>
                                <div class="island-box position-right top50">
                                    <img class="way-sty" :src="getIsland(index, item.status3)" alt=""></img>
                                    <img v-if="item.status3 == 1" class="way-status" src="@/assets/img/percision/olympiad_success.png" alt=""></img>
                                    <img v-if="item.status3 == 2" class="way-status" src="@/assets/img/percision/final_fail.png" alt=""></img>
                                    <img v-if="showChain(item.status3, index)" class="way-status-chain" src="@/assets/img/percision/chain.png" alt=""></img>
                                    <div v-if="item.status3 !== 0" class="way-rate-box">
                                        <img src="@/assets/img/percision/report.png" alt=""></img>
                                        正确率：<span class="way-rate">{{ item.correctRate?.endsWith('.00')?parseInt(item.correctRate):item.correctRate }}</span>%
                                    </div>
                                    <div class="way-point-box">
                                        <div class="way-point-box-cont">
                                        {{ index + 1 }}. {{ item.name }}
                                        </div>
                                        <div v-if="showStart(item.status3, index)" class="start-btn-box">
                                            <img @click="reChallenge(item)" src="@/assets/img/percision/start-btn.png" alt=""></img>
                                        </div>
                                        <div v-if="item.status3 == 2" class="way-point-box-btn">
                                            <div @click="reChallenge(item)">再次闯关</div>
                                            <div @click="goLearning(item)"><img src="@/assets/img/percision/play.png" alt=""></img>去学习</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="nodata" v-else>
                        <img src="@/assets/img/user/nodata.png" />暂无数据
                    </div>
                </div>
            </div>
        </div>
    </div>
    <PassGifDialog ref="passGifDialog" :isFinal="false" />
</template>
  
<script lang="ts" setup>
import router from '@/router'
import { dataEncrypt } from '@/utils/secrets'
import PassGifDialog from '@/views/ai_percision/finalQuestion/components/pass_gif_dialog.vue'
import { ArrowDown, ArrowUp } from '@element-plus/icons-vue'
import { getImprovementPointList } from "@/api/video"
import { sonPointListApi, getStudyIdApi } from "@/api/point"
import { nextTick, onMounted, ref } from 'vue'
import { storeToRefs } from 'pinia'
import { useUserStore } from "@/store/modules/user"
const userStore = useUserStore()
const { subjectObj, learnNow } = storeToRefs(userStore)
const passGifDialog = ref()
const knowledgeTreeRef = ref() // 添加ref用于访问知识树组件
const options =  ref([{id:0}] as any[])
const treeData =  ref([] as any[])
const defaultProps = {
	value: 'id',
    label: 'name',
    children: 'children'
}
const loading = ref(false)
const loadingTree = ref(false)
const pointId = ref()

onMounted(async() => {
    loadingTree.value = true
    loading.value = true
    await getPointTree()
    await getStudyId()
    await getPointlist()
})
const getPointTree = async () => {
    try {
        const res: any = await getImprovementPointList({
            subject: subjectObj.value.id,
            hierarchy:2
        })
        if(res.code == 200) {
            treeData.value = res.data || []
            pointId.value = res.data[0]?.children[0]?.id
            nextTick(() => {
                knowledgeTreeRef.value.setCurrentKey(pointId.value, true)
            })
    
        }
        loadingTree.value = false
    } catch (error) {
        loadingTree.value = false
        loading.value = false
    }
}
const getStudyId = async () => {
    try {
        const res: any = await getStudyIdApi({
            subject: subjectObj.value.id,
            type: 6
        })
        if(res.code == 200) {
            if (res.data == '0') {
                pointId.value = treeData.value[0]?.children[0]?.id
            } else {
                pointId.value = res.data
            }
            nextTick(() => {
                knowledgeTreeRef.value.setCurrentKey(pointId.value, true)
            })
    
        }
        loadingTree.value = false
    } catch (error) {
        loadingTree.value = false
        loading.value = false
    }
}
const getPointlist = async() => {
    try {
        const res: any = await sonPointListApi({
            pointId: pointId.value,
            type: 4
        })
        if(res.code == 200) {
        options.value = res.data || []
        if (res.data[res.data.length - 1].status3 == 1) {
            passGifDialog.value.init()
        }
        }
        loading.value = false
    } catch (error) {
        loading.value = false
    }
}
const showChain = (status3: number, index: number) => {
    if (status3 == 0) {
        if (options.value[index - 1].status3 == 1) {
            return false
        } else {
            return true
        }
    } else{
        return false
    }
}
const showStart = (status3: number, index: number) => {
    if (status3 == 0) {
        if (options.value[index - 1].status3 == 1) {
            return true
        } else {
            return false
        }
    } else{
        return false
    }
}
const goBack = () => {
    router.push({
        path: '/ai_percision/knowledge_graph'
    })
}
const setPointId = (nodeData: any) => {
    if (nodeData.children.length > 0) return
    pointId.value = nodeData.id
    getPointlist()
}
const getIsland = (index: number, status3: number) => {
    const sign = index % 4
    const lastStaus = options.value[index - 1]?options.value[index - 1].status3: null
    let img = ""
    if (status3 == 1 || (lastStaus == 1 && status3 == 0) || (lastStaus == null && status3 == 0)) {
        switch (sign) {
            case 0:
                img = "isplanet1.png"
                break
            case 1:
                img = "isplanet2.png"
                break
            case 2:
                img = "isplanet3.png"
                break
            case 3:
                img = "isplanet4.png"
                break
        }
    } else {
        switch (sign) {
            case 0:
                img = "isplanet1_grey.png"
                break
            case 1:
                img = "isplanet2_grey.png"
                break
            case 2:
                img = "isplanet3_grey.png"
                break
            case 3:
                img = "isplanet4_grey.png"
                break
        }
    }
    return new URL(`../../../assets/img/percision/${img}`, import.meta.url).href //静态资源引入为url，相当于require()

}
const reChallenge = (data: any) => { 
    router.push({
        path: '/ai_percision/olympiad_question/olympiad_question_write',
        query: {
            data: dataEncrypt({
                pointId: data.id,
                parentPointId: pointId.value,
                pageSource: '12'
            })
        }
    })
}
const goRecord = (data: any) => { 
    router.push({
        path: '/ai_percision/olympiad_question/olympiad__record',
        query: {
            data: dataEncrypt({
                parentPointId: pointId.value,
                pageSource: '12'
            })
        }
    })
}

const goLearning = (data: any) => {
    router.push({
        path: '/ai_percision/olympiad_question/olympiad__learning',
        // query: {
        //     data: dataEncrypt({
        //         id: data.id,
            // parentPointId: pointId.value,
            //         pointName: data.name,
            // pageSource: '12',
            //         source: 'analysis',
        //         subject: subjectObj.value.id
        //     })
        // }
        query: {
            id: data.id,
            parentPointId: pointId.value,
            pointName: data.name,
            pageSource: '12',
            source: 'analysis',
            subject: subjectObj.value.id
        }
    })
}
</script>
  
<style lang="scss" scoped>
.content{
    width: 100%;
    height: calc(100vh - 4.375rem);
    background: url(@/assets/img/percision/olympiadbg.png) no-repeat;
    background-size: 100% calc(100vh - 4.375rem);
    box-sizing: border-box;
    display: flex;
    align-items: center;
    flex-direction: column;
    .header {
        width: 81.25rem;
        height: 5.8125rem;
        display: flex;
        padding-top: 1.875rem;
        box-sizing: border-box;
        justify-content: space-between;
        &-img {
            display: flex;
            align-items: center;
            img:first-child {
                width: 2.75rem;
                height: 2rem;
                margin-right: 1rem;
                cursor: pointer;
            }
            img:last-child {
                width: 12.8125rem;
                height: 3.3125rem;
            }
        }
        &-text {
            height: fit-content;
            border-radius: 1.375rem;
            background: #0000004d;
            color: #ffffff;
            font-size: 1rem;
            font-weight: 400;
            padding: .6875rem 1.25rem;
        }
    }
    .left {
        width: 23rem;
        height: calc(100vh - 10.1875rem);
        background: #ffffff;
        box-sizing: border-box;
        margin-right: .625rem;
        padding: .875rem;
        overflow-y: auto;
    }
    .main {
        width: 57.625rem;
        height: calc(100vh - 10.1875rem);
        background: #ffffff;
        padding: 1.25rem 1.25rem 0 1.25rem;
        box-sizing: border-box;
        position: relative;
        .record-sty {
            position: absolute;
            bottom: 3.125rem;
            right: 4.375rem;
            width: 5.4375rem;
            height: 5rem;
            cursor: pointer;
            z-index: 100;
        }
        &-box {
            height: calc(100vh - 11.4375rem);
            width: 100%;
            background: url(@/assets/img/percision/olympiadbg2.png) no-repeat;
            background-size: 100% 100%;
            display: flex;
            overflow-y: auto;
            justify-content: center;
            .main-box-flex {
                width: 42.75rem;
                margin-top: .625rem;
                padding: 0 4.75rem;
                box-sizing: border-box;
            }
            .way-start {
                width: 6.0625rem;
                height: 4.0625rem;
                margin-left: 1.625rem;
                margin-bottom: -0.3125rem;
            }
            .way-box {
                position: relative;
                .way-sty {
                    width: 33.375rem;
                }
                .island-box {
                    position: absolute;
                    top: 0;
                    .way-sty {
                        width: 11.875rem;
                        height: 11.875rem;
                        position: relative;
                        z-index: 10;
                    }
                    .way-status {
                        position: absolute;
                        top: -2.375rem;
                        left: 0;
                        width: 11.875rem;
                        height: 11.875rem;
                        z-index: 11;
                    }
                    .way-status-chain {
                        position: absolute;
                        width: 10.625rem;
                        left: .6875rem;
                        top: 3.75rem;
                        z-index: 49;
                    }
                    .way-rate-box {
                        position: absolute;
                        z-index: 1;
                        width: 10.625rem;
                        top: 5.625rem;
                        right: 9.375rem;
                        background-color: #ffffff;
                        padding: .375rem .75rem;
                        font-size: .875rem;
                        border-radius: 1rem;
                        display: flex;
                        align-items: center;
                        span {
                            font-weight: 700;
                        }
                        img {
                            width: 1rem;
                            height: 1rem;
                            margin-right: .3125rem;
                        }
                    }
                    .text-right {
                        left: 9.375rem;
                        width: 8.125rem;
                        padding-left: 3.75rem;
                    }
                    .way-point-box {
                        position: absolute;
                        top: 8.9375rem;
                        z-index: 20;
                        &-cont {
                            padding: .5rem .75rem;
                            width: 12.5rem;
                            text-align: center;
                            box-sizing: border-box;
                            min-height: 1.875rem;
                            border-radius: .625rem;
                            border: .125rem solid #5a85ec;
                            background: #ffffffcc;
                            color: #323a57;
                        }
                        &-btn {
                            display: flex;
                            justify-content: space-between;
                            margin-top: .375rem;
                            div {
                                display: flex;
                                align-items: center;
                                justify-content: center;
                                width: 5.625rem;
                                cursor: pointer;
                                height: 1.8125rem;
                                border-radius: .9063rem;
                                border: .125rem solid #f25500;
                                background: #e98b00;
                                color: #ffffff;
                                font-size: .875rem;
                                img {
                                    width: 1rem;
                                    height: 1rem;
                                    margin-right: .3125rem;
                                }
                            }
                        }
                    }
                }
                .position-right {
                    right: -3.75rem;
                }
                .position-left {
                    left: -3.75rem;
                }
                .top50 {
                    top: 3.625rem;
                }
            }
        }
    }
}

.custom-tree {
  color: #666666;
  font-size: 1rem;
  .title-h1 {
    color: #2a2b2a;
    background: #f5f5f5;
  }
  .border-left {
    border-left: .1875rem solid #00C9A3;
  }
  /* 隐藏默认图标 */
  :deep(.el-tree-node__expand-icon) {
    display: none;
  }
  :deep(.el-tree-node) {
    width: 100%;
    margin: .5rem 0;  /* 增加节点间距 */
    position: relative;

    & > .el-tree-node__content {
      height: 2.5625rem;
      line-height: 2.5625rem;
    }
  }
  /* 自定义节点布局 */
  .custom-node {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0 .4375rem!important;
    box-sizing: border-box;
    span {
      display: inline-block;
      width: calc(100% - 5.3125rem);
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  /* 右侧展开图标 */
  .expand-icon {
    margin-left: .5rem;
    font-size: .875rem;
    color: #666;
  }
}
:deep(.el-tree-node__children) {
    .is-current {
      .el-tree-node__content {
        background: #e5f9f6!important;
      }
    }
}
.start-btn-box {
    width: 12.5rem;
    display: flex;
    justify-content: center;
    margin-top: .375rem;
    img {
        width: 7.125rem;
        height: 2.625rem;
        cursor: pointer;
    }
}
.nodata {
    flex: 1;
    color: #999999;
    font-size: .875rem;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-flow: column;
}
</style>
  