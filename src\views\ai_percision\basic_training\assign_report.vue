<template>
    <div class="container" v-loading="writeState.loading">
    <div style="width: 1300px;margin: 0 auto;">
            <div class="top-nav">
                <img class="exit-btn"  @click="goBack" src="@/assets/img/percision/training/exit.png" alt="">
                <!-- 🎯 新增：答题进度条 -->
                <div class="progress-container">
                    <div class="progress-bg"></div>
                    <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
                    <img class="progress-icon" :style="{ left: progressPercentage + '%' }" src="@/assets/img/percision/training/move.gif" alt="进度">
                </div>
                <!-- 🎯 新增：测试报告弹窗按钮 -->
                <div class="test-report-btn">
                    <el-button type="primary" size="small" @click="testReportDialog">测试报告弹窗</el-button>
                </div>
            </div>
    <div class="oll" style="display: flex;width: 100%;border-radius: 0 20px 20px 0;">
      <div class="left">
        <div class="test-content">
                        
            <!-- 🎯 一题一页显示模式 -->
            <div v-if="allTest.length > 0" class="single-question-container">
                <div class="question-cont"> 
                    <div class="question-header">
                        <span class="question-type">{{ allTest[writeState.current]?.ques?.cateName || '题目类型' }}</span>     
                    </div>
                    <div style="display: flex;">
                        <div class="question-number">{{ writeState.current + 1 }}.</div>
                        <div class="question-content" v-html="filterContent(allTest[writeState.current]?.ques?.content || '')" />
                    </div>
                    
                    <div class="question-options" v-if="allTest[writeState.current]?.ques?.options && allTest[writeState.current].ques.options.length > 0">
                        <div v-for="(option, optIndex) in allTest[writeState.current].ques.options" :key="optIndex" class="option-item">
                            <span class="option-label">{{ String.fromCharCode(65 + optIndex) }}.</span>
                            <div class="option-content" v-html="option" />
                        </div>
                    </div>

                    <div >
                        <div class="show-analyse">
                            <el-switch size="small"  @change="togAnswer(allTest[writeState.current],allTest[writeState.current].showAnalyse)"  v-model="allTest[writeState.current].showAnalyse" /> <span>显示答案与解析</span>
                        </div>
                        <div v-show="allTest[writeState.current].showAnalyse" class="analyse">
                            <div class="flex-sty">
                                <span>【知识点】</span>&nbsp;&nbsp;
                                <div v-if="allTest[writeState.current].ques.pointVos != null" v-html="allTest[writeState.current].ques.pointVos[0]?.name" />
                                <div v-else>--</div>
                            </div>
                            <div class="flex-sty">
                                <span>【答案】</span>&nbsp;&nbsp;
                                <div v-html="allTest[writeState.current].ques.displayAnswer" />
                            </div>
                            <div class="flex-sty">
                                <span>【分析】</span>&nbsp;&nbsp;
                                <div v-html="allTest[writeState.current].ques.analyse" />
                            </div>
                            <div class="flex-sty">
                                <span>【解答】</span>&nbsp;&nbsp;
                                <div v-html="allTest[writeState.current].ques.method" />
                            </div>
                            <div class="flex-sty">
                                <span>【点评】</span>&nbsp;&nbsp;
                                <div v-html="allTest[writeState.current].ques.discuss" />
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 🎯 答题区域 -->
                <div class="answer-section">
                    <!-- 选择题答题区域 -->
                    <div v-if="allTest[writeState.current]?.ques?.cate == 1 || allTest[writeState.current]?.ques?.cate == 3" class="choice-answer-area">
                        <div class="answer-options">
                            <div 
                                v-for="(option, optIndex) in allTest[writeState.current].ques.options" 
                                :key="optIndex" 
                                class="answer-option"
                                :class="{ 
                                    'selected': allTest[writeState.current].ques.userJson && allTest[writeState.current].ques.userJson.includes(optIndex),
                                    'disabled': allTest[writeState.current].submitted === true || allTest[writeState.current].userMark !== null
                                }"
                                @click="handleOptionClick(optIndex)"
                            >
                                <div class="option-checkbox">
                                    <i v-if="allTest[writeState.current].ques.userJson && allTest[writeState.current].ques.userJson.includes(optIndex)" class="checkmark">✓</i>
                                </div>
                                <span class="option-letter">{{ String.fromCharCode(65 + optIndex) }}</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 非选择题答题区域 -->
                    <div v-else class="non-choice-answer-area">
                        <div v-if="!writeState.showSubjective" class="normal-answer-mode">
                            <div class="paper-content-ques">
                                <!-- 已上传的图片显示 -->
                                <div v-show="!writeState.showCorrect" class="upload-images">
                                    <div 
                                        v-for="(it, ind) in allTest[writeState.current].userAnswer"
                                        :key="ind"
                                        class="upload-image-item"
                                    >
                                        <el-image
                                            class="answer-img"
                                            :src="it"
                                            :zoom-rate="1.2"
                                            :max-scale="7"
                                            :min-scale="0.2"
                                            :preview-src-list="allTest[writeState.current].userAnswer"
                                            show-progress
                                            :initial-index="ind"
                                            fit="cover"
                                        />
                                    </div>
                                </div>
                                
                                <!-- 上传组件 -->
                                <uploadAnswerImg v-if="writeState.showCorrect" :imgList="allTest[writeState.current].ques.userJson" :index="writeState.current" @getImgList="handleImgList" />
                            </div>
                        </div>
                    </div>
                </div>


                </div>
            </div>
            

                <!-- 注释：答题区域 -->
                <!-- <div class="answer-section">
                  
                    <div v-if="allTest[writeState.current].ques.cate == 1 || allTest[writeState.current].ques.cate == 3" class="choice-answer-area">

                        <div class="answer-options">
                            <div 
                                v-for="(option, optIndex) in allTest[writeState.current].ques.options" 
                                :key="optIndex" 
                                class="answer-option"
                                :class="{ 
                                    'selected': allTest[writeState.current].ques.userJson && allTest[writeState.current].ques.userJson.includes(optIndex),
                                    'disabled': allTest[writeState.current].submitted === true || allTest[writeState.current].userMark !== null
                                }"
                                @click="handleOptionClick(optIndex)"
                            >
                                <div class="option-checkbox">
                                    <i v-if="allTest[writeState.current].ques.userJson && allTest[writeState.current].ques.userJson.includes(optIndex)" class="checkmark">✓</i>
                                </div>
                                <span class="option-letter">{{ String.fromCharCode(65 + optIndex) }}</span>
                            </div>
                        </div>
                    </div>
                    
                    
                    <div v-else class="non-choice-answer-area">
                        
                        <div v-if="!writeState.showSubjective" class="normal-answer-mode">
                            <div class="paper-content-ques">
                               
                                <div v-show="!writeState.showCorrect" class="upload-images">
                                    <div 
                                        v-for="(it, ind) in allTest[writeState.current].userAnswer"
                                        :key="ind"
                                        class="upload-image-item"
                                    >
                                        <el-image
                                            class="answer-img"
                                            :src="it"
                                            :zoom-rate="1.2"
                                            :max-scale="7"
                                            :min-scale="0.2"
                                            :preview-src-list="allTest[writeState.current].userAnswer"
                                            show-progress
                                            :initial-index="ind"
                                            fit="cover"
                                        />
                                    </div>
                                </div>
                               
                                <uploadAnswerImg v-if="writeState.showCorrect" :imgList="allTest[writeState.current].ques.userJson" :index="writeState.current" @getImgList="handleImgList" />
                            </div>
                        </div>
                        
                    </div>
                    

                    <div v-if="writeState.showSubjective" class="subjective-correction-mode">
                        
                        <div class="student-answer-section">
                            
                            <div class="answer-img-box" v-if="allTest[writeState.current].userAnswer && allTest[writeState.current].userAnswer.length > 0">
                                <el-image
                                    class="answer-img"
                                    v-for="(it, ind) in allTest[writeState.current].userAnswer"
                                    :key="ind"
                                    :src="it"
                                    :zoom-rate="1.2"
                                    :max-scale="7"
                                    :min-scale="0.2"
                                    :preview-src-list="allTest[writeState.current].userAnswer"
                                    show-progress
                                    :initial-index="ind"
                                    fit="cover"
                                />
                            </div>
                        </div>
                        
                        
                        <div class="correction-section">
                            
                            <div class="answers">
                                <div 
                                    class="answer-box" 
                                    @click="correcthandle(writeState.current, 1)" 
                                    :class="allTest[writeState.current].userMark == 1 ? 'green-box selected' : ''"
                                >
                                    <div></div>正确
                                </div>
                                <div 
                                    class="answer-box" 
                                    @click="correcthandle(writeState.current, 2)" 
                                    :class="allTest[writeState.current].userMark == 2 ? 'yellow-box selected' : ''"
                                >
                                    <div></div>半对
                                </div>
                                <div 
                                    class="answer-box" 
                                    @click="correcthandle(writeState.current, 0)" 
                                    :class="allTest[writeState.current].userMark == 0 ? 'red-box selected' : ''"
                                >
                                    <div></div>错误
                                </div>
                            </div>
                        </div>
                    </div>
                </div> -->

                
                <div class="question-footer">
                    <div class="navigation-buttons">
                       
                        </div>
                                    </div>
            </div>
        </div>
      </div>
      <div class="right">
        <div class="time-box">
            <div class="time-text" > <img src="@/assets/img/percision/training/times.png" alt="">已用时间</div>
            <div style="display: flex; " class="time-bg">
                <div class="time-number"> {{ timeState.hours < 10 ? "0" + timeState.hours : timeState.hours }} </div> :
                <div class="time-number"> {{ timeState.minutes < 10 ? "0" + timeState.minutes : timeState.minutes }} </div> :
                <div class="time-number"> {{ timeState.seconds < 10 ? "0" + timeState.seconds : timeState.seconds }} </div>
            </div>    
        </div>
        <div class="test-number-box">
                        <div class="question-nav-title">
                            <img src="@/assets/img/percision/training/tihao.png" alt=""> 
                            <span>
                                题号 ( {{ allTest.length }} 题)
                            </span>
                        </div>
            <div class="question-nav-grid">
                <div 
                    v-for="(item, index) in allTest"
                    :key="index"
                    class="test-number-item" 
                    :class="[
                        setClass1(item, index)
                    ]"
                    @click="switchQuestion(index)"
                > 
                    {{ index + 1 }} 
                </div>
            </div>
            <div class="submit-section">
                <el-button type="primary" @click="showReportDialog" v-if="!dialogState.visible2">
                    查看详细报告
                </el-button>
                <el-button @click="goBack">
                    返回
                </el-button>
            </div>
                
                <!-- 🎯 新增：当所有题目都已作答时的提示 -->
                <!-- <div 
                    v-if="writeState.current !== null && allQuestionsAnswered" 
                    class="all-answered-tip"
                    style="font-size: 14px; color: #10b981; margin-top: 10px; text-align: center;"
                >
                    ✅ 所有题目已完成，可以点击完成按钮提交
                </div> -->
                
                <!-- 🎯 新增：最后一题但还有未作答题目的提示 -->
                <!-- <div 
                    v-if="writeState.current !== null && writeState.current === allTest.length - 1 && !allQuestionsAnswered" 
                    class="last-question-tip"
                    style="font-size: 14px; color: #f56565; margin-top: 10px; text-align: center;"
                >
                    ⚠️ 还有题目未作答，请先完成所有题目
                </div> -->
                
                <!-- 🎯 新增：调试信息显示 -->
                <!-- <div v-if="writeState.current !== null && allTest[writeState.current]" class="debug-info" style="font-size: 12px; color: #666; margin-top: 5px;">
                    当前题目: {{ writeState.current + 1 }}/{{ allTest.length }} | 
                    所有题目已作答: {{ allQuestionsAnswered ? '是' : '否' }} | 
                    当前题目已作答: {{ checkIfHasAnswer(allTest[writeState.current]) ? '是' : '否' }}
                </div> -->
                
            <!-- <div class="icon-btn size285" :class="writeState.disabled?'disabled':''" @click="submit" v-loading="writeState.btnloading">
                <img src="@/assets/img/percision/submit.png" alt="">
                提交批改
                </div> -->
            </div>
             <div class="color-type">
                 <div class="legend-item">
                     <div class="legend-square undone"></div>
                     <span>未做</span>
                 </div>
                 <div class="legend-item">
                     <div class="legend-square done"></div>
                     <span>已做</span>
                 </div>
                 <div class="legend-item">
                     <div class="legend-square correct"></div>
                     <span>正确</span>
                 </div>
                 <div class="legend-item">
                     <div class="legend-square half-correct"></div>
                     <span>半对</span>
                 </div>
                 <div class="legend-item">
                     <div class="legend-square wrong"></div>
                     <span>错误</span>
                 </div>
              </div>
            </div>
        </div>
      </div>
      </div>
    
    

    <!-- <coinAlert :show="writeState.jfShow" :num="writeState.jfNum" :isAjax="false" @close="jfHide">
    </coinAlert> -->
    
    <!-- 🎯 新增：报告弹窗 -->
    <el-dialog
        v-model="dialogState.visible2"
        :show-close="false"
        top="8vh"
        :class="{'weekness-dialog3': queryData.source === 'ripe'}"
        class="weekness-dialog2"
    >
        <div class="dialog-box">
            <!-- 统计信息居中显示 -->
            <div class="data-box" style="justify-content: center;">
                <div class="data-box-item">
                    <img src="@/assets/img/percision/answer-num.png" />
                    答题数：<span>{{ reportState.quesCount }}</span>
                </div>
               
                <div class="data-box-item">
                    <img src="@/assets/img/percision/report.png" />
                    正确率：<span>{{ reportState.correctRate }}%</span>
                </div>
                <div class="data-box-item">
                    <img src="@/assets/img/percision/jifen.png" />
                        <div class="time-text">答题时长: </div> {{secondsToHMS( reportState.trainTime) }}
                        <div class="time-number"> {{ timeState.hours }} </div> :
                        <div class="time-number"> {{ timeState.minutes }} </div> :
                        <div class="time-number"> {{ timeState.seconds }} </div>
                </div>
            </div>
            <div class=" item-list">
                <div class="list" v-for="(item,index) in pointItems" :key="index">
                    <div class="name">{{ item.pointName }}</div>
                    <div class="accuracy">正确率：<span style="color: rgba(42, 43, 42, 1);font-weight: 600;">{{ parseFloat((Number(item.correctRate)).toFixed(2))}} %</span> </div>
                    <img class="pass" v-if="item.passingStatus=='未过关'" src="@/assets/img/percision/training/wgg.png" alt="">
                    <img class="pass" v-else src="@/assets/img/percision/training/ywc.png" alt="">
                    
                    <div class="learn" @click="goLearning(item)">
                        <img src="@/assets/img/percision/training/bbfan.png" alt="">
                        <span>针对学习</span>
                    </div>
                    <div class="learns">
                        <span>当前等级:</span>
                        <span style="color: rgba(42, 43, 42, 1);font-weight: 600;">{{ getLevelInfo(item.level).name }}</span>
                        <img :src="getLevelInfo(item.level).image" alt="">    
                    </div>
                </div>
            </div>
            <div class="center-flex">
                <div class="handle-table">
                    <div class="first-row">
                        <div class="first-row-item blue-item"> 题号</div>
                        <div class="first-row-item" v-for="(ind, index) in quesItems" :key="index">{{ ind.quesNum }}</div>
                    </div>
                    <div class="second-row">
                        <div class="second-row-item blue-item"> 做题时长</div>
                        <div class="second-row-item" v-for="(ind, index) in quesItems" :key="index">{{ Number(ind.trainTime) / 1000}}s
                            <div v-if="ind.isTimeout" style="width: 48px;line-height: 26px;height: 26px;background: rgba(221, 42, 42, 1);color: #fff;border-radius: 4px;text-align: center;margin-left: 8px;font-size: 16px;">超时</div>
                        </div>
                    </div>
                    <div class="first-row">
                        <div class="second-row-item blue-item"> 答题情况</div>
                        <div class="second-row-item status-icon" 
                             v-for="(ind, index) in quesItems" 
                             :key="index"
                             :class="getAnswerStatusClass(ind.answeringStatus)">
                             {{ ind.answeringStatus }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <img class="close-icon" @click="dialogState.visible2 = false" src="@/assets/img/percision/dialog-close.png" />
        <template #footer>

        </template>
    </el-dialog>
</template>

<script lang="ts" setup>
import { watch, onMounted, reactive, ref, onUnmounted, nextTick, computed } from 'vue'
import uploadAnswerImg from '@/views/components/uploadAnswerImg/indexNew.vue'
import coinAlert from "@/views/components/coinAlert/index.vue"
import { dataDecrypt, dataEncrypt, mergeObject } from "@/utils/secret"
import { useRouter, useRoute } from 'vue-router'
import fiveStep from "@/views/components/fiveStep/index.vue"
import { quesGetApi} from "@/api/video"
import { Action, ElMessage, ElMessageBox, ElIcon, ElButton } from 'element-plus'
import { Loading, Document, SuccessFilled, CircleCheckFilled } from '@element-plus/icons-vue'
import { createTrainToAtlasApi, getDetailsApi, saveToAtlasApi, checkTrainQuesApi, getDetailssApi, saveUserJsonApi,saveToIntroduceddApi } from '@/api/training'
import {  savePointTrainingApi,saveTrainingApi,detailsToPointApi} from "@/api/precise"
import { fa } from 'element-plus/es/locale'
const route = useRoute()
const router = useRouter()
const timeState:any = reactive({
    hours: 0,
    minutes: 0,
    seconds: 0
})
const writeState:any = reactive({
    current: 0 as number | null,
    step: 1,
    btnloading: false,
    showCorrect: true,
    loading: false,
    showStep: false,
    disabled: true,
    trainingId: "",
    //积分
    jfShow: false,
    jfHide: true,
    jfNum: '0',
    jfSource: '0',
    itemTimer: 0, // 单题计时器
    lastTimestamp: 0, // 上次计时时间戳
    fromNextButton: false, // 标记是否从"下一题"按钮触发提交
    expectedNextIndex: undefined, // 期望的下一题索引
    showDialog:false,
    subjectiveNum: 0, // 主观题数量
    showSubjective: false, // 是否显示主观题批改模式
    isAllCorrect: false, // 是否全部批改完成
    unWriteNum: [] as number[]
})

// 🎯 新增：报告弹窗相关数据结构
const reportState = reactive({
    step: 1,
    switch: false,
    loading: false,
    correctRate: 0,
    quesCount: 0,
    percentage: 0,
    integral: 0,
    correct: 0,
    trainingId: "",
    trainTime: 0
})

const dialogState = reactive({
    visible: false,
    visible2: false,
    showGif: false
})

interface PointItem {
    pointName: string;
    passingStatus: string;
    level: number | string;
    correctRate: number;
    pointId?: string; // 知识点ID字段
    id?: string; // 备用ID字段
}

interface QuesItem {
    quesNum: number | string;
    trainTime: number | string;
    answeringStatus: number | string;
    isTimeout: boolean;
}

const pointItems = ref<PointItem[]>([])
const quesItems = ref<QuesItem[]>([])
const recordTime = ref()
interface Ques {
    cate: number;
    cateName: string;
    content: string;
    displayAnswer: string;
    analyse: string;
    method: string;
    discuss: string;
    options: any[];
    pointVos: any[];
    userJson: any[];
    answers: any[];
}

class AData {
    quesId: string = "";
    cate: number = 0;
    cateName: string = "";
    trainTime: string = "";
    userAnswer: string[] = [];
    userMark: number | null = null;
    userMarks: number | null = null;
    showAnalyse: boolean = false;
    content: string = "";
    ques: Ques = { // 添加 ques 属性
        cate: 0,
        cateName: "",
        content: "",
        analyse: "",
        discuss: "",
        method: "",
        displayAnswer: "",
        options: [],
        pointVos: [],
        userJson: [],
        answers: []
    };
}
let detailData
let timer :  NodeJS.Timeout | null = null
const queryData = route.query.data ? dataDecrypt(route.query.data) : {}
const allTest = ref([] as any[])
watch(() => timeState.seconds, () => {
    if(timeState.seconds == 60) {
        timeState.minutes ++
        timeState.seconds = 0
    }
    if(timeState.minutes == 60) {
        timeState.hours ++
        timeState.minutes = 0
    }

})

const goBack = () =>{
    router.go(-1)
}

// 🎯 优化：计算答题进度百分比，基于已提交题目数量，增强安全性
const progressPercentage = computed(() => {
    try {
        // 🎯 优化：添加更严格的数据验证
        if (!allTest.value || !Array.isArray(allTest.value) || allTest.value.length === 0) {
            return 0
        }
        
        // 🎯 修改：只计算已提交（saveUserJsonApi成功）的题目数量
        const submittedCount = allTest.value.filter(item => {
            try {
                // 🎯 优化：增加安全检查
                if (!item || typeof item !== 'object') {
                    return false;
                }
                
                // 检查是否已提交（submitted为true或userMark不为null表示已通过saveUserJsonApi）
                const isSubmitted = item.submitted === true;
                const isMarked = item.userMark !== null && item.userMark !== undefined;
                
                return isSubmitted || isMarked;
            } catch (error) {
                console.warn('⚠️ progressPercentage: 过滤题目时出错:', error, item);
                return false;
            }
        }).length
        
        const totalCount = allTest.value.length
        
        // 🎯 优化：防止除零错误
        if (totalCount === 0) {
            return 0;
        }
        
        let percentage = Math.round((submittedCount / totalCount) * 100)
        
        // 确保进度在0-100之间
        percentage = Math.max(0, Math.min(100, percentage))
        
        // 如果有题目但都未提交，至少显示2%的进度（让图标可见）
        if (totalCount > 0 && percentage === 0) {
            percentage = 2
        }
        
        console.log('📊 答题进度计算（基于已提交）:', {
            已提交题数: submittedCount,
            总题数: totalCount,
            进度百分比: percentage + '%',
            详细状态: allTest.value.map((item, index) => {
                try {
                    return {
                        题目: index + 1,
                        已提交: item?.submitted || false,
                        已批改: item?.userMark !== null
                    }
                } catch (error) {
                    return {
                        题目: index + 1,
                        已提交: false,
                        已批改: false,
                        错误: '数据异常'
                    }
                }
            })
        })
        
        return percentage
    } catch (error) {
        console.error('❌ progressPercentage 计算出错:', error);
        return 0;
    }
})

// 🎯 优化：检查所有题目是否都已作答，使用统一的检查逻辑，增强安全性
const allQuestionsAnswered = computed(() => {
    try {
        // 🎯 优化：添加更严格的数据验证
        if (!allTest.value || !Array.isArray(allTest.value) || allTest.value.length === 0) {
            return false
        }
        
        // 🎯 优化：使用统一的checkIfHasAnswer函数检查所有题目
        const allAnswered = allTest.value.every(item => {
            try {
                return checkIfHasAnswer(item);
            } catch (error) {
                console.warn('⚠️ allQuestionsAnswered: 检查题目时出错:', error, item);
                return false;
            }
        })
        
        console.log('🔍 检查所有题目是否都已作答:', {
            总题数: allTest.value.length,
            所有题目已作答: allAnswered,
            详细状态: allTest.value.map((item, index) => {
                try {
                    const hasAnswer = checkIfHasAnswer(item);
                    return {
                        题目: index + 1,
                        题目类型: item?.ques?.cate === 1 ? '单选题' : item?.ques?.cate === 3 ? '多选题' : '非选择题',
                        选择题答案: item?.ques?.userJson || [],
                        非选择题答案: item?.userAnswer || [],
                        已作答: hasAnswer,
                        检查结果: hasAnswer ? '✅ 已作答' : '❌ 未作答'
                    }
                } catch (error) {
                    return {
                        题目: index + 1,
                        题目类型: '未知',
                        选择题答案: [],
                        非选择题答案: [],
                        已作答: false,
                        检查结果: '❌ 数据异常'
                    }
                }
            })
        })
        
        return allAnswered
    } catch (error) {
        console.error('❌ allQuestionsAnswered 计算出错:', error);
        return false;
    }
})

// 🎯 新增：检查是否还有未作答的题目
const hasUnansweredQuestions = computed(() => {
    if (!allTest.value || allTest.value.length === 0) {
        return false
    }
    
    return allTest.value.some(item => !checkIfHasAnswer(item))
})

// 🎯 优化：检查是否应该显示完成按钮
const shouldShowFinishButton = computed(() => {
    if (writeState.current === null || allTest.value.length === 0) {
        return false
    }
    
    const isLastQuestion = writeState.current === allTest.value.length - 1
    const allAnswered = allQuestionsAnswered.value
    
    // 🎯 优化：当所有题目都已作答时，无论在哪个题目都显示完成按钮
    const shouldShow = allAnswered
    
    console.log('🎯 完成按钮显示检查:', {
        当前题目索引: writeState.current,
        总题目数: allTest.value.length,
        是否最后一题: isLastQuestion,
        所有题目已作答: allAnswered,
        应该显示完成按钮: shouldShow,
        说明: shouldShow ? '✅ 所有题目已作答，显示完成按钮' : '❌ 还有未作答题目'
    })
    
    return shouldShow
})

// 🎯 优化：检查是否应该显示下一题按钮
const shouldShowNextButton = computed(() => {
    if (writeState.current === null || allTest.value.length === 0) {
        return false
    }
    
    const isLastQuestion = writeState.current === allTest.value.length - 1
    const allAnswered = allQuestionsAnswered.value
    const hasUnanswered = hasUnansweredQuestions.value
    
    // 🎯 优化：如果所有题目都已作答，不显示下一题按钮
    if (allAnswered) {
        console.log('🎯 下一题按钮显示检查: 所有题目已作答，不显示下一题按钮')
        return false
    }
    
    // 如果还有未作答题目，显示下一题按钮
    const shouldShow = hasUnanswered
    
    console.log('🎯 下一题按钮显示检查:', {
        当前题目索引: writeState.current,
        总题目数: allTest.value.length,
        是否最后一题: isLastQuestion,
        所有题目已作答: allAnswered,
        还有未作答题目: hasUnanswered,
        应该显示下一题按钮: shouldShow
    })
    
    return shouldShow
})

// 🎯 新增：检查是否还有下一个主观题
const hasNextSubjectiveQuestion = computed(() => {
    if (writeState.current === null || !writeState.showSubjective) {
        return false
    }
    
    const subjectiveQuestions = allTest.value.filter(item => item.ques.cate != 1 && item.ques.cate != 3)
    const currentSubjectiveIndex = subjectiveQuestions.findIndex(item => allTest.value.indexOf(item) === writeState.current)
    
    // 如果当前不是主观题或已经是最后一个主观题，不显示下一题按钮
    const hasNext = currentSubjectiveIndex !== -1 && currentSubjectiveIndex < subjectiveQuestions.length - 1
    
    return hasNext
})

// 🎯 新增：检查是否是最后一个主观题
const isLastSubjectiveQuestion = computed(() => {
    if (writeState.current === null || !writeState.showSubjective) {
        return false
    }
    
    const subjectiveQuestions = allTest.value.filter(item => item.ques.cate != 1 && item.ques.cate != 3)
    const currentSubjectiveIndex = subjectiveQuestions.findIndex(item => allTest.value.indexOf(item) === writeState.current)
    
    // 🎯 优化：如果当前是最后一个主观题，显示完成按钮
    const isLast = currentSubjectiveIndex !== -1 && currentSubjectiveIndex === subjectiveQuestions.length - 1
    
    return isLast
})

// 🎯 新增：检查是否所有主观题都已批改完成
const allSubjectiveQuestionsCorrected = computed(() => {
    if (!writeState.showSubjective) {
        return false
    }
    
    const subjectiveQuestions = allTest.value.filter(item => item.ques.cate != 1 && item.ques.cate != 3)
    const allCorrected = subjectiveQuestions.every(item => item.userMark !== null && item.userMark !== undefined)

    
    return allCorrected
})

// 🎯 新增：检查是否应该显示完成批改按钮
const shouldShowFinishCorrectionButton = computed(() => {
    if (!writeState.showSubjective) {
        return false
    }
    
    // 如果所有主观题都已批改完成，显示完成批改按钮
    const shouldShow = allSubjectiveQuestionsCorrected.value
    
    return shouldShow
})

// 自定义返回方法
const customGoBack = () => {
    router.go(-1)
}
onMounted(async () => {
    console.log(queryData,"queryDataqueryDataqueryData111111426666")
    
    try {
        // 先加载数据
        await getDetails()
        
        // 数据加载完成后，延迟一点时间再显示弹窗，确保DOM完全渲染
        setTimeout(() => {
            showDialog()
        }, 500)
        
    } catch (error) {
        console.error('页面初始化失败:', error)
        ElMessage.error('页面加载失败，请刷新重试')
    }
   
    // 注册自定义返回方法
    window.customGoBack = customGoBack
})

onUnmounted(() => {
    if (timer !== null) { // 添加类型安全检查
        clearInterval(timer)
        timer = null // 确保timer被清空
    }
    // 重置计时器状态
    writeState.itemTimer = 0

    // 清除自定义返回方法
    if (window.customGoBack) {
        delete window.customGoBack
    }
})
// 🎯 优化：页面数据加载函数
const getDetails = async() => {
    try {
        // 设置加载状态
        writeState.loading = true
        
        console.log('🎯 开始加载页面数据...', {
            source: queryData.source,
            pageSource: queryData.pageSource,
            reportId: queryData.reportId,
            bookId: queryData.bookId,
            pointId: queryData.pointId
        })
        
        if(queryData.source == 'training'){
            // 训练模式：调用知识点详情接口
            try {
                const res = await detailsToPointApi({
                    bookId: queryData.bookId,
                    pointId: queryData.pointId,
                    type: queryData.type,
                    chapterId: queryData.chapterId
                })
                
                // 🎯 优化：增强API响应验证
                if (!res || typeof res !== 'object') {
                    throw new Error('API响应格式无效')
                }
                
                if (res.code === 200) {
                    const data: any = res.data
                    console.log('🎯 训练模式数据加载成功:', data)
                    
                    // 🎯 优化：增强数据验证
                    if (!data || typeof data !== 'object') {
                        throw new Error('训练数据格式无效')
                    }
                    
                    // 处理题目数据
                    if (data.items && Array.isArray(data.items)) {
                        data.items.forEach((item, index) => {
                            try {
                                if (item && typeof item === 'object') {
                                    item.showAnalyse = false
                                }
                            } catch (error) {
                                console.warn(`⚠️ 处理题目${index + 1}时出错:`, error)
                            }
                        })
                        allTest.value = data.items
                        // 初始化当前题目为第一题
                        writeState.current = 0
                    } else {
                        console.warn('⚠️ 训练模式：题目数据为空或格式错误')
                        allTest.value = []
                        writeState.current = null
                    }
                    
                    // 🎯 优化：安全地设置报告数据
                    try {
                        reportState.correctRate = Number(data.correctRate) || 0
                        reportState.correct = Number(data.correct) || 0
                        reportState.quesCount = Number(data.quesCount) || 0
                        reportState.integral = Number(data.integral) || 0
                        reportState.percentage = reportState.quesCount > 0 ? 
                            Number((reportState.correct * 100 / reportState.quesCount).toFixed(0)) : 0
                        reportState.trainTime = Number(data.trainTime / 1000) || 0
                        recordTime.value = Number(data.trainTime / 1000) || 0
                        
                        // 设置报告弹窗数据
                        pointItems.value = data.reportJson?.pointItems || []
                        quesItems.value = data.reportJson?.quesItems || []
                    } catch (error) {
                        console.error('❌ 设置报告数据时出错:', error)
                        // 设置默认值
                        reportState.correctRate = 0
                        reportState.correct = 0
                        reportState.quesCount = 0
                        reportState.integral = 0
                        reportState.percentage = 0
                        reportState.trainTime = 0
                        recordTime.value = 0
                        pointItems.value = []
                        quesItems.value = []
                    }
                    
                    // 根据条件显示弹窗
                    if (queryData.pageSource == '11' || queryData.source === 'training') {
                        dialogState.visible2 = true
                    }
                    
                    console.log('🎯 训练模式数据处理完成，当前题目:', writeState.current)
                } else {
                    throw new Error(res.message || `训练模式API调用失败，错误码: ${res.code}`)
                }
            } catch (error) {
                console.error('❌ 训练模式数据加载失败:', error)
                throw error // 重新抛出错误，让外层catch处理
            }
        } else {
            // 报告模式：调用训练详情接口
            try {
                // 🎯 优化：验证必要参数
                if (!queryData.reportId) {
                    throw new Error('报告ID缺失')
                }
                
                const res: any = await getDetailssApi({trainingId: queryData.reportId})
                
                // 🎯 优化：增强API响应验证
                if (!res || typeof res !== 'object') {
                    throw new Error('API响应格式无效')
                }
                
                if (res.code === 200) {
                    const data = res.data
                    console.log('🎯 报告模式数据加载成功:', data)
                    
                    // 🎯 优化：增强数据验证
                    if (!data || typeof data !== 'object') {
                        throw new Error('报告数据格式无效')
                    }
                    
                    // 处理题目数据
                    if (data.items && Array.isArray(data.items)) {
                        data.items.forEach((item, index) => {
                            try {
                                if (item && typeof item === 'object') {
                                    item.showAnalyse = false
                                }
                            } catch (error) {
                                console.warn(`⚠️ 处理报告题目${index + 1}时出错:`, error)
                            }
                        })
                        allTest.value = data.items
                        // 初始化当前题目为第一题
                        writeState.current = 0
                    } else {
                        console.warn('⚠️ 报告模式：题目数据为空或格式错误')
                        allTest.value = []
                        writeState.current = null
                    }
                    
                    // 🎯 优化：安全地设置报告数据
                    try {
                        reportState.correctRate = parseFloat((Number(data.correctRate)).toFixed(2)) || 0
                        reportState.correct = Number(data.correct) || 0
                        reportState.quesCount = Number(data.quesCount) || 0
                        reportState.integral = Number(data.integral) || 0
                        reportState.percentage = reportState.quesCount > 0 ? 
                            Number((reportState.correct * 100 / reportState.quesCount).toFixed(0)) : 0
                        reportState.trainTime = Number(data.trainTime / 1000) || 0
                        recordTime.value = Number(data.trainTime / 1000) || 0
                        
                        // 设置报告弹窗数据
                        pointItems.value = data.reportJson?.pointItems || []
                        quesItems.value = data.reportJson?.quesItems || []
                    } catch (error) {
                        console.error('❌ 设置报告模式数据时出错:', error)
                        // 设置默认值
                        reportState.correctRate = 0
                        reportState.correct = 0
                        reportState.quesCount = 0
                        reportState.integral = 0
                        reportState.percentage = 0
                        reportState.trainTime = 0
                        recordTime.value = 0
                        pointItems.value = []
                        quesItems.value = []
                    }
                    
                    // 根据条件显示弹窗
                    if (queryData.pageSource == '11' || queryData.source === 'analysis') {
                        dialogState.visible2 = true
                    }
                    
                    console.log('🎯 报告模式数据处理完成，当前题目:', writeState.current)
                } else {
                    throw new Error(res.message || `报告模式API调用失败，错误码: ${res.code}`)
                }
            } catch (error) {
                console.error('❌ 报告模式数据加载失败:', error)
                throw error // 重新抛出错误，让外层catch处理
            }
        }
        
    } catch (error) {
        console.error('❌ 数据加载失败:', error)
        ElMessage.error('数据加载失败，请刷新重试')
        
        // 设置默认值
        allTest.value = []
        writeState.current = null
        reportState.correctRate = 0
        reportState.correct = 0
        reportState.quesCount = 0
        reportState.integral = 0
        reportState.percentage = 0
        reportState.trainTime = 0
        recordTime.value = 0
        pointItems.value = []
        quesItems.value = []
        
    } finally {
        // 关闭加载状态
        writeState.loading = false
        console.log('🎯 数据加载完成，加载状态已关闭')
    }
}
// 测评报告弹窗
const showDialog = () => {
    // 防止重复显示
    if (dialogState.visible2) {
        console.log('🎯 弹窗已显示，跳过重复显示')
        return
    }
    
    // 根据来源决定是否显示弹窗
    const shouldShowDialog = queryData.source === 'analysis' || 
                           queryData.source === 'ripe' || 
                           queryData.source === 'training_report' ||
                           queryData.pageSource === 'analysis' ||
                           queryData.pageSource === 'ripe' ||
                           queryData.pageSource === 'training_report'
    
    if(shouldShowDialog){
        dialogState.visible2 = true
        console.log('🎯 来自报告相关页面，显示报告弹窗', {
            source: queryData.source,
            pageSource: queryData.pageSource
        })
        
        // 设置弹窗数据（如果有的话）
        if (reportState.quesCount === 0) {
            // 如果还没有数据，设置一些默认值
            reportState.quesCount = allTest.value.length || 0
            reportState.correctRate = 0
            reportState.trainTime = 0
        }
    } else {
        dialogState.visible2 = false
        console.log('🎯 非报告相关页面来源，不显示报告弹窗', {
            source: queryData.source,
            pageSource: queryData.pageSource
        })
    } 
}
// 将时分秒转换为毫秒时间戳
const convertTimeToMilliseconds = () => {
    const totalSeconds = timeState.hours * 3600 + timeState.minutes * 60 + timeState.seconds
    return totalSeconds * 1000 // 转换为毫秒
}

  //显示答案
// const togAnswer = async (item:any,isShow:any) => {
//     if(isShow){
//         // 如果已经有完整的题目信息，直接显示，无需重复请求
//         if (item.ques.analyse && item.ques.method && item.ques.discuss) {
//             return
//         }
//         try {
//             // 添加加载状态，防止重复点击
//             if (item.loading) return
//             item.loading = true

//             const response = await quesGetApi({id: item.ques.quesId}) as any

//             if (response.code === 200 && response.data) {
//                 // 使用 Object.assign 来安全地合并数据，保留原有属性
//                 Object.assign(item.ques, response.data)

//                 // 确保必要的属性存在
//                 if (!item.ques.pointVos) {
//                     item.ques.pointVos = []
//                 }
//                 if (!item.ques.options) {
//                     item.ques.options = []
//                 }
//                 if (!item.ques.answers) {
//                     item.ques.answers = []
//                 }
//             } else {
//                 console.error('获取题目详细信息失败:', response)
//                 // 如果获取失败，关闭显示开关
//                 item.showAnalyse = false
//                 // 可以添加用户提示
//             }
//         } catch (error) {
//             console.error('获取题目详细信息时发生错误:', error)
//             // 发生错误时关闭显示开关
//             item.showAnalyse = false
//             // 可以添加用户提示
//         } finally {
//             // 清除加载状态
//             item.loading = false
//         }
//     }

// }

const createTrain = () => {
    const formdata = new FormData()
    formdata.append("sourceId", queryData.sourceId)
    formdata.append("noteSource", '1')
    // for(let i of queryData.reportId){
    //   formdata.append("pointIds[]", i)
    // }
    formdata.append("step", queryData.step)
    createTrainToAtlasApi(formdata).then((res: any) => {
        if (res.data) {
            writeState.trainingId = res.data
            writeState.showStep = true

            // 获取详情
            getDetails()

        }

    }).catch((error) => {
    })
}
// const getDetails = async () => {
//     try {
//         // 🎯 优化：直接使用动态trainingId，移除缓存逻辑
//         const trainingId = queryData.reportId || queryData.trainingId || '1970043752987348993'
               
//         // 直接调用API获取数据
//         const res1 = await getDetailssApi({trainingId}) as any
        
//         if (res1.code === 200) {
            
//             // 直接处理返回的数据
//             processDetailsData(res1)
            
//         } else {
//             console.error('❌ 获取训练详情失败:', res1)
//             ElMessage.error(res1.message || '获取训练详情失败')
//             writeState.loading = false
//         }

//     } catch (error) {
//         console.error('❌ 获取训练详情时发生错误:', error)
//         ElMessage.error('网络错误，请检查网络连接后重试')
//         writeState.loading = false
//     }
// }

// 提取数据处理逻辑为独立方法
const processDetailsData = (res1: any) => {
    try {
        
        // 验证数据完整性
        if (!res1.data || !res1.data.items || !Array.isArray(res1.data.items)) {
            throw new Error('训练数据格式不正确')
        }
        
    detailData = res1.data
        
        // 设置训练时间
        if (res1.data.trainTime) {
    timeState.seconds = Number(res1.data.trainTime) / 1000
            // console.log('⏰ 设置训练时间:', timeState.seconds, '秒')
        }
        
        // 处理题目数据
        res1.data.items.forEach((item, index) => {
            // 🔍 调试：打印每个题目的原始数据
            // console.log(`🔍 题目${index + 1}原始数据检查:`, {
            //     userAnswer: item.userAnswer,
            //     userMark: item.userMark,
            //     submitted: item.submitted,
            //     cate: item.ques?.cate,
            //     cateName: item.ques?.cateName
            // })
            
            // 初始化基础属性
        item.showAnalyse = false
        
            // 确保ques对象存在
            if (!item.ques) {
                console.warn(`⚠️ 题目${index + 1}缺少ques对象`)
                item.ques = {
                    cate: 0,
                    cateName: "",
                    content: "",
                    analyse: "",
                    discuss: "",
                    method: "",
                    displayAnswer: "",
                    options: [],
                    pointVos: [],
                    userJson: [],
                    answers: []
                }
            }
            
            // 🎯 修复：优先检查userAnswer是否有值，恢复已答题目的数据
            const hasUserAnswer = item.userAnswer && item.userAnswer.length > 0
            const isChoiceQuestion = item.ques.cate === 1 || item.ques.cate === 3
            const isSubmittedOrGraded = item.userMark !== null || item.submitted === true
            
            if (hasUserAnswer && isChoiceQuestion) {
                // 选择题且有用户答案：恢复答案数据
                try {
                    // 处理不同格式的userAnswer
                    let userAnswerArray = []
                    if (Array.isArray(item.userAnswer)) {
                        userAnswerArray = item.userAnswer
                    } else if (typeof item.userAnswer === 'string') {
                        // 可能是JSON字符串或逗号分隔的字符串
                        try {
                            userAnswerArray = JSON.parse(item.userAnswer)
                        } catch {
                            userAnswerArray = item.userAnswer.split(',').filter(v => v.trim())
                        }
                    }
                    
                    item.ques.userJson = userAnswerArray.map((answer: any) => parseInt(answer))
                    // console.log(`✅ 恢复题目${index + 1}选择题答案:`, {
                    //     题目类型: item.ques.cateName,
                    //     原始userAnswer: item.userAnswer,
                    //     处理后数组: userAnswerArray,
                    //     最终userJson: item.ques.userJson,
                    //     是否已提交: isSubmittedOrGraded
                    // })
                } catch (error) {
                    console.error(`❌ 题目${index + 1}答案处理失败:`, error)
                    item.ques.userJson = []
                }
            } else if (hasUserAnswer && !isChoiceQuestion) {
                // 🎯 优化：非选择题且有用户答案，确保数据正确恢复
                item.ques.userJson = []
                
                // 验证userAnswer是否为有效的图片URL数组
                const validImages = item.userAnswer.filter(url => 
                    typeof url === 'string' && url.trim().length > 0
                )
                
                if (validImages.length !== item.userAnswer.length) {
                    // 清理无效数据
                    item.userAnswer = validImages
                }
                
                // console.log(`✅ 题目${index + 1}非选择题数据恢复:`, {
                //     题目类型: item.ques.cateName,
                //     图片数量: item.userAnswer.length,
                //     图片URLs: item.userAnswer,
                //     是否已提交: item.submitted,
                //     是否已批改: item.userMark !== null,
                //     批改结果: item.userMark,
                //     完整状态: isSubmittedOrGraded ? '已提交/批改' : '未提交'
                // })
        } else {
                // 没有用户答案：初始化为空
            item.ques.userJson = []
                // console.log(`📝 题目${index + 1}无答案，初始化userJson`, {
                //     题目类型: item.ques.cateName,
                //     hasUserAnswer,
                //     isChoiceQuestion
                // })
            }
        })
        
        // 设置题目数据
        allTest.value = res1.data.items

        // 检查是否有未完成的题目并设置当前题目
        findAndSetCurrentQuestion()
        
        // 启动计时器（如果有未完成题目）
        startTimerIfNeeded()
        
        writeState.loading = false
        console.log('✅ 训练详情数据处理完成')
        
        // 🎯 验证非选择题数据恢复状态
        validateNonChoiceQuestionsData()
        
        // 🎯 调试：打印题号状态信息
        setTimeout(() => {
            debugQuestionStatus()
        }, 100)
        
    } catch (error) {
        console.error('❌ 处理训练详情数据时发生错误:', error)
        // ElMessage.error('数据处理失败：' + error.message)
        writeState.loading = false
    }
}

// 🎯 优化：查找并设置当前题目，优先显示未作答的题目
const findAndSetCurrentQuestion = () => {
    let hasUnansweredQuestions = false
    let currentQuestionSet = false
    
    // 🎯 优化：优先查找未作答的题目
    for (let i = 0; i < allTest.value.length; i++) {
        const item = allTest.value[i]
        if (!checkIfHasAnswer(item)) {
            writeState.current = i
            writeState.itemTimer = 0 // 重置单题计时器
            hasUnansweredQuestions = true
            currentQuestionSet = true
            console.log('📍 设置当前题目为未作答题目:', i + 1)
            break
        }
    }
    
    // 如果没有未作答的题目，但有题目，则设置为第一题（用于查看模式）
    if (!currentQuestionSet && allTest.value.length > 0) {
        writeState.current = 0
        console.log('📍 设置当前题目为第一题（查看模式）:', 1)
    }
    
    // 检查是否所有题目都已作答
    if (!hasUnansweredQuestions && allTest.value.length > 0) {
        const allAnswered = allQuestionsAnswered.value
        if (allAnswered) {
            // 🎯 优化：所有题目完成后不设置为null，保持当前题目索引以继续显示答题内容
            // writeState.current = null  // 注释掉这行，保持当前题目索引
            writeState.disabled = false
            console.log('🎉 所有题目都已作答，保持当前题目显示')
        }
    }
    
    // 🎯 设置初始答题区域显示模式
    if (writeState.current !== null) {
        updateAnswerAreaDisplay(writeState.current)
    }
    
    return hasUnansweredQuestions
}

// 新增：根据需要启动计时器
const startTimerIfNeeded = () => {
    // 🎯 优化：即使所有题目都已完成，也保持计时器运行（因为用户可能还在查看题目）
    // 只有当真正没有题目时才停止计时器
    if (allTest.value.length === 0) {
        if (timer !== null) {
            clearInterval(timer)
            timer = null
        }
        return
    }
    
    // 如果计时器还未启动，启动它
    if (timer === null) {
        timer = setInterval(() => {
            timeState.seconds++
            writeState.itemTimer++ // 更新单题计时
        }, 1000)
        console.log('⏱️ 计时器已启动')
    }
}
const setClass = (item: any, index: number) => {
    let classState = ""
    if (item.userMark != null) {
        if (item.userMark == 0) {
            classState = "red-border"
        } else if (item.userMark == 1) {
            classState = "green-border"
        } else if (item.userMark == 2) {
            classState = "yellow-border"
        }

    } else if (writeState.current == index) {
        classState = "black-text"
    }
    return classState
}

// 🎯 优化：去批改主观题
const toCorrect = () => {
    console.log('📝 开始批改主观题流程')
    
    // 🎯 优化：获取所有非选择题（主观题）
    const subjectiveQuestions = allTest.value.filter(item => item.ques.cate != 1 && item.ques.cate != 3)
    
    console.log('🔍 发现的主观题:', subjectiveQuestions.map((item, index) => ({
        题目: allTest.value.indexOf(item) + 1,
        类型: item.ques.cateName,
        题目分类: item.ques.cate,
        用户答案: item.userJson,
        批改状态: item.userMark
    })))
    
    // 🎯 优化：处理非选择题的图片数据
    subjectiveQuestions.forEach((item: any) => {
        console.log(`🔄 处理题目${allTest.value.indexOf(item) + 1}的图片数据:`, {
            原始userJson: item.userJson,
            题目类型: item.ques.cateName
        })
        
        if (item.userJson && Array.isArray(item.userJson)) {
            // 如果userJson是对象数组，提取url
            if (item.userJson.length > 0 && typeof item.userJson[0] === 'object' && item.userJson[0].url) {
                item.userJson = item.userJson.map((img: any) => img.url)
                console.log(`✅ 题目${allTest.value.indexOf(item) + 1}图片数据已转换:`, item.userJson)
            }
            // 如果userJson已经是URL数组，保持不变
            else if (item.userJson.length > 0 && typeof item.userJson[0] === 'string') {
                console.log(`✅ 题目${allTest.value.indexOf(item) + 1}图片数据已是URL格式:`, item.userJson)
            }
        }
        
        // 设置images用于显示
        item.images = [...(item.userJson || [])]
    })
    
    // 🎯 优化：激活主观题批改模式
    writeState.showDialog = false
    writeState.showSubjective = true
    writeState.isAllCorrect = false
    
    // 🎯 新增：设置当前题目为第一个主观题
    if (subjectiveQuestions.length > 0) {
        const firstSubjectiveIndex = allTest.value.indexOf(subjectiveQuestions[0])
        writeState.current = firstSubjectiveIndex
        writeState.itemTimer = 0
        console.log(`📍 设置当前题目为第一个主观题: ${firstSubjectiveIndex + 1}`)
    }
    
    console.log('✅ 主观题批改环节已激活:', {
        showSubjective: writeState.showSubjective,
        isAllCorrect: writeState.isAllCorrect,
        主观题数量: subjectiveQuestions.length,
        当前题目: writeState.current + 1,
        批改状态: subjectiveQuestions.map(item => ({
            题目: allTest.value.indexOf(item) + 1,
            已批改: item.userMark !== null,
            批改结果: item.userMark === 1 ? '正确' : item.userMark === 2 ? '半对' : item.userMark === 0 ? '错误' : '未批改'
        }))
    })
    
    // 🎯 新增：强制触发响应式更新
    setTimeout(() => {
        console.log('🔄 强制触发响应式更新，确保非选择题正确渲染')
        // 这里可以添加一些强制更新的逻辑
    }, 100)
}

// 🎯 新增：批改主观题
const correcthandle = (index: number, userMark: number) => {
    // 验证 index 是否在有效范围内
    if (index < 0 || index >= allTest.value.length) {
        console.error(`Invalid index: ${index}`)
        return
    }

    // 确保 allTest.value[index] 存在
    const currentItem = allTest.value[index]
    if (!currentItem) {
        console.error(`Item at index ${index} is undefined`)
        return
    }

    // 🎯 优化：更新批改结果
    currentItem.userMark = userMark
    
    console.log(`📝 批改题目${index + 1}:`, {
        题目类型: currentItem.ques.cateName,
        批改结果: userMark === 1 ? '正确' : userMark === 2 ? '半对' : '错误',
        用户答案: currentItem.userAnswer
    })
    
    // 🎯 优化：判断是否已全部批改
    writeState.isAllCorrect = true
    const subjectiveQuestions = allTest.value.filter(item => item.ques.cate != 1 && item.ques.cate != 3)
    
    subjectiveQuestions.forEach((item: any) => {
        if (item.userMark == null) {
            writeState.isAllCorrect = false
        }
    })
    
    console.log('📊 批改进度:', {
        总主观题数: subjectiveQuestions.length,
        已批改数: subjectiveQuestions.filter(item => item.userMark !== null).length,
        未批改数: subjectiveQuestions.filter(item => item.userMark === null).length,
        是否全部批改: writeState.isAllCorrect
    })
}

// 🎯 新增：主观题批改模式下的下一题功能
const goToNextSubjectiveQuestion = () => {
    console.log('📝 主观题批改模式：切换到下一题')
    
    if (writeState.current === null) {
        console.log('❌ 当前题目索引为null，无法切换')
        return
    }
    
    // 查找下一个主观题
    const subjectiveQuestions = allTest.value.filter(item => item.ques.cate != 1 && item.ques.cate != 3)
    const currentSubjectiveIndex = subjectiveQuestions.findIndex(item => allTest.value.indexOf(item) === writeState.current)
    
    if (currentSubjectiveIndex === -1) {
        console.log('❌ 当前题目不是主观题')
        return
    }
    
    // 查找下一个主观题
    let nextSubjectiveIndex = -1
    for (let i = currentSubjectiveIndex + 1; i < subjectiveQuestions.length; i++) {
        nextSubjectiveIndex = allTest.value.indexOf(subjectiveQuestions[i])
        break
    }
    
    if (nextSubjectiveIndex !== -1) {
        writeState.current = nextSubjectiveIndex
        writeState.itemTimer = 0
        console.log(`✅ 切换到下一个主观题: ${nextSubjectiveIndex + 1}`)
    } else {
        console.log('ℹ️ 已经是最后一个主观题')
    }
}

// 🎯 新增：主观题批改模式下的题目切换
const switchToSubjectiveQuestion = (index: number) => {
    console.log('📝 主观题批改模式：点击题号切换题目')
    console.log('从题目', writeState.current + 1, '切换到题目', index + 1)
    
    const targetQuestion = allTest.value[index]
    
    if (!targetQuestion) {
        console.log('❌ 目标题目不存在')
        return
    }
    
    // 检查是否是主观题
    if (targetQuestion.ques.cate === 1 || targetQuestion.ques.cate === 3) {
        console.log('❌ 目标题目不是主观题，无法在批改模式下切换')
        return
    }
    
    const oldQuestion = writeState.current !== null ? allTest.value[writeState.current] : null
    
    if (oldQuestion) {
        console.log('离开题目信息:', {
            题目序号: writeState.current + 1,
            题目类型: oldQuestion?.ques?.cateName,
            批改状态: oldQuestion?.userMark !== null ? (oldQuestion.userMark === 1 ? '正确' : oldQuestion.userMark === 2 ? '半对' : '错误') : '未批改'
        })
    }
    
    // 切换到目标主观题
    writeState.itemTimer = 0
    writeState.current = index
    
    console.log('切换到题目信息:', {
        题目序号: index + 1,
        题目类型: targetQuestion?.ques?.cateName,
        批改状态: targetQuestion?.userMark !== null ? (targetQuestion.userMark === 1 ? '正确' : targetQuestion.userMark === 2 ? '半对' : '错误') : '未批改',
        用户答案: targetQuestion?.userAnswer
    })
    
    console.log('=== 主观题切换完成 ===')
}

// 🎯 新增：继续提交流程（批改主观题后调用）
const continueSubmit = async () => {
    console.log('🔄 继续提交流程')
    writeState.btnloading = true
    
    // 继续执行原来的提交流程
    await executeSubmit()
}

// 题号颜色
const setClass1 = (item: any, index: number) => {
    let classState = ""
    
    // 🎯 优化：只有已批改的题目才显示结果颜色
    if (item.userMark !== null && item.userMark !== undefined) {
        // 已批改：显示批改结果颜色
        if (item.userMark === 0) {
            classState = "red"      // 错误
        } else if (item.userMark === 1) {
            classState = "green"    // 正确
        } else if (item.userMark === 2) {
            classState = "yellow"   // 半对
        }
        // console.log(`题目${index + 1}已批改，显示结果颜色:`, classState)
    } else {
        // 未批改的题目：根据答题状态显示
        const hasAnswer = checkIfHasAnswer(item)
        
        if (hasAnswer) {
            // 已作答但未批改：显示蓝色
        classState = "blue"
            // console.log(`题目${index + 1}已作答未批改，显示蓝色`)
        } else if (writeState.current === index) {
            // 当前题目：显示蓝色高亮
            classState = "blue"
            // console.log(`题目${index + 1}当前题目，显示蓝色高亮`)
        } else {
            // 未作答：显示默认状态（白色）
            classState = ""
            // console.log(`题目${index + 1}未作答，显示默认状态`)
        }
    }
    
    return classState
}

// 🎯 优化：检查题目是否已作答的辅助方法，支持所有题目类型
const checkIfHasAnswer = (item: any) => {
    // 🎯 修复：添加空值检查，防止 undefined 错误
    if (!item || typeof item !== 'object') {
        console.warn('⚠️ checkIfHasAnswer: item 参数无效:', item);
        return false;
    }
    
    // 确保 ques 对象存在
    if (!item.ques || typeof item.ques !== 'object') {
        console.warn('⚠️ checkIfHasAnswer: item.ques 不存在:', item);
        return false;
    }
    
    const isChoiceQuestion = item.ques.cate === 1 || item.ques.cate === 3
    
    if (isChoiceQuestion) {
        // 选择题：检查userJson是否有选择
        const hasChoiceAnswer = item.ques.userJson && Array.isArray(item.ques.userJson) && item.ques.userJson.length > 0
        // console.log(`🔍 选择题${item.ques.cateName}作答检查:`, {
        //     userJson: item.ques.userJson,
        //     hasChoiceAnswer,
        //     题目类型: item.ques.cate === 1 ? '单选题' : '多选题'
        // });
        return hasChoiceAnswer
    } else {
        // 🎯 优化：非选择题：检查userAnswer是否有上传的图片或文件
        const hasUploadAnswer = item.userAnswer && Array.isArray(item.userAnswer) && item.userAnswer.length > 0
        
        // 额外检查：如果userAnswer为空但userJson有值，也认为已作答
        const hasUserJsonAnswer = item.ques.userJson && Array.isArray(item.ques.userJson) && item.ques.userJson.length > 0
        
        const finalResult = hasUploadAnswer || hasUserJsonAnswer;        
        
        return finalResult
    }
}

// 🎯 新增：验证非选择题数据恢复状态
const validateNonChoiceQuestionsData = () => {
    
    const nonChoiceQuestions = allTest.value.filter(item => 
        item.ques?.cate !== 1 && item.ques?.cate !== 3
    )
    
    if (nonChoiceQuestions.length === 0) {
        return
    }
    
    nonChoiceQuestions.forEach((item, index) => {
        const realIndex = allTest.value.findIndex(q => q === item)
        const hasImages = item.userAnswer && Array.isArray(item.userAnswer) && item.userAnswer.length > 0
        const hasAnswer = checkIfHasAnswer(item)
        const colorStatus = setClass1(item, realIndex)
        
        // console.log(`🔍 非选择题${realIndex + 1}验证:`, {
        //     题目类型: item.ques?.cateName,
        //     有图片数据: hasImages,
        //     图片数量: item.userAnswer?.length || 0,
        //     图片URLs: hasImages ? item.userAnswer : '无',
        //     checkIfHasAnswer结果: hasAnswer,
        //     是否已提交: item.submitted,
        //     是否已批改: item.userMark !== null,
        //     题号背景色: colorStatus,
        //     预期显示模式: hasImages ? '查看模式（显示图片）' : '答题模式（显示上传组件）'
        // })
        
        // 验证数据一致性
        if (hasImages !== hasAnswer) {
            console.warn(`⚠️ 题目${realIndex + 1}数据不一致:`, {
                hasImages,
                hasAnswer,
                可能原因: 'checkIfHasAnswer方法判断逻辑需要检查'
            })
        }
    })
    
    console.log('=== 非选择题验证完成 ===')
}

// 🎯 调试方法：验证题号状态显示
const debugQuestionStatus = () => {
    allTest.value.forEach((item, index) => {
        const hasAnswer = checkIfHasAnswer(item)
        const status = setClass1(item, index)
        // console.log(`题目${index + 1}:`, {
        //     题目类型: item.ques?.cateName,
        //     是否已作答: hasAnswer,
        //     是否已批改: item.userMark !== null,
        //     批改结果: item.userMark,
        //     显示状态: status,
        //     userJson: item.ques?.userJson,
        //     userAnswer: item.userAnswer
        // })
    })
}

// 🎯 优化：点击题号切换题目的方法，支持非选择题查看
const switchQuestion = (index: number) => {
    // console.log('=== 点击题号切换题目 ===')
    // console.log('从题目', writeState.current + 1, '切换到题目', index + 1)
    
    const targetQuestion = allTest.value[index]
    
    const oldQuestion = writeState.current !== null ? allTest.value[writeState.current] : null
    
    if (oldQuestion) {
        console.log('离开题目信息:', {
            题目序号: writeState.current + 1,
            题目类型: oldQuestion?.ques?.cateName,
            用户答案_选择题: oldQuestion?.ques?.userJson,
            用户答案_非选择题: oldQuestion?.userAnswer,
            是否已答题: checkIfHasAnswer(oldQuestion),
            是否已提交: oldQuestion?.submitted === true
        })
    }
    
    // 允许切换到任何题目（包括已答题的题目）
    writeState.itemTimer = 0
    writeState.current = index
    
    // 🎯 优化：根据题目类型和答题状态设置显示模式
    updateAnswerAreaDisplay(index)
    
    console.log('切换到题目信息:', {
        题目序号: index + 1,
        题目类型: targetQuestion?.ques?.cateName,
        题目分类: targetQuestion?.ques?.cate === 1 ? '单选题' : targetQuestion?.ques?.cate === 3 ? '多选题' : '非选择题',
        用户答案_选择题: targetQuestion?.ques?.userJson,
        用户答案_非选择题: targetQuestion?.userAnswer,
        是否已答题: checkIfHasAnswer(targetQuestion),
        是否已提交: targetQuestion?.submitted === true,
        是否已批改: targetQuestion?.userMark !== null,
        当前模式: writeState.showCorrect ? '答题模式' : '查看模式'
    })
    console.log('=== 题目切换完成 ===')
}

// 🎯 优化：统一的答题区域显示逻辑，支持刷新后数据恢复
const updateAnswerAreaDisplay = (questionIndex: number) => {
    const currentQuestion = allTest.value[questionIndex]
    if (!currentQuestion) {
        console.warn('⚠️ 题目不存在:', questionIndex)
        return
    }
    
    const isChoiceQuestion = currentQuestion?.ques?.cate === 1 || currentQuestion?.ques?.cate === 3
    const isSubmittedOrGraded = currentQuestion.submitted === true || currentQuestion.userMark !== null
    const hasUserAnswer = checkIfHasAnswer(currentQuestion)
    
    console.log(`🎯 题目${questionIndex + 1}显示模式判断:`, {
        题目类型: isChoiceQuestion ? '选择题' : '非选择题',
        是否已答: hasUserAnswer,
        是否已提交或批改: isSubmittedOrGraded,
        userAnswer内容: currentQuestion.userAnswer,
        userAnswer长度: currentQuestion.userAnswer?.length || 0,
        userJson长度: currentQuestion.ques?.userJson?.length || 0,
        submitted状态: currentQuestion.submitted,
        userMark状态: currentQuestion.userMark
    })
    
    if (isChoiceQuestion) {
        // 选择题：始终显示选项区域
        writeState.showCorrect = true
        console.log(`✅ 选择题${questionIndex + 1}：显示选项区域`)
    } else {
        // 🎯 优化：非选择题显示逻辑，确保刷新后正确显示已上传图片
        if (hasUserAnswer) {
            if (isSubmittedOrGraded) {
                // 已答题且已提交/批改：查看模式（显示已上传的图片）
        writeState.showCorrect = false
                console.log(`📖 非选择题${questionIndex + 1}：已答题已提交，查看模式 - 显示已上传图片`)
                console.log(`🖼️ 图片列表:`, currentQuestion.userAnswer)
    } else {
                // 已答题但未提交：根据具体情况决定
                // 如果有图片数据，优先显示查看模式（让用户看到已上传的图片）
                if (currentQuestion.userAnswer && currentQuestion.userAnswer.length > 0) {
                    writeState.showCorrect = false
                    console.log(`📖 非选择题${questionIndex + 1}：已上传图片未提交，查看模式 - 显示已上传图片`)
                    console.log(`🖼️ 图片列表:`, currentQuestion.userAnswer)
                } else {
                    // 标记为已答但没有图片数据，切换到答题模式
        writeState.showCorrect = true
                    console.log(`✏️ 非选择题${questionIndex + 1}：已答题但无图片数据，答题模式`)
                }
            }
        } else {
            // 未答题：答题模式
            writeState.showCorrect = true
            console.log(`📝 非选择题${questionIndex + 1}：未答题，答题模式`)
        }
    }
    
    // 使用nextTick确保DOM更新
    nextTick(() => {
        // console.log(`🔄 题目${questionIndex + 1}显示模式已更新:`, {
        //     当前模式: writeState.showCorrect ? '答题模式' : '查看模式',
        //     显示内容: writeState.showCorrect ? '上传组件' : '已上传图片',
        //     图片数量: currentQuestion.userAnswer?.length || 0
        // })
    })
}

// 🎯 优化：统一处理选项点击
// const handleOptionClick = (optionIndex: number) => {
//     console.log('=== 点击选项 ===')
//     console.log('点击的选项索引:', optionIndex)
//     console.log('当前题目索引:', writeState.current)
    
//     if (writeState.current === null) {
//         console.log('❌ 当前题目索引为null，无法选择选项')
//         return
//     }
    
//     const currentItem = allTest.value[writeState.current]
    
//     // 检查题目是否已提交或已批改（只读模式）
//     if (currentItem.submitted === true || currentItem.userMark !== null) {
//         console.log('ℹ️ 题目已提交或已批改，仅查看模式')
//         // 在已批改的情况下，可以显示答案解析等信息
//         if (currentItem.userMark !== null) {
//             console.log('📊 查看批改结果:', {
//                 用户答案: currentItem.ques.userJson,
//                 正确答案: currentItem.ques.answers,
//                 批改结果: currentItem.userMark === 1 ? '正确' : currentItem.userMark === 2 ? '半对' : '错误'
//             })
//         }
//         return
//     }
    
//     // 可以选择答案的情况
//     selectOption(optionIndex)
// }

// 选择选项方法
const selectOption = (optionIndex: number) => {
    console.log('=== 选择答案选项 ===')
    console.log('点击的选项索引:', optionIndex)
    console.log('当前题目索引:', writeState.current)
    
    if (writeState.current === null) {
        console.log('当前题目索引为null，无法选择选项')
        return
    }
    
    const currentItem = allTest.value[writeState.current]
    
    // 检查题目是否已提交
    if (currentItem.submitted === true) {
        console.log('题目已提交，不允许修改答案')
        return
    }
    
    // 检查题目是否已批改
    if (currentItem.userMark !== null) {
        console.log('题目已批改，不允许修改答案')
        return
    }
    
    console.log('当前题目类型:', currentItem?.ques?.cate === 1 ? '单选题' : '多选题')
    console.log('选择前的用户答案:', currentItem?.ques?.userJson)
    
    if (!currentItem || !currentItem.ques.userJson) {
        currentItem.ques.userJson = []
    }
    
    // 对于单选题，替换选择；对于多选题，切换选择
    if (currentItem.ques.cate === 1) { // 单选题
        currentItem.ques.userJson = [optionIndex]
        console.log('单选题：设置答案为', [optionIndex])
    } else { // 多选题
        const index = currentItem.ques.userJson.indexOf(optionIndex)
        if (index > -1) {
            currentItem.ques.userJson.splice(index, 1)
            console.log('多选题：取消选择', optionIndex, '，当前答案:', currentItem.ques.userJson)
        } else {
            currentItem.ques.userJson.push(optionIndex)
            console.log('多选题：添加选择', optionIndex, '，当前答案:', currentItem.ques.userJson)
        }
    }
    
    console.log('选择后的最终答案:', currentItem.ques.userJson)
    console.log('是否已答题:', Boolean(currentItem.ques.userJson.length))
    
    // 🎯 新增：验证选择题答题时进度条不移动
    const currentProgress = progressPercentage.value
    console.log('📊 选择题答题后进度检查:', {
        当前进度: currentProgress + '%',
        说明: '选择题答题不会移动进度条，只有saveUserJsonApi成功后才移动',
        题目状态: {
            已选择: Boolean(currentItem.ques.userJson.length),
            已提交: currentItem.submitted,
            已批改: currentItem.userMark !== null
        }
    })
    
    console.log('=== 选择答案完成 ===')
}

// 上一题
const goToPrevQuestion = () => {
    if (writeState.current !== null && writeState.current > 0) {
        writeState.current = writeState.current - 1
        writeState.itemTimer = 0
    }
}

// 🎯 优化：智能下一题功能
const goToNextQuestion = () => {
    console.log('=== 点击下一题按钮 ===')
    console.log('当前题目索引:', writeState.current)
    console.log('总题目数量:', allTest.value.length)
    console.log('所有题目已作答:', allQuestionsAnswered.value)
    console.log('还有未作答题目:', hasUnansweredQuestions.value)
    
    if (writeState.current !== null) {
        const currentQuestion = allTest.value[writeState.current]
        const hasAnswer = checkIfHasAnswer(currentQuestion)
        
        console.log('当前题目信息:', {
            题目序号: writeState.current + 1,
            题目类型: currentQuestion?.ques?.cateName,
            用户答案_选择题: currentQuestion?.ques?.userJson,
            用户答案_非选择题: currentQuestion?.userAnswer,
            是否已答题: hasAnswer
        })
        
        // 如果当前题目已答题，先提交
        if (hasAnswer) {
            console.log('检测到已答题，先提交当前题目')
            
            // 🎯 优化：智能查找下一个未作答的题目
            let nextUnansweredIndex:any = null
            for (let i = 0; i < allTest.value.length; i++) {
                if (!checkIfHasAnswer(allTest.value[i])) {
                    nextUnansweredIndex = i
                    break
                }
            }
            
            // 设置标记，表示这是通过"下一题"按钮触发的提交
            writeState.fromNextButton = true
            writeState.expectedNextIndex = nextUnansweredIndex
            
            console.log('期望跳转到下一个未作答题目索引:', nextUnansweredIndex)
            
            handleSubmit(currentQuestion, writeState.current)
        } else {
            // 如果当前题目未答题，直接跳转到下一个未作答的题目
            const nextUnansweredIndex = findNextUnansweredQuestion(writeState.current)
            
            if (nextUnansweredIndex !== null) {
                console.log('当前题目未答题，直接跳转到下一个未作答题目，索引:', nextUnansweredIndex)
                writeState.current = nextUnansweredIndex
                writeState.itemTimer = 0
                
                const nextQuestion = allTest.value[nextUnansweredIndex]
                console.log('跳转到题目信息:', {
                    题目序号: nextUnansweredIndex + 1,
                    题目类型: nextQuestion?.ques?.cateName,
                    用户答案_选择题: nextQuestion?.ques?.userJson,
                    用户答案_非选择题: nextQuestion?.userAnswer,
                    是否已答题: checkIfHasAnswer(nextQuestion)
                })
                
                // 更新答题区域显示模式
                updateAnswerAreaDisplay(nextUnansweredIndex)
            } else {
                console.log('所有题目都已作答，无法跳转')
            }
        }
    } else {
        console.log('当前题目索引为null，无法切换')
    }
    console.log('=== 下一题按钮处理完成 ===')
}

// 🎯 新增：查找下一个未作答的题目
const findNextUnansweredQuestion = (currentIndex: number) => {
    // 从当前题目之后开始查找
    for (let i = currentIndex + 1; i < allTest.value.length; i++) {
        if (!checkIfHasAnswer(allTest.value[i])) {
            return i
        }
    }
    
    // 如果后面没有未作答的题目，从头开始查找
    for (let i = 0; i < currentIndex; i++) {
        if (!checkIfHasAnswer(allTest.value[i])) {
            return i
        }
    }
    
    // 没有找到未作答的题目
    return null
}

// 🎯 新增：自动判断答案是否正确
const checkAnswerCorrectness = (userAnswers: any[], correctAnswers: any[], questionType: number) => {
    console.log('🔍 开始判断答案正确性:', {
        用户答案: userAnswers,
        正确答案: correctAnswers,
        题目类型: questionType === 1 ? '单选题' : questionType === 3 ? '多选题' : '非选择题'
    })
    
    // 非选择题不进行自动判断，返回null（需要人工批改）
    if (questionType !== 1 && questionType !== 3) {
        console.log('📝 非选择题，跳过自动判断')
        return null
    }
    
    // 确保答案数组存在且不为空
    if (!userAnswers || !Array.isArray(userAnswers) || userAnswers.length === 0) {
        console.log('❌ 用户答案为空')
        return 0 // 错误
    }
    
    if (!correctAnswers || !Array.isArray(correctAnswers) || correctAnswers.length === 0) {
        console.log('❌ 正确答案为空')
        return 0 // 错误
    }
    
    // 将答案转换为字符串数组进行比较
    const userAnswersStr = userAnswers.map(answer => String(answer)).sort()
    const correctAnswersStr = correctAnswers.map(answer => String(answer)).sort()
    
    console.log('🔄 答案比较:', {
        用户答案排序: userAnswersStr,
        正确答案排序: correctAnswersStr
    })
    
    // 单选题和多选题的判断逻辑
    if (questionType === 1) {
        // 单选题：答案必须完全一致
        if (userAnswersStr.length === 1 && correctAnswersStr.length === 1) {
            const isCorrect = userAnswersStr[0] === correctAnswersStr[0]
            console.log('📊 单选题判断结果:', isCorrect ? '正确' : '错误')
            return isCorrect ? 1 : 0
        } else {
            console.log('❌ 单选题答案格式错误')
            return 0
        }
    } else if (questionType === 3) {
        // 多选题：答案必须完全一致（包括数量和内容）
        if (userAnswersStr.length === correctAnswersStr.length) {
            const isCorrect = userAnswersStr.every((answer, index) => answer === correctAnswersStr[index])
            console.log('📊 多选题判断结果:', isCorrect ? '正确' : '错误')
            return isCorrect ? 1 : 0
        } else {
            console.log('❌ 多选题答案数量不匹配')
            return 0
        }
    }
    
    console.log('❌ 未知题目类型')
    return 0
}

// 提交当前题目
const handleCurrentSubmit = () => {
    if (writeState.current !== null) {
        const currentItem = allTest.value[writeState.current]
        handleSubmit(currentItem, writeState.current)
    }
}

// 监听当前题目变化
watch(() => writeState.current, (newVal, oldVal) => {
    if (newVal !== oldVal && newVal !== null) {
        // 切换题目时重置单题计时器
        writeState.itemTimer = 0
    }
})

// 🎯 新增：监听数据加载完成，自动显示报告弹窗
watch([allTest, writeState], ([newAllTest, newWriteState], [oldAllTest, oldWriteState]) => {
  // 当数据加载完成且不再处于加载状态时
  if (newAllTest && newAllTest.length > 0 && !newWriteState.loading) {
    console.log('🎯 数据加载完成，检查是否需要显示报告弹窗')
    
    // 延迟一点时间确保DOM完全渲染
    setTimeout(() => {
      showDialog()
    }, 300)
  }
}, { deep: true })

// 非选择题自主批改
const nonSelectFinish = (index: number) => {
    // 验证 index 是否在有效范围内
    if (index < 0 || index >= allTest.value.length) {
        console.error(`Invalid index: ${index}`);
        return;
    }

    // 确保 allTest.value[index] 存在且 userJson 已初始化
    const currentItem = allTest.value[index];
    if (!currentItem) {
        console.error(`Item at index ${index} is undefined`);
        return;
    }

    // 更新 userJson
    const arr = [] as string[]
    currentItem.ques.userJson.map((item: any) => {
        arr.push(item.url)
    })
    currentItem.userAnswer = arr
    writeState.showCorrect = false
    // 注意：这里不重置计时器，因为用户还需要进行自主批改
    // 计时器会在correcthandle调用handleSubmit时重置
}
const checkChange = (val: any, index: number) => {
    // 验证 index 是否在有效范围内
    if (index < 0 || index >= allTest.value.length) {
        console.error(`Invalid index: ${index}`);
        return;
    }

    // 确保 allTest.value[index] 存在且 userJson 已初始化
    const currentItem = allTest.value[index]?.ques;
    if (!currentItem) {
        console.error(`Item at index ${index} is undefined`);
        return;
    }

    // 更新 userJson
    currentItem.userJson = [val];
}
// 🎯 优化：处理图片上传列表更新，确保下一题按钮正确显示
// const handleImgList = (index: number, imgList: any) => {
//     // 验证 index 是否在有效范围内
//     if (index < 0 || index >= allTest.value.length) {
//         console.error(`Invalid index: ${index}`);
//         return;
//     }

//     // 确保 allTest.value[index] 存在且 userJson 已初始化
//     const currentItem = allTest.value[index];
//     if (!currentItem) {
//         console.error(`Item at index ${index} is undefined`);
//         return;
//     }

//     console.log('🖼️ 图片上传更新:', {
//         题目索引: index + 1,
//         题目类型: currentItem.ques?.cateName,
//         上传前userJson: currentItem.ques?.userJson,
//         上传前userAnswer: currentItem.userAnswer,
//         新图片列表: imgList
//     });

//     // 🎯 优化：同时更新 userJson 和 userAnswer
//     if (currentItem.ques) {
//         currentItem.ques.userJson = imgList;
//     }
    
//     // 将图片列表转换为 userAnswer 格式
//     if (imgList && Array.isArray(imgList)) {
//         currentItem.userAnswer = imgList.map((item: any) => {
//             return typeof item === 'string' ? item : item.url || item
//         });
//     } else {
//         currentItem.userAnswer = [];
//     }

//     console.log('✅ 图片上传更新完成:', {
//         题目索引: index + 1,
//         更新后userJson: currentItem.ques?.userJson,
//         更新后userAnswer: currentItem.userAnswer,
//         是否已作答: checkIfHasAnswer(currentItem)
//     });

//     // 🎯 优化：强制触发响应式更新
//     nextTick(() => {
//         console.log('🔄 响应式更新完成，下一题按钮状态:', {
//             题目索引: index + 1,
//             是否已作答: checkIfHasAnswer(currentItem),
//             下一题按钮应显示: checkIfHasAnswer(currentItem) ? '高亮' : '禁用'
//         });
//     });
// }
//过滤修改题干标签内容
const resetSty = function (testItem: any, sort?: number) {
  const tittle = "（" + testItem.ques.cateName + "）" +sort + "." + '&nbsp;&nbsp;' + filterContent(testItem.ques.content)
  return tittle
}
//过滤掉填空题等div标签的contenteditable属性
const filterContent = (contect: string) => {
  let highlightedResult = ""
  highlightedResult = contect.replaceAll('contenteditable="true"', " ")
  highlightedResult = highlightedResult.replaceAll("contenteditable='true'", " ")
  return highlightedResult
}
//过滤修改选项内容
const resetOptions = function (testItem: any) {
    let optionHtml = ""
    if (!testItem.ques.options) return
    testItem.ques.options.map((item: any, index: number) => {
        optionHtml += `<div class="answer-item"> ${String.fromCharCode(65 + index)}. <div style="display:inline-table;max-width:39.8125rem">${item}</div></div>`
    })
    return optionHtml
}

// 🎯 优化：下一题提交处理，包含自动答案判断
const handleSubmit = (data: any, index: number) => {
    console.log(data.ques,"datadatadatadata398")
    // 计算单题训练时长 - 修改为使用独立计时器
    let userMark = data.ques.userMark
    let userJsons = data.ques.userJson 
    console.log(userJsons,"userJsonsuserJsonsuserJsons899999999999999",userMark)
    
    // 🎯 新增：自动判断答案正确性（仅对选择题）
    if (data.ques.cate === 1 || data.ques.cate === 3) {
        // 选择题：自动判断答案是否正确
        const autoUserMark = checkAnswerCorrectness(
            userJsons, 
            data.ques.answers || [], 
            data.ques.cate
        )
        
        // 如果自动判断成功，使用自动判断结果
        if (autoUserMark !== null) {
            userMark = autoUserMark
            console.log('🤖 自动判断结果:', {
                题目类型: data.ques.cate === 1 ? '单选题' : '多选题',
                用户答案: userJsons,
                正确答案: data.ques.answers,
                判断结果: userMark === 1 ? '正确' : userMark === 2 ? '半对' : '错误',
                userMark值: userMark
            })
        } else {
            console.log('⚠️ 自动判断失败，保持原userMark值:', userMark)
        }
    } else {
        // 非选择题：不进行自动判断，保持原userMark值
        console.log('📝 非选择题，跳过自动判断，userMark保持:', userMark)
    }
    
    if (data.ques.cate != 1 && data.ques.cate != 3) {
        userJsons = data.ques.userJson.map((item: any) => { return item.url })
    }
    
    const params = {
        trainingItemId: data.trainingItemId,
        trainTime: timeState.seconds * 1000,
        trainItemTime: writeState.itemTimer * 1000, // 使用独立的单题计时器
        userJson: userJsons,
        userMark: userMark,
        noteSource: data.ques.noteSource,
        cate: data.ques.cate
    }
    
    // 🎯 优化：选择题也传递userMark参数（因为我们已经自动判断了）
    // if(data.ques.cate == 1 || data.ques.cate == 3) {
    //     delete params.userMark
    // }
    writeState.loading = true

    saveUserJsonApi(params)

    .then((res: any) => {
        // ElMessage({
        //     message: '批改成功',
        //     type: 'success'
        // })
        writeState.itemTimer = 0 // 重置单题计时器
        
        // 🎯 优化：对于选择题，使用我们自动判断的userMark；对于非选择题，使用服务器返回的值
        if (data.ques.cate === 1 || data.ques.cate === 3) {
            // 选择题：使用自动判断的userMark
            console.log('📊 选择题使用自动判断的userMark:', userMark)
        } else {
            // 非选择题：使用服务器返回的userMark
            userMark = res.data
            console.log('📊 非选择题使用服务器返回的userMark:', userMark)
        }
        
        // 手动更新allTest数据，避免调用getDetails
        // 更新当前题目的状态
        if (allTest.value[index]) {
            allTest.value[index].userMark = userMark
            allTest.value[index].submitted = true  // 标记为已提交
            console.log('题目已标记为已提交状态, 题目索引:', index + 1, 'userMark:', userMark)
            
            // 🎯 优化：正确设置用户答案，确保题号背景色显示
            if (data.ques.cate == 1 || data.ques.cate == 3) {
                // 选择题：将userJson转换为userAnswer
                allTest.value[index].userAnswer = data.ques.userJson.map((item: any) => item.toString())
                console.log(`✅ 选择题${index + 1}答案已保存:`, allTest.value[index].userAnswer)
            } else {
                // 非选择题：确保userAnswer包含上传的图片URL
                if (data.ques.userJson && data.ques.userJson.length > 0) {
                    allTest.value[index].userAnswer = data.ques.userJson.map((item: any) => {
                        return typeof item === 'string' ? item : item.url
                    })
                    console.log(`✅ 非选择题${index + 1}图片答案已保存:`, allTest.value[index].userAnswer)
                }
                writeState.showCorrect = true
            }
        }

        // 🎯 强制触发题号背景色更新
        nextTick(() => {
            console.log('🎨 题目提交后，触发题号背景色更新检查')
            const hasAnswer = checkIfHasAnswer(allTest.value[index])
            const colorStatus = setClass1(allTest.value[index], index)
            console.log(`题目${index + 1}状态更新:`, {
                题目类型: allTest.value[index].ques?.cateName,
                是否已答题: hasAnswer,
                是否已提交: allTest.value[index].submitted,
                是否已批改: allTest.value[index].userMark !== null,
                题号背景色: colorStatus,
                userAnswer长度: allTest.value[index].userAnswer?.length || 0
            })
            
            // 🎯 新增：触发进度条更新检查
            const currentProgress = progressPercentage.value
            console.log('🚀 saveUserJsonApi成功，进度条将更新:', {
                当前进度: currentProgress + '%',
                触发原因: 'saveUserJsonApi成功',
                题目索引: index + 1
            })
            
            // 🎯 调试：显示所有题目的最新状态
            setTimeout(() => {
                debugQuestionStatus()
            }, 50)
        })

        // 🎯 优化：智能查找下一个未作答的题目
        let nextQuestionIndex:any = null
        
        // 如果是通过"下一题"按钮触发的提交，使用指定的下一题索引
        if (writeState.fromNextButton && writeState.expectedNextIndex !== undefined) {
            nextQuestionIndex = writeState.expectedNextIndex
            console.log('使用下一题按钮指定的索引:', nextQuestionIndex)
            // 清除标记
            writeState.fromNextButton = false
            writeState.expectedNextIndex = undefined
        } else {
            // 🎯 优化：查找下一个未作答的题目（而不是未批改的题目）
            nextQuestionIndex = findNextUnansweredQuestion(index)
            console.log('使用智能查找的下一题索引:', nextQuestionIndex)
        }
        
        // 更新当前题目索引
        writeState.current = nextQuestionIndex

        // 🎯 切换到下一题时，设置答题区域显示模式
        if (nextQuestionIndex !== null) {
            nextTick(() => {
                updateAnswerAreaDisplay(nextQuestionIndex)
                console.log(`🔄 已切换到题目${nextQuestionIndex + 1}，显示模式已更新`)
            })
        }

        // 如果所有题目都已完成，停止计时器并启用提交按钮
        if (nextQuestionIndex === null) {
            if (timer !== null) {
                clearInterval(timer)
                timer = null
            }
            writeState.disabled = false
        }

        writeState.loading = false
        console.log('writeState.current', writeState.current)
    })
    .catch(() => {
        writeState.loading = false
    })
}
// 🎯 优化：完成提交，确保最后一题也调用saveUserJson接口
const submit = async () => {
    console.log("点击完成",detailData)
    console.log("点击完成123")
    
    // 🎯 优化：如果当前还有题目未提交，先提交当前题目
    if (writeState.current !== null) {
        const currentItem = allTest.value[writeState.current]
        const hasAnswer = checkIfHasAnswer(currentItem)
        
        if (hasAnswer) {
            console.log('🔄 检测到最后一题已作答，先提交当前题目')
            try {
                // 直接调用handleSubmit，但不等待完成
                handleSubmit(currentItem, writeState.current)
                
                // 等待一小段时间让提交完成
                await new Promise(resolve => setTimeout(resolve, 1000))
                
                console.log('✅ 最后一题提交完成')
            } catch (error) {
                console.error('❌ 最后一题提交失败:', error)
            }
        } else {
            console.log('⚠️ 最后一题未作答，跳过提交')
        }
    }
    
    // 🎯 新增：检查题目类型，判断是否有主观题需要批改
    const subjectiveQuestions = allTest.value.filter((item: any) => {
        const cate = item.ques?.cate
        return cate !== 1 && cate !== 3 // 非选择题（非单选题和多选题）
    })
    
    writeState.subjectiveNum = subjectiveQuestions.length
    
    console.log('🔍 题目类型检查:', {
        总题数: allTest.value.length,
        选择题数量: allTest.value.filter(item => item.ques?.cate === 1 || item.ques?.cate === 3).length,
        主观题数量: subjectiveQuestions.length,
        主观题类型: subjectiveQuestions.map(item => ({
            题目: allTest.value.indexOf(item) + 1,
            类型: item.ques?.cateName,
            cate: item.ques?.cate
        }))
    })
    
    // 🎯 优化：确保点击完成按钮后显示最后一题
    writeState.current = allTest.value.length - 1
    console.log('🎯 点击完成按钮，设置当前题目为最后一题:', writeState.current + 1)
    
    // 🎯 新增：如果有主观题，显示批改弹窗
    if (subjectiveQuestions.length > 0) {
        console.log('📝 检测到主观题，显示批改弹窗')
        writeState.showDialog = true
        writeState.btnloading = false
        return // 停止执行，等待用户批改
    }
    
    console.log('✅ 所有题目都是选择题，直接进入正常提交流程')
    writeState.btnloading = true
    
    // 执行提交流程
    await executeSubmit()
}

// 🎯 新增：执行提交流程的独立函数
const executeSubmit = async () => {
    // 🎯 优化：构建提交数据，确保包含所有题目的最新状态
    const items = allTest.value.map((item: any, index: number) => {
        const itemData = {
            // cate: item.ques.cate,
            trainingItemId: item.trainingItemId,
            userJson: item.userAnswer || [],
            userMark: item.userMark,
            trainItemTime: item.trainTime || 0
        }
        
        console.log(`📊 题目${index + 1}提交数据:`, {
            题目类型: item.ques?.cate === 1 ? '单选题' : item.ques?.cate === 3 ? '多选题' : '非选择题',
            用户答案: itemData.userJson,
            批改结果: itemData.userMark,
            训练时长: itemData.trainItemTime,
            是否已提交: item.submitted
        })
        
        return itemData
    })
    
    // 🎯 优化：在第1645行打印最后一条数据存储状态
    console.log('📋 所有题目提交数据汇总:', {
        总题数: items.length,
        已提交题数: items.filter(item => item.userMark !== null).length,
        未提交题数: items.filter(item => item.userMark === null).length,
        最后一条数据: items[items.length - 1],
        数据存储状态: items[items.length - 1]?.userMark !== null ? '✅ 已存储成功' : '❌ 没有存储成功'
    })

        // 确保使用最新的计时值
        const finalTrainTime = timer !== null ? timeState.seconds * 1000 : convertTimeToMilliseconds();
        console.log(queryData.bookId,"queryData.bookIdqueryData.bookIdqueryData.bookIdqueryData.bookId")
        const params = {
            trainingId: detailData.trainingId,   //训练id
            trainTime: finalTrainTime, // 使用最新的时间  训练时长
            status: 2,   //训练状态  1:未完成  2:已完成
            bookId: queryData.bookId,   //书籍id
            chapterId:queryData.chapterId,   //章节id
            level:queryData.level,   //级别  1:青铜  2:白银  3:黄金 4:钻石
            items:items
        }
        const params2 = {
            trainingId: detailData.trainingId,   //训练id
            trainTime: finalTrainTime, // 使用最新的时间  训练时长
            status: 2,   //训练状态  1:未完成  2:已完成
        }

        // 根据source来源调用不同的API
        if(queryData.source === 'ripe') {
            console.log('调用知识点标熟API - saveTrainingApi')
            // 知识点标熟答题保存
            saveTrainingApi(params2)
            .then((res: any) => {
                console.log(res,"saveTrainingApi - 知识点标熟保存成功")
                if (timer !== null) { // 添加类型安全检查
                    clearInterval(timer)
                    timer = null // 确保timer被清空
                }
                // 重置计时器
                writeState.itemTimer = 0

                writeState.btnloading = false
                return;
                router.push({
                    path: '/ai_percision/foundation_report',
                    query: {
                        data: dataEncrypt({
                        reportId: detailData.trainingId,
                        pageSource: '1',
                        source: queryData.source, // 传递source标识
                        type:queryData.type,
                        subject:queryData.subject,
                        bookId:queryData.bookId,
                        subjectEn:queryData.subjectEn,
                        curChapterId:queryData.curChapterId
                        }),
                    }
                })
            })
            .catch(() => {
                writeState.btnloading = false
            })
        } else {
            console.log('调用精准学API - savePointTrainingApi')
            // 精准学答题保存
            savePointTrainingApi(params)
            .then((res: any) => {
                console.log(res,"savePointTrainingApi - 精准学保存成功")
                if (timer !== null) { // 添加类型安全检查
                    clearInterval(timer)
                    timer = null // 确保timer被清空
                }
                // 重置计时器
                writeState.itemTimer = 0

                writeState.btnloading = false
                return;
                
                router.push({
                    path: '/ai_percision/foundation_report',
                    query: {
                        data: dataEncrypt({
                            reportId: detailData.trainingId,
                            pageSource: '1',
                            type:queryData.type,
                            subject:queryData.subject,
                            subjectEn:queryData.subjectEn,
                            curChapterId:queryData.curChapterId,
                            bookId:queryData.bookId,
                            selectedChildIds:queryData.selectedChildIds,
                            learn:queryData.learn,
                        }),
                    }
                })
            })
            .catch(() => {
                writeState.btnloading = false
            })
        }

        // saveToIntroduceddApi(params)
        // .then((res: any) => {
        //     if (timer !== null) { // 添加类型安全检查
        //         clearInterval(timer)
        //         timer = null // 确保timer被清空
        //     }
        //     // 重置计时器
        //     writeState.itemTimer = 0

        //     writeState.btnloading = false
        //     router.push({
        //         path: '/ai_percision/entrance_assessment/test_report',
        //         query: {
        //             data: dataEncrypt({
        //             reportId: detailData.trainingId,
        //             pageSource: '1'
        //             }),
        //         }
        //     })
        // })
        // .catch(() => {
        //     writeState.btnloading = false
        // })

}
// 获取训练报告数据
// const getTringDetail = () => {
//     // 生成诊断报告弹窗暂未加上

//     getDetailsApi({trainingId: writeState.trainingId}).then((res: any) => {
//         if (res.code == 200) {
//             const data = res.data
//             localStorage.setItem('diagnosticReport', JSON.stringify(data))
//             writeState.btnloading = false
//             writeState.jfNum = res.data.integral
//             writeState.jfShow = true
//             writeState.jfHide = false
//             setTimeout(()=>{
//                 router.push({
//                     path: '/ai_percision/knowledge_graph_detail/training_report',
//                     query: {
//                         data: dataEncrypt({
//                             sourceId: queryData.sourceId,
//                             showStep: '1'
//                         })
//                     }
//                 })
//             },3000)
//         }
//     }).catch((error) => {
//     })
// }
const handleAnalysis = () => {
    router.push({
        path: '/ai_percision/knowledge_graph_detail/paper_analysis'
    })
}

// 🎯 新增：报告弹窗相关方法
const toPoint = (val: any) => {
    router.push({
        name: 'TeachRoomTeachVideo',
        query: {
            id: val.pointId,
            pointName: val.ques.pointVos[0].name,
            source: 'analysis',
            subject: queryData.subject,
        }
    })
}

const goLearning = (val: any) => {
    router.push({
        name: 'TeachRoomTeachVideo',
        query: {
            id: val.pointId,
            pointName: val.pointName,
            source: 'analysis',
            subject: queryData.subject,
        }
    })
}

// 获取答题状态的CSS类名
const getAnswerStatusClass = (status: string | number) => {
    if (status === '正确' || status === 1 || status === '对') {
        return 'status-correct'
    } else if (status === '错误' || status === 0 || status === '错') {
        return 'status-wrong'
    }
    return ''
}

// 获取等级信息（名称和图片）
const getLevelInfo = (level: number | string) => {
    const levelNum = Number(level)
    switch (levelNum) {
        case 1:
            return {
                name: '青铜',
                image: new URL('@/assets/img/percision/training/qingt.png', import.meta.url).href
            }
        case 2:
            return {
                name: '白银',
                image: new URL('@/assets/img/percision/training/baiyin.png', import.meta.url).href
            }
        case 3:
            return {
                name: '黄金',
                image: new URL('@/assets/img/percision/training/huangjin.png', import.meta.url).href
            }
        case 4:
            return {
                name: '钻石',
                image: new URL('@/assets/img/percision/training/zuanshi.png', import.meta.url).href
            }
        default:
            return {
                name: '青铜',
                image: new URL('@/assets/img/percision/training/qingt.png', import.meta.url).href
            }
    }
}

function secondsToHMS(seconds: number) {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    // 补零操作，确保两位数显示
    const pad = (num: number) => num.toString().padStart(2, '0');
    timeState.seconds = pad(secs)
    timeState.minutes = pad(minutes)
    timeState.hours = pad(hours)
}

// 显示报告弹窗
const showReportDialog = () => {
    dialogState.visible2 = true
}

// 🎯 新增：导航相关方法
const goToPreviousQuestion = () => {
    if (writeState.current > 0) {
        writeState.current--
        console.log('🎯 切换到上一题:', writeState.current)
    }
}

// const goToNextQuestion = () => {
//     if (writeState.current < allTest.value.length - 1) {
//         writeState.current++
//         console.log('🎯 切换到下一题:', writeState.current)
//     }
// }

const finishTest = () => {
    console.log('🎯 完成测试')
    ElMessage.success('测试完成！')
    // 可以在这里添加完成测试后的逻辑
}

// 处理选项点击
const handleOptionClick = (optIndex: number) => {
    if (!allTest.value[writeState.current]?.ques) return
    
    const currentQuestion = allTest.value[writeState.current]
    if (currentQuestion.submitted === true || currentQuestion.userMark !== null) return
    
    // 初始化 userJson 数组
    if (!currentQuestion.ques.userJson) {
        currentQuestion.ques.userJson = []
    }
    
    // 切换选项选择状态
    const userJson = currentQuestion.ques.userJson
    const index = userJson.indexOf(optIndex)
    
    if (index > -1) {
        // 如果已选中，则取消选择
        userJson.splice(index, 1)
    } else {
        // 如果未选中，则添加选择
        userJson.push(optIndex)
    }
    
    console.log('🎯 选项点击:', optIndex, '当前选择:', userJson)
}

// 处理图片上传
const handleImgList = (imgList: string[]) => {
    if (allTest.value[writeState.current]) {
        allTest.value[writeState.current].userAnswer = imgList
        console.log('🎯 图片上传完成:', imgList)
    }
}

// 切换答案显示
const togAnswer = (question: any, showAnalyse: boolean) => {
    question.showAnalyse = showAnalyse
    console.log('🎯 切换答案显示:', showAnalyse)
}

// 🎯 优化：增强题号颜色设置方法的安全性
const setClass1 = (item: any, index: number) => {
    try {
        let classState = ""
        
        // 🎯 优化：添加参数验证
        if (!item || typeof item !== 'object') {
            console.warn('⚠️ setClass1: item 参数无效:', item, 'index:', index);
            return "";
        }
        
        if (typeof index !== 'number' || index < 0) {
            console.warn('⚠️ setClass1: index 参数无效:', index);
            return "";
        }
        
        // 已批改：显示批改结果颜色
        const userMark = item.userMark;
        if (userMark !== null && userMark !== undefined && typeof userMark === 'number') {
            if (userMark === 0) {
                classState = "red"      // 错误
            } else if (userMark === 1) {
                classState = "green"    // 正确
            } else if (userMark === 2) {
                classState = "yellow"   // 半对
            } else {
                console.warn('⚠️ setClass1: 未知的userMark值:', userMark);
                classState = ""
            }
        } else {
            // 未批改的题目：根据答题状态显示
            const hasAnswer = checkIfHasAnswer(item)
            
            if (hasAnswer) {
                // 已作答但未批改：显示蓝色
                classState = "blue"
            } else if (writeState.current === index) {
                // 当前题目：显示蓝色高亮
                classState = "blue"
            } else {
                // 未作答：显示默认状态（白色）
                classState = ""
            }
        }
        
        return classState;
    } catch (error) {
        console.error('❌ setClass1 执行出错:', error, 'item:', item, 'index:', index);
        return "";
    }
}

// 🎯 优化：增强检查题目是否已作答的辅助方法安全性
const checkIfHasAnswer = (item: any) => {
    try {
        // 🎯 优化：添加更严格的类型和空值检查
        if (!item || typeof item !== 'object') {
            console.warn('⚠️ checkIfHasAnswer: item 参数无效:', item);
            return false;
        }
        
        if (!item.ques || typeof item.ques !== 'object') {
            console.warn('⚠️ checkIfHasAnswer: item.ques 不存在或无效:', item);
            return false;
        }
        
        // 🎯 优化：安全地检查题目类型
        const cate = item.ques.cate;
        if (typeof cate !== 'number') {
            console.warn('⚠️ checkIfHasAnswer: 题目类型无效:', cate);
            return false;
        }
        
        const isChoiceQuestion = cate === 1 || cate === 3;
        
        if (isChoiceQuestion) {
            // 选择题：检查userJson是否有选择
            const userJson = item.ques.userJson;
            const hasChoiceAnswer = userJson && 
                                  Array.isArray(userJson) && 
                                  userJson.length > 0 &&
                                  userJson.every(answer => typeof answer === 'number' || typeof answer === 'string');
            return hasChoiceAnswer;
        } else {
            // 非选择题：检查userAnswer是否有上传的图片或文件
            const userAnswer = item.userAnswer;
            const hasUploadAnswer = userAnswer && 
                                  Array.isArray(userAnswer) && 
                                  userAnswer.length > 0 &&
                                  userAnswer.every(answer => typeof answer === 'string' && answer.trim().length > 0);
            
            // 额外检查：如果userAnswer为空但userJson有值，也认为已作答
            const userJson = item.ques.userJson;
            const hasUserJsonAnswer = userJson && 
                                    Array.isArray(userJson) && 
                                    userJson.length > 0;
            
            return hasUploadAnswer || hasUserJsonAnswer;
        }
    } catch (error) {
        console.error('❌ checkIfHasAnswer 执行出错:', error, 'item:', item);
        return false;
    }
}

// 🎯 优化：增强切换题目方法的安全性
const switchQuestion = (index: number) => {
    try {
        // 🎯 优化：参数验证
        if (typeof index !== 'number' || index < 0) {
            console.error('❌ switchQuestion: 无效的题目索引:', index)
            return
        }
        
        // 🎯 优化：检查题目是否存在
        if (!allTest.value || !Array.isArray(allTest.value) || index >= allTest.value.length) {
            console.error('❌ switchQuestion: 题目不存在，索引:', index, '总题数:', allTest.value?.length || 0)
            return
        }
        
        const targetQuestion = allTest.value[index]
        if (!targetQuestion || typeof targetQuestion !== 'object') {
            console.error('❌ switchQuestion: 目标题目数据无效:', targetQuestion)
            return
        }
        
        // 🎯 优化：安全切换
        const oldIndex = writeState.current
        writeState.current = index
        
        console.log('🎯 切换题目成功:', {
            从: oldIndex !== null ? `题目${oldIndex + 1}` : '无',
            到: `题目${index + 1}`,
            题目类型: targetQuestion?.ques?.cateName || '未知',
            是否已作答: checkIfHasAnswer(targetQuestion)
        })
    } catch (error) {
        console.error('❌ switchQuestion 执行出错:', error, 'index:', index)
    }
}

// 🎯 新增：测试报告弹窗功能
const testReportDialog = () => {
    // 设置测试数据
    reportState.correctRate = 85.5
    reportState.quesCount = 20
    reportState.trainTime = 1800 // 30分钟
    
    // 设置测试知识点数据
    pointItems.value = [
        {
            pointName: '函数的概念与性质',
            passingStatus: '已过关',
            level: 2,
            correctRate: 90.0,
            pointId: 'test-point-1'
        },
        {
            pointName: '三角函数',
            passingStatus: '未过关',
            level: 1,
            correctRate: 75.0,
            pointId: 'test-point-2'
        },
        {
            pointName: '立体几何',
            passingStatus: '已过关',
            level: 3,
            correctRate: 95.0,
            pointId: 'test-point-3'
        }
    ]
    
    // 设置测试题目数据
    quesItems.value = [
        { quesNum: 1, trainTime: 45000, answeringStatus: '正确', isTimeout: false },
        { quesNum: 2, trainTime: 30000, answeringStatus: '错误', isTimeout: false },
        { quesNum: 3, trainTime: 60000, answeringStatus: '正确', isTimeout: true },
        { quesNum: 4, trainTime: 25000, answeringStatus: '正确', isTimeout: false },
        { quesNum: 5, trainTime: 40000, answeringStatus: '错误', isTimeout: false }
    ]
    
    // 显示弹窗
    showReportDialog()
}
</script>
<style lang="scss" scoped>
.container {
    display: flex;
    position: relative;
    width: 100%;
    background-image: url('@/assets/img/percision/training/dtbj.png');
    background-size: 100%;
    background-repeat: no-repeat;
    padding-top: 60px;
    /* height: 700px; */
    .top-nav{
        background-image: url('@/assets/img/percision/training/top_back.png');
        background-size: 100%;
        background-repeat: no-repeat;
        display: flex;
        height: 95px;
        align-items: center;
        position: relative;
        
        .exit-btn{
            width: 100px;
            height: 31px;
            margin-left: 30px;
            cursor: pointer;
        }
        
        .test-report-btn {
            margin-left: auto;
            margin-right: 30px;
        }
        
        /* 🎯 新增：答题进度条样式 */
        .progress-container {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 10px;
            
            .progress-bg {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(222, 229, 234, 1);
                border-radius: 0;
            }
            
            .progress-fill {
                position: absolute;
                top: 0;
                left: 0;
                height: 100%;
                background-color: rgba(255, 196, 159, 1);
                border-radius: 0;
                transition: width 0.3s ease;
                z-index: 1;
            }
            
            .progress-icon {
                position: absolute;
                top: -15px; /* 向上偏移，使图标在进度条上方 */
                width: 40px;
                height: 40px;
                transform: translateX(-50%); /* 居中对齐 */
                transition: left 0.3s ease-in-out;
                z-index: 2;
                pointer-events: none; /* 防止影响其他元素的点击 */
                
                /* 添加一些视觉效果 */
                filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
            }
        }
    }
    .five-step-box {
        position: absolute;
        right: -8.75rem;
        top: 11.25rem;
    }
    .left {
        flex: 1;
        .test-content {
            /* height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height)); */
            box-sizing: border-box;
            overflow-y: auto;
                background: #ffffff;
            /* border-radius: 12px; */
            border-radius: 0 0 0px 20px;
            
            .single-question-container {
                padding:20px;
                height: 100%;
                display: flex;
                flex-direction: column;
                
                
                .question-cont {
                    height: 300px;
                    overflow-y: auto;
                    padding-right: 10px;
                    margin-bottom: 20px;
                    
                    /* 自定义滚动条样式 */
                    &::-webkit-scrollbar {
                        width: 6px;
                    }
                    
                    &::-webkit-scrollbar-track {
                        background: #f1f1f1;
                        border-radius: 3px;
                    }
                    
                    &::-webkit-scrollbar-thumb {
                        background: #c1c1c1;
                        border-radius: 3px;
                        
                        &:hover {
                            background: #a8a8a8;
                        }
                    }
                }
                
                .question-header {
                    display: flex;
                    align-items: center;
                    margin-bottom: 4px;
                    
                    .question-type {
                        background: rgba(238, 242, 253, 1);
                        color: rgba(90, 133, 236, 1);
                        padding: 0.25rem 0.75rem;
                        border-radius: 4px;
                        font-size: 16px;
                        font-weight: 700;
                        margin-right: 1rem;
                        flex-shrink: 0;
                    }
                    
                    .question-number {
                        font-size: 20px;
                        font-weight: 700;
                        color: #1f2937;
                        flex-shrink: 0;
                    }
                }
                
                .question-content {
                    font-size: 1rem;
                    line-height: 1.6;
                    color: rgba(42, 43, 42, 1);
                    margin-bottom: 10px;
                    flex: 1;
                }
                
                .question-options {
                    margin-bottom: 1rem;
                    
                    .option-item {
                        display: flex;
                        align-items: flex-start;
                        padding: 10px 15px;
                        border-radius: 8px;
                        
                        .option-label {
                            font-weight: 600;
                            margin-right: 0.75rem;
                            color: #374151;
                            min-width: 1.5rem;
                        }
                        
                        .option-content {
                            flex: 1;
                            color: #374151;
                        }
                    }
                }
                
                .answer-section {
                    background-image: url('@/assets/img/percision/training/ztqy.png');
                    background-size: 100%;
                    background-repeat: no-repeat;
                    min-height: 248px;
                    position: relative;
                    padding: 20px;
                    box-sizing: border-box;
                
                    
                    .choice-answer-area {
                        .answer-options {
                            margin: 20px;
                            display: flex;
                            flex-wrap: wrap;
                            gap: 1rem;
                            
                            .answer-option {
                                display: flex;
                                align-items: center;
                                padding: 0.75rem 1.5rem;
                                border: 2px solid #e5e7eb;
                                border-radius: 8px;
                                cursor: pointer;
                                transition: all 0.2s ease;
                                background: #ffffff;
                                
                                &:hover:not(.disabled) {
                                    border-color: #3b82f6;
                                    background: #f0f9ff;
                                }
                                
                                &.disabled {
                                    cursor: not-allowed;
                                    opacity: 0.6;
                                    background: #f5f5f5;
                                    border-color: #d1d5db;
                                    
                                    &:hover {
                                        border-color: #d1d5db;
                                        background: #f5f5f5;
                                    }
                                }
                                
                                &.selected {
                                    border-color: #3b82f6;
                                    background: #dbeafe;
                                    
                                    .option-checkbox {
                                        background: #3b82f6;
                                        border-color: #3b82f6;
                                        
                                        .checkmark {
                                            color: white;
                                        }
                                    }
                                }
                                
                                .option-checkbox {
                                    width: 20px;
                                    height: 20px;
                                    border: 2px solid #d1d5db;
                                    border-radius: 4px;
                                    margin-right: 0.75rem;
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                    transition: all 0.2s ease;
                                    
                                    .checkmark {
                                        font-size: 12px;
                                        font-weight: bold;
                                    }
                                }
                                
                                .option-letter {
                                    font-weight: 600;
                                    color: #374151;
                                }
                            }
                        }
                    }
                }
                
                .question-footer {
                    margin-top: auto;
                    padding-top: 2rem;
                    
                    .navigation-buttons {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        
                        .submit-btn {
                            padding: 0.75rem 2rem;
                            background: #10b981;
                            color: white;
                            border: none;
                            border-radius: 6px;
                            cursor: pointer;
                            font-weight: 500;
                            transition: all 0.2s ease;
                            
                            &:hover {
                                background: #059669;
                            }
                        }
                    }
                }
            }
        }
    }
    .right {
        padding: 1.125rem 0 0 0 ;
        width: 280px;
        flex-shrink: 0;
        background: #f5f8ff;
        box-sizing: border-box;
        border-radius: 0 0px 20px 0px;
        background: url('@/assets/img/percision/training/dtbjt.png') no-repeat;
        background-size: 100% 100%;
        /* height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height)); */
        .time-box {
            /* display: flex; */
            align-items: center;
            color: #2a2b2a;
            font-size: 1.875rem;
            font-weight: 700;
            padding-left: .625rem;
            padding-bottom: 1.25rem;
            border-bottom: .0625rem dashed #eaeaea;
            .time-text {
                display: flex;
                align-items: center;
                margin-left: 10px;
                font-size: 16px;
                font-weight: 700;
                margin-bottom: 20px;
                img{
                    width: 21px;
                    height: 21px;
                    margin-right: 8px;
                }
            }
             .time-bg{
                 background: url('@/assets/img/percision/training/timebg.png') no-repeat;
                 background-size: 100% 100%;
                 background-position: center;
                 width: 220px;
                 height: 60px;
                 display: flex;
                 align-items: center;
                 justify-content: center;
                 margin: 0 auto;
                .time-number {
                    width: 36px;
                    height: 36px;
                    line-height: 36px;
                    font-size: 20px;
                    font-weight: 700;
                    text-align: center;
                    border-radius: .25rem;
                    border: .0625rem solid #eaeaea;
                    background: #5A85EC;
                    margin: 0 .625rem;
                    color: #fff;
                }
            }
           
        }
        .test-number-box {
            /* height: calc(100vh - var(--v3-navigationbar-height) - var(--v3-breadcrumb-height) - 8.125rem); */
            box-sizing: border-box;
            padding: 1.25rem .625rem 0 0;
            display: flex;
            flex-direction: column;
            margin-left: 10px;
            .question-nav-title {
                font-size: 1rem;
                font-weight: 600;
                color: #374151;
                margin-bottom: 1rem;
                padding-left: .625rem;
                display: flex;
                align-items: center;
                img{
                    width: 21px;
                    height: 21px;
                    margin-right: 8px;
                }
            }
            
            .question-nav-grid {
                height: 248px;
                overflow-y: auto;
                overflow-x: hidden;
                padding-right: 8px;
                
                /* 自定义滚动条样式 */
                &::-webkit-scrollbar {
                    width: 6px;
                }
                
                &::-webkit-scrollbar-track {
                    background: #f1f1f1;
                    border-radius: 3px;
                }
                
                &::-webkit-scrollbar-thumb {
                    background: #c1c1c1;
                    border-radius: 3px;
                    
                    &:hover {
                        background: #a8a8a8;
                    }
                }
                
                /* Firefox 滚动条样式 */
                scrollbar-width: thin;
                scrollbar-color: #c1c1c1 #f1f1f1;
                
            .test-number-item {
                display: inline-block;
                margin-left: 20px;
                margin-bottom: .625rem;
                border-radius: .25rem;
                width: 2.5rem;
                height: 2.5rem;
                line-height: 2.5rem;
                text-align: center;
                color: #2a2b2a;
                font-size: 1rem;
                font-weight: 400;
                border: .0625rem solid #eaeaea;
                background: #fff;
                box-sizing: border-box;
                cursor: pointer;
                transition: all 0.2s ease;
                
                &:hover {
                    background: #e5e7eb;
                    border-color: #9ca3af;
                }
            }
            }
            
            .submit-section {
                margin-top: 1rem;
                padding-top: 1rem;
                padding: 0 10px;
                display: flex;
                flex-direction: column;
                align-items: center;
                margin-bottom: 10px;
                border-bottom: 1px dashed rgba(221, 221, 221, 1);
                .next-question-btn,
                .finish-btn {
                    width: 136px;
                    height: 50px;
                    border: none;
                    border-radius: 20px;
                    background: rgba(42, 43, 42, 1);
                    color: #ffffff;
                    font-size: 18px;
                    font-weight: 700;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    margin-bottom: 10px;
                    
                    &:hover:not(.disabled) {
                        background: rgba(90, 133, 236, 1);
                    }
                    
                    &.disabled {
                        background: rgba(221, 221, 221, 1);
                        color: rgba(153, 153, 153, 1);
                        cursor: not-allowed;
                        
                        &:hover {
                            background: rgba(221, 221, 221, 1);
                            color: rgba(153, 153, 153, 1);
                        }
                    }
                }
                
                .finish-btn {
                    background: #10b981;
                    
                    &:hover {
                        background: #059669;
                    }
                }
            }
            
            .color-type {
                display: flex;
                justify-content: center;
                align-items: center;
                gap: 15px;
                padding: 14px 10px;
                
                .legend-item {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    gap: 5px;
                    
                    .legend-square {
                        width: 14px;
                        height: 14px;
                        border-radius: 2px;
                        border-radius: 4px;
                        &.undone {
                            background: rgba(255, 255, 255, 1);
                            border: 1px solid #eaeaea;
                        }
                        
                        &.done {
                            background: rgba(90, 133, 236, 1);
                        }
                        
                        &.correct {
                            background: rgba(0, 201, 163, 1);
                        }
                        
                        &.half-correct {
                            background: rgba(241, 190, 33, 1);
                        }
                        
                        &.wrong {
                            background: rgba(221, 42, 42, 1);
                        }
                    }
                    
                    span {
                        font-size: 12px;
                        color: #666666;
                        font-weight: 400;
                        text-align: center;
                    }
                }
            }
            }
        }
        
            .disabled {
                background: #bebebe;
                cursor: not-allowed;
            }
            .blue {
                background: #5a85ec!important;
                color: #ffffff!important;
            }
            .submitted {
                background: rgba(90, 133, 236, 1)!important;
                color: #ffffff!important;
            }
            .red {
                background: #dd2a2a!important;
                color: #ffffff!important;
            }
            .green {
                background: #00c9a3!important;
                color: #ffffff!important;
            }
            .yellow {
                background: #f1be21!important;
                color: #ffffff!important;
            }
            .size285 {
                width: 17.8125rem;
                height: 2.75rem;
                font-size: 1rem;
                font-weight: 700;
                margin-left: .625rem;
                margin-top: 1.25rem;
                img {
                    width: 1rem;
                    height: 1rem;
        }
    }
}
.icon-btn {
    border-radius: .25rem;
    background: #00c9a3;
    color: #ffffff;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    img {
        margin-right: .3125rem;
    }
}
.answer-img-box {
    /* padding-left: 1.875rem; */
    /* padding-top: 1.25rem; */
    .answer-img {
        width: 144px;
        height: 144px;
        /* border-radius: .25rem; */
        /* margin-right: .625rem; */
    }
}
:deep(.el-checkbox-group) {
    .el-checkbox {
        width: 6.25rem;
        height: 3.125rem;
        margin-right: 1.25rem;
        display: inline-flex;
        justify-content: center;
    }
    .is-checked {
        .el-checkbox__inner {
            background: #5a85ec;
            border: .0625rem solid #5a85ec;
        }
        .el-checkbox__label {
            color: #5a85ec;
        }
    }
    .el-checkbox__inner {
        &:hover {
            border: .0625rem solid #5a85ec;
        }
    }
}
.show-analyse {
    width: 100%;
    background: #fef8e9;
    padding-left: 1.875rem;
    height: 2.1875rem;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    color: #666666;
    font-size: .75rem;
    font-weight: 400;
    margin-top: 1.25rem;
    span {
        margin-left: .375rem;
    }
}
.analyse {
    padding: .625rem 1.875rem;
    letter-spacing: .125rem;
    background: #fef8e9;
    div {
      margin-bottom: .625rem;
    }
}
.flex-sty {
  display: flex;
  font-size: .875rem;
  align-items: baseline;
  div {
    max-width: 52.375rem;
    line-height: 1.0625rem;
  }
  span {
    text-wrap: nowrap;
    font-weight: 700;
    letter-spacing: normal;
  }
}
.paper-content-ques {
    margin-top: 20px;
    margin-left: 20px;
    display: flex;
    flex-direction: column;
    gap: 10px;
    
    /* 覆盖组件内的按钮样式 */
    :deep(.camera-container) {
        padding-right: 0;
        flex-direction: row;
        gap: 10px;
        
        .upload-btn {
            width: 101px !important;
            height: 37px !important;
            padding: 0 !important;
            /* border: 1px solid rgba(90, 133, 236, 1) !important; */
            border-radius: 4px !important;
            background: rgba(238, 242, 253, 1)!important;
            color: rgba(90, 133, 236, 1) !important;
            border: none;
            font-size: 14px !important;
            font-weight: 400 !important;
            margin-bottom: 0 !important;
            margin-left: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            
            &:hover {
                background: rgba(90, 133, 236, 0.1) !important;
            }
        }
    }
    
    :deep(.upload-container) {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        align-items: flex-start;
        position: relative;
        
        .img-box {
            width: 140px !important;
            height: 140px !important;
            margin-right: 0 !important;
        }
        
        .upload-box {
            position: static !important;
            padding: 20px;
            
            .upload-btn {
                width: 101px !important;
                height: 37px !important;
                padding: 0 !important;
                border-radius: 4px !important;
                background: rgba(238, 242, 253, 1) !important;
                color: rgba(90, 133, 236, 1) !important;
                font-size: 14px !important;
                font-weight: 400 !important;
                position: static !important;
                width: 101px !important;
                display: flex;
                align-items: center;
                justify-content: center;
                
                &:hover {
                    background: rgba(90, 133, 236, 0.1) !important;
                }
            }
        }
    }
    
    .upload-images {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        
        .upload-image-item {
            width: 140px;
            height: 140px;
            border: 1px solid rgba(221, 221, 221, 1);
            border-radius: 4px;
            position: relative;
            overflow: hidden;
            
            img {
                width: 100%;
                height: 100%;
                object-fit: cover;
            }
        }
    }
}
.blue-btn {
    width: 7.625rem;
    height: 2.375rem;
    line-height: 2.375rem;
    text-align: center;
    border-radius: 1.1875rem;
    background: #00c9a3;
    color: #ffffff;
    font-size: 1rem;
    cursor: pointer;
    margin-right: .625rem;
    font-weight: 400;
}
.black-text {
    color: black!important;
}
.red-border {
    border: .0625rem solid #dd2a2a;
    color: black!important;
    .squre {
        background: #dd2a2a!important;
    }
    .wrong-selected {
        border: .0625rem solid #dd2a2a!important;
        background: #fce9e9!important;
        div {
            background-image: url(@/assets/img/percision/wrong-check.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
    }
}
.green-border {
    color: black!important;
    border: .0625rem solid #00C9A3;
    .squre {
        background: #00C9A3!important;
    }
    .selected {
        border: .0625rem solid #00c9a3!important;
        background: #e5f9f6!important;
        div {
            background-image: url(@/assets/img/percision/right-check.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
    }
}
.yellow-border {
    color: black!important;
    border: .0625rem solid #f1be21;
    .squre {
        background: #f1be21!important;
    }
    .selected {
        border: .0625rem solid #00c9a3!important;
        background: #e5f9f6!important;
        div {
            background-image: url(@/assets/img/percision/right-check.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
    }
    .wrong-selected {
        border: .0625rem solid #dd2a2a!important;
        background: #fce9e9!important;
        div {
            background-image: url(@/assets/img/percision/wrong-check.png);
            background-size: 100% 100%;
            background-repeat: no-repeat;
        }
    }
}
.green-box {
    border: .0625rem solid #00c9a3!important;
    background: #e5f9f6!important;
    div {
        background-image: url(@/assets/img/percision/right-check.png) !important;
        background-size: 100% 100% !important;
        background-repeat: no-repeat !important;
        background-position: center !important;
        width: 1rem !important;
        height: 1rem !important;
        border-radius: .125rem !important;
        margin-right: .625rem !important;
        display: block !important;
        border: none !important;
    }
}
.red-box {
    border: .0625rem solid #dd2a2a!important;
    background: #fce9e9!important;
    div {
        background-image: url(@/assets/img/percision/wrong-check.png) !important;
        background-size: 100% 100% !important;
        background-repeat: no-repeat !important;
        background-position: center !important;
        width: 1rem !important;
        height: 1rem !important;
        border-radius: .125rem !important;
        margin-right: .625rem !important;
        display: block !important;
        border: none !important;
    }
}
.yellow-box {
    border: .0625rem solid #f1be21!important;
    background: #fef8e8!important;
    div {
        background-image: url(@/assets/img/percision/harf-right.png) !important;
        background-size: 100% 100% !important;
        background-repeat: no-repeat !important;
        background-position: center !important;
        width: 1rem !important;
        height: 1rem !important;
        border-radius: .125rem !important;
        margin-right: .625rem !important;
        display: block !important;
        border: none !important;
    }
}
.answers {
    display: flex;
    /* margin-top: 1.25rem; */
    margin-left: auto;
    float: right;
    gap: 10px;
    /* justify-content: center; */
    /* margin-top: 15px; */
    
    .answer-box {
        width: 6.25rem;
        height: 3.125rem;
        border-radius: .25rem;
        cursor: pointer;
        border: .0625rem solid #dddddd;
        background: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 0;
        transition: all 0.2s ease;
        
        &:first-child {
            margin-left: 0;
        }
        
        &:hover {
            border-color: #3b82f6;
            background: #f0f9ff;
        }
        
        &.selected {
            /* border-color: #059669; */
            background: #ecfdf5;
            color: #065f46;
        }
        
        div {
            border: .0625rem solid #999999;
            width: 1rem;
            height: 1rem;
            border-radius: .125rem;
            margin-right: .625rem;
            transition: all 0.2s ease;
        }
        
        &.selected div {
            background: #059669;
            border-color: #059669;
        }
    }
}

/* 🎯 新增：主观题批改模式样式 */
/* .subjective-correction-mode 样式已移除，使用默认样式 */

/* .student-answer-section, .correction-section 样式已移除，使用默认样式 */

.section-title {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
    font-weight: 600;
    color: #374151;
    gap: 10px;
}

.image-count {
    font-size: 14px;
    color: #6b7280;
    font-weight: 400;
}

.current-result {
    font-size: 14px;
    color: #059669;
    font-weight: 500;
}

.answer-img-box {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    padding: 10px;
    
    .answer-img {
        width: 144px;
        height: 144px;
        border-radius: 4px;
        object-fit: cover;
        border: 1px solid #e5e7eb;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        transition: transform 0.2s ease;
        
        &:hover {
            transform: scale(1.02);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
        }
    }
}

.no-answer {
    text-align: center;
    color: #9ca3af;
    font-style: italic;
    padding: 20px;
}

/* .normal-answer-mode {
    正常答题模式的样式
} */
.black-text {
    color: #2a2b2a;
    font-size: 1rem;
    font-weight: 400;
    text-align: center;
}
.grey-text {
    color: #999999;
    font-size: .875rem;
    font-weight: 400;
    text-align: center;
}

.dialog-footer {
    margin-top: 9.375rem;
    display: flex;
    justify-content: center;
}
.blue-btn {
    width: 7.625rem;
    height: 2.375rem;
    line-height: 2.375rem;
    cursor: pointer;
    border-radius: 1.1875rem;
    background: #00c9a3;
    color: #ffffff;
    text-align: center;
}
.gery-text {
    text-align: center;
    color: #999999;

}
</style>
<style lang="scss">
.answer-item:not(:last-child) {
    margin-bottom: 1.875rem;
}

.dialog-correct {
    width: 34.875rem!important;
    border-radius: 1.25rem;
    box-sizing: border-box;
}

/* 🎯 新增：报告弹窗样式 */
.data-box {
    display: flex;
    padding-left: 2.5rem;
    margin-bottom: 1.875rem;
    &-item {
        padding: .475rem 1rem;
        color: #2a2b2a;
        font-size: .875rem;
        font-weight: 400;
        border-radius: 1rem;
        background: #ffffff;
        margin-right: .625rem;
        display: flex;
        align-items: center;
        img {
            width: 1rem;
            height: 1rem;
        }
        span {
            font-size: 14px;
            font-weight: 700;
        }
    }
}

.item-list{
    width: 908px;
    margin: 0 auto;
    .list{
        display: flex;
        background: rgba(246, 248, 255, 1);
        margin-bottom: 10px;
        line-height: 47px;
        padding: 0 6px;
        border: 2px solid rgba(90, 133, 236, 1);
        font-size: 14px;
        align-items: center;
        border-radius: 10px;
        .name{
            color: rgba(90, 133, 236, 1);
            font-weight: 700;
            width: 35%;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        .accuracy{
            margin-left: 20px;
            font-size: 14px;
            color: rgba(42, 43, 42, 1);
        }
        .pass{
            margin-left: 30px;
            width: 60px;
            height: 31px;
        }
        .learn{
            margin-left: 30px;
            align-items: center;
            width: 96px;
            height: 29px;
            line-height: 29px;
            background: rgba(233, 139, 0, 1);
            border-radius: 14px;
            display: flex;
            cursor: pointer;
            img{
                width: 16px;
                height: 16px;
                margin-left: 9px;
                margin-right: 3px;
            }
            span{
                color: rgba(255, 255, 255, 1);
                font-size: 14px;
                line-height: 29px;
                display: block;
            }
        }
        .learns{
            margin-left: auto;
            align-items: center;
            display: flex;
            img{
                width: 36px;
                height: 30px;
            }
        }
    }
}

.center-flex {
    width: 908px;
    max-height: 438px;
    overflow-x: auto;
    overflow-y: auto;
    
    &::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }
    
    &::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 4px;
    }
    
    &::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 4px;
        
        &:hover {
            background: #a8a8a8;
        }
    }
    
    scrollbar-width: thin;
    scrollbar-color: #c1c1c1 #f1f1f1;
}

.handle-table {
    border: 1px solid #D1DAEF;
    border-right: none;
    min-width: 906px;
    width: max-content;
    .first-row {
        display: flex;
        min-width: max-content;
        div {
            border-right: 1px solid #D1DAEF;
            border-bottom: 1px solid #D1DAEF;
        }
        &-item {
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 128px;
            width: 128px;
            height: 73px;
            background: #f6f8ff;
            color: #2a2b2a;
            font-size: 16px;
            font-weight: 400;
            flex-shrink: 0;
            flex: 1;
        }
    }
    .second-row {
        display: flex;
        min-width: max-content;
        div {
            border-right: 1px solid #D1DAEF;
        }
        &-item {
            display: flex;
            align-items: center;
            justify-content: center;
            min-width:128px;
            width: 128px;
            height: 73px;
            background: #E3EEFF;
            color: #2a2b2a;
            font-size: 16px;
            font-weight: 400;
            flex-shrink: 0;
            flex: 1;
        }
    }
    .blue-item {
        color: #275dde;
        background: #B8D4FF;
        font-size: 16px;
        font-weight: 700;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    .status-icon{
        color: #275dde;
        background: rgba(246, 248, 255, 1);
        font-size: 16px;
        font-weight: 700;
        display: flex;
        align-items: center;
        justify-content: center;
        &.status-correct {
            color: rgba(0, 156, 127, 1) !important;
        }
        &.status-wrong {
            color: rgba(221, 42, 42, 1) !important;
        }
    }
}

.gif-box {
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    .success-gif {
        width: 600px;
        height: 342px;
    }
    .text-png {
        width: 411px;
        height: 26px;
    }
    .fail-gif {
        width: 300px;
        height: 300px;
    }
    .fail-text-png {
        width: 100px;
        height: 26px;
    }
}

.jf_num {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    margin-top: 80px;
    top: -50px;
    opacity: 0;
    width: 76px;
    height: 26px;
    border-radius: 13px;
    background: #00000066;
    color: #ffc000;
    font-size: 16px;
    font-weight: 700;
    img {
        width: 20px;
        height: 20px;
        margin-right: 8px;
    }
}

@keyframes showup {
    0% {
      top: 0;
      opacity: 0;
    }

    50% {
      top: -50px;
      opacity: 1;
    }

    100% {
      top: -50px;
      opacity: 1;
    }
}

.showup {
    -webkit-animation: showup 4s;
}
</style>

<style lang="scss">
.weekness-dialog2 {
    max-width: none;
    max-height: none;
    margin: 0 auto;
    padding: 0;
    background-color: rgba(0, 0, 0, 0) !important;
    background-image: url(@/assets/img/percision/training/tzbgs.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center center;
    border: none;
    box-shadow: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 1090px;
    height: 660px;
    max-width: 95vw;
    max-height: 95vh;
    z-index: 9999;
    overflow: visible;
    display: flex;
    align-items: center;
    justify-content: center;
    .close-icon {
        position: absolute;
        width: 3.125rem;
        height: 3.125rem;
        left: 50%;
        bottom: -64px;
        transform: translateX(-50%);
        cursor: pointer;
        z-index: 10000;
    }
    .el-dialog__header {
        display: none;
    }
    .dialog-box {
        width: 100%;
        height: 100%;
        overflow-y: auto;
        overflow-x: hidden;
        padding: 20px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin-top: 30px;
        
        .summary-text {
            text-align: center;
            font-size: 16px;
            font-weight: 600;
            color: #2a2b2a;
            margin-bottom: 20px;
            padding: 10px 20px;
            background: rgba(0, 201, 163, 0.1);
            border-radius: 8px;
            border: 1px solid rgba(0, 201, 163, 0.3);
        }
        
        .data-box {
            margin-top: 40px;
        }
    }
}

.weekness-dialog3{
    background-image: url(@/assets/img/percision/training/jcbgs.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    background-position: center center;
}

/* 🎯 新增：一题一页模式样式 */
.single-question-container {
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    margin: 20px 0;
    overflow: hidden;
}

.question-cont {
    padding: 30px;
}

.question-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f0f0f0;
}

.question-type {
    background: #5a85ec;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
}

.question-number {
    font-size: 18px;
    font-weight: 700;
    color: #2a2b2a;
    margin-right: 15px;
    min-width: 30px;
}

.question-content {
    flex: 1;
    font-size: 16px;
    line-height: 1.6;
    color: #333;
}

.question-options {
    margin: 20px 0;
}

.option-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.option-item:hover {
    background: #e9ecef;
}

.option-label {
    font-weight: 600;
    color: #5a85ec;
    margin-right: 15px;
    min-width: 30px;
}

.option-content {
    flex: 1;
    font-size: 15px;
    line-height: 1.5;
    color: #333;
}

.answer-section {
    padding: 30px;
    background: #f8f9fa;
    border-top: 1px solid #e0e0e0;
}

.choice-answer-area {
    margin-bottom: 20px;
}

.answer-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.answer-option {
    display: flex;
    align-items: center;
    padding: 15px;
    background: #fff;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.answer-option:hover {
    border-color: #5a85ec;
    background: #f0f4ff;
}

.answer-option.selected {
    border-color: #5a85ec;
    background: #e3f2fd;
}

.answer-option.disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.option-checkbox {
    width: 20px;
    height: 20px;
    border: 2px solid #ddd;
    border-radius: 4px;
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.answer-option.selected .option-checkbox {
    background: #5a85ec;
    border-color: #5a85ec;
}

.checkmark {
    color: white;
    font-size: 12px;
    font-weight: bold;
}

.option-letter {
    font-weight: 600;
    color: #5a85ec;
    font-size: 16px;
}

.non-choice-answer-area {
    margin-top: 20px;
}

.upload-images {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.upload-image-item {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.answer-img {
    width: 100%;
    height: 150px;
    object-fit: cover;
}

.question-footer {
    padding: 20px 30px;
    background: #fff;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: center;
}

.navigation-buttons {
    display: flex;
    gap: 15px;
}

.empty-container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    background: #fff;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    margin: 20px 0;
}

.empty-content {
    text-align: center;
}

.empty-icon {
    font-size: 64px;
    color: #ccc;
    margin-bottom: 20px;
}

.empty-text {
    font-size: 16px;
    color: #666;
    margin-bottom: 20px;
}

.show-analyse {
    margin: 20px 0;
    padding: 15px;
    background: #f0f4ff;
    border-radius: 8px;
    border-left: 4px solid #5a85ec;
}

.analyse {
    margin-top: 15px;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.flex-sty {
    display: flex;
    margin-bottom: 15px;
    align-items: flex-start;
}

.flex-sty span {
    font-weight: 600;
    color: #5a85ec;
    min-width: 60px;
    margin-right: 10px;
}

.flex-sty div {
    flex: 1;
    line-height: 1.6;
    color: #333;
}
</style>
