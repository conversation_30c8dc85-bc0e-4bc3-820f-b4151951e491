/** 统一处理 Cookie */
import <PERSON><PERSON><PERSON><PERSON> from "@/utils/cache/cacheKey"
import Cookies from "js-cookie"


export const getToken = () => {
  return Cookies.get(CacheKey.TOKEN)
}
export const setToken = (token: string): Promise<void> => {
  return new Promise((resolve) => {
    Cookies.set(CacheKey.TOKEN, token, { expires: 1 }) // 有效期为1天
    resolve() // 表示操作完成
  })
  // Cookies.set(CacheKey.TOKEN, token, { expires: 1 }) //有效期为1天
}
export const removeToken = () => {
  Cookies.remove(CacheKey.TOKEN)
}
