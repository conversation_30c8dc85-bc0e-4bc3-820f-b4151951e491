<template>
    <div class="step-box">
        <div class="step-box-item" v-for="item in stepList">
            <div v-if="item.id != 1" class="step-box-item-line" :class="item.id > step?'grey-bg':''"></div>
            <div class="step-box-item-icon">
                <img :src="getUrl(item)" alt=""></img>
                <div :class="item.id > step?'white-bg':''"> {{ item.id }}.{{ item.name }}</div>
            </div>
        </div>
        
    </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, watch } from 'vue';
import { getStudyStepApi } from '@/api/book'
const props = defineProps({
    sourceId: {
        type: String,
        default: ''
    },
    // 1.章节，2.知识点
    type: {
        type: Number,
        default: 1
    },
    update: {
        type: Boolean,
        default: false
    }
})
const emit = defineEmits(['sendStep'])
const getStep = async() => {
    await getStudyStepApi({ sourceId: props.sourceId, type: props.type, update: props.update }).then((res: any) => {
        if (res.code == 200) {
            step.value = Number(res.data.key)
            emit('sendStep', step.value)
        }
    }).catch((error)=>{
        console.log(error)
    })
}
watch(
    ()=> props.sourceId, 
    (newVal, oldVal) => {
        if(newVal) {
            // console.log("进来了") 
            getStep()
        }
    },
    {immediate: true}
)
const step = ref(1)
const stepList = ref([
    {id: 1, name: '弱项检测', icon: 'step-1'},
    {id: 2, name: '针对学习', icon: 'step-2'},
    {id: 3, name: '学后检测', icon: 'step-3'},
    {id: 4, name: '阶段测试', icon: 'step-4'},
    {id: 5, name: '错题消化', icon: 'step-5'}
])

const getUrl = (item: any ) => {
    let url = item.icon
    if (item.id <= step.value) {
        url = url + '-color'
    }
    return new URL(`../../../assets/img/percision/${url}.png`, import.meta.url).href
}

// onMounted(() => {
//     console.log("第一次")
//     getStep()
// })

</script>
<style lang="scss" scoped>
.step-box-item-line {
    width: 1.0625rem;
    height: 6.5625rem;
    background: #2cdac4;
}
.step-box-item-icon {
    position: relative;
    img {
        position: absolute;
        left: -1.1875rem;
        top: -0.9375rem;
        width: 3.4375rem;
        height: 3.4375rem;
    }
    div {
        width: 7rem;
        height: 1.6875rem;
        line-height: 1.6875rem;
        border-radius: .25rem;
        padding-left: 2.25rem;
        font-size: .875rem;
        box-sizing: border-box;
        color: #F5F5F5;
        background: linear-gradient(150.8deg, #36e2c2 0%, #00b7d0 100%);
    }
}
.grey-bg {
    background-color: #E1E1E1;
}
.white-bg {
    background: #f5f5f5!important;
    color: #2a2b2a!important;
}
</style>