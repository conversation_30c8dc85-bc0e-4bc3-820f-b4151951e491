<template>
    <div class="mask-box" v-if="dialogVisible">
        <div class="cont-box">
            <div id="lottie_pass" class="canvas" ref="containerRef"></div>
            <img class="btn" @click="dialogVisible = false" src="@/assets/img/percision/pass_btn.png"></img>
        </div>
    </div>
</template>
<script setup lang="ts">
import animationDataF from '@/utils/lottie/final_pass.json'
import animationDataO from '@/utils/lottie/olympiad_pass.json'
import { nextTick, ref } from 'vue';
import lottie from 'lottie-web';
const dialogVisible = ref(false);
const containerRef = ref<HTMLDivElement | null>(null)
const props = defineProps({
  isFinal: {
    type: Boolean,
    default: true
  }
})
const init = () => {
    dialogVisible.value = true
    nextTick(() => {
        const container = containerRef.value
        if (container) {
            let animations = lottie.loadAnimation({
                container: container,
                renderer: 'svg',
                loop: false,
                autoplay: true,
                animationData: props.isFinal?animationDataF:animationDataO
            });
            animations.play()
        }
    })
}
defineExpose({
    init
})
</script>
<style lang="scss" scoped>
.mask-box {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    z-index: 100;
    background: rgba(0, 0, 0, 0.5);
    opacity: 1;
    display: flex;
    justify-content: center;
    align-items: center;
}
@keyframes showup {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}
.cont-box {
    position: fixed;
    z-index: 10;
    width: 700px;
    top: calc(50% - 350px);
    left: calc(50% - 360px);
    .btn {
        position: absolute;
        cursor: pointer;
        bottom: 130px;
        left: 233px;
        width: 236px;
        height: 62px;
        -webkit-animation: showup 3s;
    }
}
</style>